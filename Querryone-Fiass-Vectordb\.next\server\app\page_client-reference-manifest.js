globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/client/image-component.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_next_dist_ea69ac._.js","static/chunks/app_not-found_tsx_703f02._.js","static/chunks/app_not-found_tsx_ab4b51._.js"],"async":false},"[project]/app/sign-in/page.tsx <module evaluation>":{"id":"[project]/app/sign-in/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_next_84c963._.js","static/chunks/_c3a0b0._.js","static/chunks/app_page_tsx_ab4b51._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/app/providers.tsx <module evaluation>":{"id":"[project]/app/providers.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js"],"async":false},"[project]/node_modules/next/dist/client/image-component.js":{"id":"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_next_dist_ea69ac._.js","static/chunks/app_not-found_tsx_703f02._.js","static/chunks/app_not-found_tsx_ab4b51._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/app/sign-in/page.tsx":{"id":"[project]/app/sign-in/page.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_next_84c963._.js","static/chunks/_c3a0b0._.js","static/chunks/app_page_tsx_ab4b51._.js"],"async":false},"[project]/app/providers.tsx":{"id":"[project]/app/providers.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/app/providers.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/providers.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__1adf9f._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/image-component.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__1adf9f._.js","server/chunks/ssr/_29cb45._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_36e229._.js","server/chunks/ssr/[root of the server]__d4a2ae._.js"],"async":false}},"[project]/app/sign-in/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/sign-in/page.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__1adf9f._.js","server/chunks/ssr/node_modules_a01d1b._.js","server/chunks/ssr/[root of the server]__fc927a._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/lib/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/lib/metadata/metadata-boundary.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/image-component.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/image-component.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/app/providers.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/providers.tsx (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/app/sign-in/page.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/app/sign-in/page.tsx (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/app/page":[{"path":"static/chunks/[root of the server]__6509f0._.css","inlined":false}],"[project]/app/not-found":[{"path":"static/chunks/[root of the server]__6509f0._.css","inlined":false}],"[project]/app/layout":[{"path":"static/chunks/[root of the server]__6509f0._.css","inlined":false}],"[project]/app/favicon.ico":[]},"entryJSFiles":{"[project]/app/page":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_next_84c963._.js","static/chunks/_c3a0b0._.js","static/chunks/app_page_tsx_ab4b51._.js"],"[project]/app/favicon.ico":["static/chunks/_22d2ba._.js","static/chunks/app_favicon_ico_mjs_53e3fe._.js"],"[project]/app/not-found":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js","static/chunks/node_modules_next_dist_ea69ac._.js","static/chunks/app_not-found_tsx_703f02._.js","static/chunks/app_not-found_tsx_ab4b51._.js"],"[project]/app/layout":["static/chunks/_a9db51._.js","static/chunks/app_layout_tsx_61af54._.js"]}}
