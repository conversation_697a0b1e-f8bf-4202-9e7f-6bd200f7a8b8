"use client";
import Footer from "@/components/Footer";
import Header from "@/components/Header";
import MainSidebar from "@/components/MainSidebar";
import MainModal from "@/components/modals/MainModal";
import GradientBackground from "@/components/ui/GradientBackground";
import ClientOnly from "@/components/ui/ClientOnly";
import HydrationBoundary from "@/components/ui/HydrationBoundary";
import { useChatHandler } from "@/stores/chatList";
import React, { useEffect, useState } from "react";

function Layout({ children }: { children: React.ReactNode }) {
  const [showSidebar, setShowSidebar] = useState(false);
  const { updateChatList } = useChatHandler();

  useEffect(() => {
    // Only run on client side to prevent hydration errors
    if (typeof window !== 'undefined') {
      updateChatList();
    }
  }, [updateChatList]);

  return (
    <HydrationBoundary>
      <ClientOnly fallback={
        <div className="text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30">
          <div className="flex justify-center items-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primaryColor"></div>
          </div>
        </div>
      }>
        <div className="text-n500 bg-white relative z-10 h-dvh overflow-hidden dark:bg-n0 dark:text-n30">
          <GradientBackground />
          <div className="flex justify-start items-start h-full">
            <MainSidebar
              showSidebar={showSidebar}
              setShowSidebar={setShowSidebar}
            />
            <div className="flex-1 flex flex-col gap-2 sm:gap-3 justify-between items-center h-full pb-2 sm:pb-3 relative z-20 w-full overflow-hidden">
              <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />
              <div className="w-full flex-1 overflow-auto flex flex-col">
                {children}
              </div>
              <Footer />
            </div>
          </div>

          {/* Modal */}
          <MainModal />
        </div>
      </ClientOnly>
    </HydrationBoundary>
  );
}

export default Layout;
