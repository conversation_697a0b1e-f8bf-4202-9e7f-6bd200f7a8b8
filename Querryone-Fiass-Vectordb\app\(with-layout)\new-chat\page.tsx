"use client";
import React, { useState, useEffect } from "react";
import logo from "@/public/images/favicon.ico";
import Image from "next/image";

import ChatBox from "@/components/chatComponents/ChatBox"

import { v4 as uuidv4 } from "uuid";
import { useRouter } from "next/navigation";
import { ApiService } from "@/components/chatComponents/services/ApiService";

function NewChat() {
  const router = useRouter();

  // State for FAISS index selector and fetched indexes
  const [faissIndexes, setFaissIndexes] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<string>(() => {
    // Initialize from localStorage if available
    if (typeof window !== 'undefined') {
      const savedIndex = localStorage.getItem('faiss_index_name') || localStorage.getItem('selectedFaissIndex');
      return savedIndex || '';
    }
    return '';
  });
  const [showConfirmation, setShowConfirmation] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);



  // Fetch FAISS indexes on mount
  useEffect(() => {
    const fetchFilteredFaissData = async () => {
      // Only run on client side to prevent hydration errors
      if (typeof window === 'undefined') return;

      setIsLoading(true);

      try {
        console.log("Fetching available FAISS indexes from PINE collection...");

        // Use the centralized ApiService method
        const availableIndexes = await ApiService.fetchUserIndexes();
        console.log("Available indexes for user:", availableIndexes);

        // Update the indexes state
        setFaissIndexes(availableIndexes);

        // Initialize selectedIndex from localStorage, fallback to first available or 'default'
        const savedIndex = localStorage.getItem('selectedFaissIndex') || localStorage.getItem('faiss_index_name');
        let indexToSelect = 'default';

        if (savedIndex && availableIndexes.includes(savedIndex)) {
          indexToSelect = savedIndex;
        } else if (availableIndexes.includes('default')) {
          indexToSelect = 'default';
        } else if (availableIndexes.length > 0) {
          indexToSelect = availableIndexes[0];
        }

        setSelectedIndex(indexToSelect);
        localStorage.setItem('selectedFaissIndex', indexToSelect);
        localStorage.setItem('faiss_index_name', indexToSelect);

        console.log(`Selected index: ${indexToSelect}`);
      } catch (error) {
        console.error("Error fetching indexes:", error);
        // Fallback to default configuration
        setFaissIndexes(['default']);
        setSelectedIndex('default');
        localStorage.setItem('selectedFaissIndex', 'default');
        localStorage.setItem('faiss_index_name', 'default');
      } finally {
        setIsLoading(false);
      }
    };



    fetchFilteredFaissData();
  }, []);

  // Hide confirmation message after 5 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showConfirmation) {
      timer = setTimeout(() => {
        setShowConfirmation(false);
      }, 5000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showConfirmation]);

  // Handle FAISS index selection
  const handleIndexSelect = (index: string) => {
    setSelectedIndex(index);
    setShowConfirmation(true);

    // Store the selected index in localStorage for use in other components
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedFaissIndex', index);
      localStorage.setItem('faiss_index_name', index);
    }
    console.log(`Selected FAISS index: ${index}`);
  };

  // Function to start a new chat with the selected index
  const startNewChat = () => {
    const chatId = uuidv4();
    const currentChatId = chatId;

    // Store the selected index in localStorage directly
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedFaissIndex', selectedIndex);
      localStorage.setItem('faiss_index_name', selectedIndex);
    }

    // Navigate to the chat page without creating a message
    router.push(`/chat/${currentChatId}`);
  };
  return (
    <>
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
      <div className="w-full h-full flex items-center justify-center">
        <div className="w-full max-w-[1090px] mx-auto px-6 flex flex-col">
          <div
            className={`flex flex-col justify-center items-center text-center pb-8 `}
          >
            <div className="flex justify-start items-center gap-3">
              <Image src={logo} alt="" />
              <p className="text-2xl font-semibold text-n700 dark:text-n30">
                Hello, I&apos;m QueryOne
              </p>
            </div>
            <p className="text-n700 pt-4 dark:text-n30">
              How can I make things easier for you?
            </p>
          </div>

          <ChatBox />

          {/* FAISS Index buttons styled like the second image - Positioned first below ChatBox */}
          <div className="w-full max-w-[800px] mx-auto mt-6 flex justify-center">
            <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-2 flex flex-wrap justify-center gap-2">
              {isLoading ? (
                <div className="py-2 px-4 text-gray-500 dark:text-gray-400 flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  Loading FAISS indexes...
                </div>
              ) : faissIndexes.length > 0 ? (
                faissIndexes.map((index) => (
                  <button
                    key={index}
                    onClick={() => handleIndexSelect(index)}
                    className={`py-2 px-4 rounded-lg flex items-center gap-2 transition-colors
                      ${selectedIndex === index
                        ? 'bg-white dark:bg-gray-700 shadow-sm text-blue-600 dark:text-blue-400'
                        : 'hover:bg-white dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'}`}
                  >
                    <span className="font-medium">{index}</span>
                  </button>
                ))
              ) : (
                <div className="py-2 px-4 text-gray-500 dark:text-gray-400">
                  No FAISS indexes found. Please create an index first.
                </div>
              )}
            </div>
          </div>

          {/* Confirmation message */}
          {showConfirmation && (
            <div className="w-full max-w-[800px] mx-auto mt-6">
              <div className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 text-center animate-fadeIn">
                <div className="text-green-600 dark:text-green-400 font-medium mb-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 inline-block mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  FAISS index &quot;{selectedIndex}&quot; is selected, you can now ask questions based on this!
                </div>
                <div className="flex justify-center mt-4">
                  <button
                    onClick={startNewChat}
                    className="py-2 px-6 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md transition-colors flex items-center gap-2"
                  >
                    <span>Start Chat</span>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}


        </div>
      </div>
    </>
  );
}

export default NewChat;
