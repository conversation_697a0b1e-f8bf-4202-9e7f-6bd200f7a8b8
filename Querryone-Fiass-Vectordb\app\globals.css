/* Import animation styles */
@import '../styles/typingAnimation.css';
@import '../styles/snowfall.css';
@import '../styles/fileUpload.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-color: #4a90e2;
  --primary-color-dark: #357abf;
  --primary-color-rgb: 74, 144, 226;
  --success-color: #4CAF50;
  --error-color: #F44336;
  --text-primary: #333;
  --text-secondary: #666;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-inter);
  -webkit-tap-highlight-color: transparent;
}

/* Prevent hydration-related layout shifts */
.hydration-error-fallback {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Hide elements that might cause hydration issues during initial load */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Scroll bar */
::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #a7a7a71f;
}

::-webkit-scrollbar {
  width: 6px;
  background-color: #a7a7a71f;
}

::-webkit-scrollbar-thumb {
  background-color: #4d6bfe;
}
/* For Chrome, Safari, Edge, and Opera */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* For Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

@layer components {
  /* Typography */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply m-0 p-0 !leading-[120%];
  }
  p {
    @apply !leading-[150%];
  }

  /* Main Container styles */
  .container {
    @apply mx-auto max-sm:max-w-[90%] sm:max-w-[540px] md:max-w-[720px] lg:max-w-[960px] xl:max-w-[1140px] xxl:max-w-[1296px];
  }
  .large-container {
    @apply mx-auto 4xl:max-w-[1760px];
  }

  /* stp = section top padding, sbp= section bottom padding */
  .stp-30 {
    @apply pt-16 md:pt-24 xl:pt-30;
  }
  .sbp-30 {
    @apply pb-16 md:pb-24 xl:pb-30;
  }
  .stp-15 {
    @apply pt-8 md:pt-12 xl:pt-15;
  }
  .sbp-15 {
    @apply pb-8 md:pb-12 xl:pb-15;
  }
}

/* Keyframe animation */
.circle {
  animation: circleAnimation 5s linear;
}
@keyframes circleAnimation {
  0% {
    transform: rotate(0deg) scale(1);
  }
  50% {
    transform: rotate(180deg) scale(0.9);
  }
  100% {
    transform: rotate(360deg) scale(1);
  }
}

/* Typing animation effects */
@keyframes blink-cursor {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-up {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.typing-animation-professional {
  animation: fade-in 0.3s ease-out;
}

.typing-animation-professional button {
  animation: slide-up 0.5s ease-out forwards;
}

pre {
  white-space: pre-line;
  word-wrap: break-word;
  text-align: justify;
}
