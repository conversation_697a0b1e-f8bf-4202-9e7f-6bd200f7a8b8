"use client";

import React from 'react';
import IndexMigrationTool from '@/components/admin/IndexMigrationTool';

export default function MigrateIndexesPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8">
      <div className="container mx-auto px-4">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
            Index Migration Tool
          </h1>
          <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Migrate your existing FAISS indexes to the new email-based filtering system 
            to ensure proper data isolation and security.
          </p>
        </div>
        
        <IndexMigrationTool />
      </div>
    </div>
  );
}
