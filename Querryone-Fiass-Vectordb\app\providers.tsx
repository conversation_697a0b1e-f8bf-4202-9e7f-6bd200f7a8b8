"use client";

import { ThemeProvider } from "next-themes";
import AnimationController from "@/components/chatComponents/AnimationController";
import ClientOnly from "@/components/ui/ClientOnly";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange
    >
      {/* Animation controller manages typing animations across the app */}
      <ClientOnly>
        <AnimationController />
      </ClientOnly>
      {children}
    </ThemeProvider>
  );
}
