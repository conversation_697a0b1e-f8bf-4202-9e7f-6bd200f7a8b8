import React from 'react';

interface CategoryTabProps {
  category: string;
  isActive: boolean;
  onClick: () => void;
}

const CategoryTab: React.FC<CategoryTabProps> = ({ category, isActive, onClick }) => {
  // Format category name for display (capitalize first letter)
  const displayName = category.charAt(0).toUpperCase() + category.slice(1);
  
  return (
    <button
      onClick={onClick}
      className={`px-4 py-2 text-sm font-medium rounded-full transition-colors duration-300 ${
        isActive 
          ? 'bg-primaryColor text-white' 
          : 'bg-white dark:bg-n0 text-n700 dark:text-n30 hover:bg-primaryColor/10'
      } border border-primaryColor/20`}
    >
      {displayName}
    </button>
  );
};

export default CategoryTab;
