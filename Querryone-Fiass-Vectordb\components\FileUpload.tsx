'use client';

import React, { useState, useRef } from 'react';
import { formatFileSize, isFileTypeAllowed, isFileSizeValid, createPineCollectionEntry } from '@/services/fileUploadService';
import { mockUploadFile } from '@/services/mockServer';
import { downloadSampleFile } from '@/services/fileDownloadService';

interface FileUploadProps {
  onFileUpload?: (files: File[]) => void;
  maxFileSize?: number;
  allowedTypes?: string[];
}

const FileUpload: React.FC<FileUploadProps> = ({
  onFileUpload,
  maxFileSize = 50,
  allowedTypes = ['image/jpeg', 'image/png', 'application/pdf', 'video/mp4', 'text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel']
}) => {
  const [files, setFiles] = useState<File[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [uploadStatus, setUploadStatus] = useState<Record<string, { status: string; error?: string; data?: any }>>({});
  const [downloadError, setDownloadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);

  // Handle file selection
  const handleFileSelect = (fileList: FileList | null) => {
    if (!fileList || fileList.length === 0) return;

    // Get the first file (we're only allowing one file at a time)
    const newFile = fileList[0];

    // Validate file type
    if (!isFileTypeAllowed(newFile.type, allowedTypes)) {
      alert(`File type not allowed. Please upload one of the following: ${allowedTypes.join(', ')}`);
      return;
    }

    // Validate file size
    if (!isFileSizeValid(newFile.size, maxFileSize)) {
      alert(`File size exceeds the ${maxFileSize}MB limit.`);
      return;
    }

    // If we already have a file, replace it with the new one
    setFiles([newFile]);

    // Initialize progress for the file
    setUploadProgress({
      [newFile.name]: 0
    });

    // Set status to ready
    setUploadStatus({
      [newFile.name]: { status: 'ready' }
    });

    // If onFileUpload callback is provided, call it with the valid file
    if (onFileUpload) {
      onFileUpload([newFile]);
    }
  };

  // Handle file input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
    // Reset the input value to allow selecting the same file again
    if (e.target) e.target.value = '';
  };

  // Handle click on the drop area
  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // Only set dragging to false if we're leaving the drop area (not a child element)
    if (e.currentTarget === e.target) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    // Get the dropped files
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  };

  // Upload all files
  const uploadAllFiles = () => {
    files.forEach(file => {
      if (uploadStatus[file.name]?.status === 'ready') {
        uploadFile(file);
      }
    });
  };

  // Upload file using our mock service
  const uploadFile = (file: File) => {
    // Set status to uploading
    setUploadStatus(prev => ({
      ...prev,
      [file.name]: { status: 'uploading' }
    }));

    // Simulate progress updates
    const progressInterval = setInterval(() => {
      setUploadProgress(prev => {
        const currentProgress = prev[file.name] || 0;
        // Don't go to 100% until the upload is actually complete
        const newProgress = Math.min(currentProgress + 5, 95);
        return {
          ...prev,
          [file.name]: newProgress
        };
      });
    }, 200);

    // Use our mock service for development
    mockUploadFile(file)
      .then(response => {
        // Clear the progress interval
        clearInterval(progressInterval);

        // Set progress to 100%
        setUploadProgress(prev => ({
          ...prev,
          [file.name]: 100
        }));

        // Update status to success
        setUploadStatus(prev => ({
          ...prev,
          [file.name]: { status: 'success', data: response }
        }));

        console.log('File uploaded successfully:', response);
      })
      .catch(error => {
        // Clear the progress interval
        clearInterval(progressInterval);

        // Update status to error
        setUploadStatus(prev => ({
          ...prev,
          [file.name]: { status: 'error', error: error.message }
        }));

        console.error('Error uploading file:', error);
      });

    // In a production environment, you would use the real service:
    /*
    uploadFile(file, updateProgress)
      .then(response => {
        setUploadStatus(prev => ({
          ...prev,
          [file.name]: { status: 'success', data: response }
        }));
      })
      .catch(error => {
        setUploadStatus(prev => ({
          ...prev,
          [file.name]: { status: 'error', error: error.message }
        }));
      });
    */
  };

  // Handle file removal
  const removeFile = (fileName: string) => {
    setFiles(prevFiles => prevFiles.filter(file => file.name !== fileName));
    setUploadProgress(prev => {
      const newProgress = { ...prev };
      delete newProgress[fileName];
      return newProgress;
    });
    setUploadStatus(prev => {
      const newStatus = { ...prev };
      delete newStatus[fileName];
      return newStatus;
    });
  };

  // Handle sample file download
  const handleSampleFileDownload = async (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent the click from bubbling up to the drop area
    setDownloadError(null);
    try {
      const success = await downloadSampleFile('querry.csv');
      if (!success) {
        setDownloadError('Failed to download sample file. Please try again.');
      }
    } catch (error) {
      console.error('Error downloading sample file:', error);
      setDownloadError('An error occurred while downloading the sample file.');
    }
  };

  return (
    <div className="file-upload-container">
      <div
        ref={dropAreaRef}
        className={`drop-area ${isDragging ? 'dragging' : ''} bg-primaryColor/5 border border-dashed border-primaryColor/30 rounded-xl p-8 flex justify-center items-center flex-col`}
        onClick={handleClick}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <div className="drop-icon">
          <svg width="50" height="50" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 16V8M12 8L8 12M12 8L16 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            <path d="M3 15V16C3 18.2091 4.79086 20 7 20H17C19.2091 20 21 18.2091 21 16V15" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
          </svg>
        </div>
        <p className="drop-text text-center text-lg font-medium pt-5">Drag & drop a CSV or Excel file or click to browse</p>
        <div className="file-restriction-notice mt-2 bg-primaryColor/10 px-3 py-1 rounded-full text-xs">
          <span className="restriction-icon">ⓘ</span>
          <span>Your CSV or Excel file name is taken as index in DB and your data will be stored in that index

          </span>
        </div>
        <p className="file-types text-n300 text-sm pt-2">CSV & Excel format only, up to {maxFileSize}MB</p>

        {/* Download Sample File Button */}
        <button
          type="button"
          onClick={handleSampleFileDownload}
          className="download-sample-btn mt-3 bg-white border border-primaryColor text-primaryColor hover:bg-primaryColor/5 px-4 py-2 rounded-md text-sm flex items-center transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Download Sample File
        </button>

        {/* Error message for download */}
        {downloadError && (
          <div className="download-error mt-2 text-red-500 text-xs">
            {downloadError}
          </div>
        )}

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleInputChange}
          className="file-input hidden"
          accept=".csv,text/csv"
        />
      </div>

      {files.length > 0 && (
        <div className="file-list mt-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-2 dark:text-gray-200">Selected File</h3>
          <ul className="space-y-2">
            {files.map((file, index) => (
              <li key={`${file.name}-${index}`} className="file-item bg-white dark:bg-gray-800 p-3 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="file-info flex justify-between items-center">
                  <span className="file-name font-medium dark:text-gray-200">{file.name}</span>
                  <span className="file-size text-sm text-gray-500 dark:text-gray-400">{formatFileSize(file.size)}</span>
                </div>
                <div className="file-actions flex items-center gap-2 mt-2">
                  {uploadStatus[file.name]?.status === 'ready' && (
                    <button
                      className="upload-btn bg-primaryColor text-white px-3 py-1 rounded-md text-sm hover:bg-primaryColor/90"
                      onClick={(e) => {
                        e.stopPropagation();
                        uploadFile(file);
                      }}
                    >
                      Upload
                    </button>
                  )}
                  {uploadStatus[file.name]?.status === 'uploading' && (
                    <div className="progress-container w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                      <div
                        className="progress-bar bg-primaryColor h-2.5 rounded-full"
                        style={{ width: `${uploadProgress[file.name]}%` }}
                      ></div>
                      <span className="progress-text text-xs text-gray-500 dark:text-gray-400 mt-1">{uploadProgress[file.name]}%</span>
                    </div>
                  )}
                  {uploadStatus[file.name]?.status === 'success' && (
                    <span className="success-icon text-green-500 dark:text-green-400">✓</span>
                  )}
                  {uploadStatus[file.name]?.status === 'error' && (
                    <span className="error-icon text-red-500 dark:text-red-400">✗</span>
                  )}
                  <button
                    className="remove-btn text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(file.name);
                    }}
                  >
                    ✕
                  </button>
                </div>
                {uploadStatus[file.name]?.error && (
                  <div className="error-message text-red-500 dark:text-red-400 text-sm mt-1">{uploadStatus[file.name].error}</div>
                )}
              </li>
            ))}
          </ul>
          {files.some(file => uploadStatus[file.name]?.status === 'ready') && (
            <button
              className="upload-btn upload-single-btn mt-3 bg-primaryColor text-white px-4 py-2 rounded-md text-sm hover:bg-primaryColor/90"
              onClick={uploadAllFiles}
            >
              Upload File
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default FileUpload;
