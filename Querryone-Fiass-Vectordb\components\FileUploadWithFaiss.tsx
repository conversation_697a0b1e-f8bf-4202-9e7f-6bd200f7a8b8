'use client';

import React, { useState, useRef } from 'react';
import { PiCloudArrowUp, PiFile, PiX, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PiDatabase, PiDownload } from 'react-icons/pi';
import { formatFileSize, isFileTypeAllowed, isFileSizeValid } from '@/services/fileUploadService';
import { uploadFileToFaiss } from '@/services/faissService';
import { downloadSampleFile } from '@/services/fileDownloadService';

interface FileUploadWithFaissProps {
  onFileUpload?: (files: File[]) => void;
  maxFileSize?: number;
  allowedTypes?: string[];
  selectedLanguage?: string;
  showFaissUpload?: boolean;
  indexName?: string;
  clientEmail?: string;
  updateMode?: string | null;
  embedModel?: string;
  onFaissUploadSuccess?: (response: any) => void;
  onFaissUploadError?: (error: string) => void;
}

interface FileWithStatus {
  file: File;
  status: 'selected' | 'uploading' | 'success' | 'error' | 'faiss-uploading' | 'faiss-success' | 'faiss-error';
  error?: string;
  faissResponse?: any;
  uploadProgress?: number;
  faissProgress?: number;
}

const FileUploadWithFaiss: React.FC<FileUploadWithFaissProps> = ({
  onFileUpload,
  maxFileSize = 50,
  allowedTypes = ['text/csv', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
  selectedLanguage = 'English',
  showFaissUpload = true,
  indexName = 'default',
  clientEmail = '<EMAIL>',
  updateMode = 'update',
  embedModel = 'all-MiniLM-L6-v2',
  onFaissUploadSuccess,
  onFaissUploadError
}) => {
  const [files, setFiles] = useState<FileWithStatus[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [downloadError, setDownloadError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropAreaRef = useRef<HTMLDivElement>(null);

  // Get language-specific text
  const getLanguageText = () => {
    const texts = {
      English: {
        dragDrop: 'Drag & drop a CSV or Excel file or click to browse',
        csvOnly: 'CSV & Excel format only, up to',
        downloadSample: 'Download Sample File',
        selectedFile: 'Selected File',
        upload: 'Upload',
        uploadToFaiss: 'Upload to FAISS',
        uploading: 'Uploading...',
        uploadingToFaiss: 'Uploading to FAISS...',
        success: 'Success',
        error: 'Error',
        remove: 'Remove',
        indexInfo: 'Your CSV or Excel file name is taken as index in DB and your data will be stored in that index'
      },
      Tamil: {
        dragDrop: 'CSV அல்லது Excel கோப்பை இழுத்து விடவும் அல்லது உலாவ கிளிக் செய்யவும்',
        csvOnly: 'CSV & Excel வடிவம் மட்டும், வரை',
        downloadSample: 'மாதிரி கோப்பை பதிவிறக்கவும்',
        selectedFile: 'தேர்ந்தெடுக்கப்பட்ட கோப்பு',
        upload: 'பதிவேற்று',
        uploadToFaiss: 'FAISS இல் பதிவேற்று',
        uploading: 'பதிவேற்றுகிறது...',
        uploadingToFaiss: 'FAISS இல் பதிவேற்றுகிறது...',
        success: 'வெற்றி',
        error: 'பிழை',
        remove: 'அகற்று',
        indexInfo: 'உங்கள் CSV அல்லது Excel கோப்பு பெயர் DB இல் குறியீடாக எடுக்கப்பட்டு உங்கள் தரவு அந்த குறியீட்டில் சேமிக்கப்படும்'
      },
      Telugu: {
        dragDrop: 'CSV లేదా Excel ఫైల్‌ను లాగి వదలండి లేదా బ్రౌజ్ చేయడానికి క్లిక్ చేయండి',
        csvOnly: 'CSV & Excel ఫార్మాట్ మాత్రమే, వరకు',
        downloadSample: 'నమూనా ఫైల్ డౌన్‌లోడ్ చేయండి',
        selectedFile: 'ఎంచుకున్న ఫైల్',
        upload: 'అప్‌లోడ్',
        uploadToFaiss: 'FAISS కు అప్‌లోడ్',
        uploading: 'అప్‌లోడ్ చేస్తోంది...',
        uploadingToFaiss: 'FAISS కు అప్‌లోడ్ చేస్తోంది...',
        success: 'విజయం',
        error: 'లోపం',
        remove: 'తొలగించు',
        indexInfo: 'మీ CSV లేదా Excel ఫైల్ పేరు DB లో ఇండెక్స్‌గా తీసుకోబడుతుంది మరియు మీ డేటా ఆ ఇండెక్స్‌లో నిల్వ చేయబడుతుంది'
      },
      Kannada: {
        dragDrop: 'CSV ಅಥವಾ Excel ಫೈಲ್ ಅನ್ನು ಎಳೆದು ಬಿಡಿ ಅಥವಾ ಬ್ರೌಸ್ ಮಾಡಲು ಕ್ಲಿಕ್ ಮಾಡಿ',
        csvOnly: 'CSV & Excel ಸ್ವರೂಪ ಮಾತ್ರ, ವರೆಗೆ',
        downloadSample: 'ಮಾದರಿ ಫೈಲ್ ಡೌನ್‌ಲೋಡ್ ಮಾಡಿ',
        selectedFile: 'ಆಯ್ಕೆಮಾಡಿದ ಫೈಲ್',
        upload: 'ಅಪ್‌ಲೋಡ್',
        uploadToFaiss: 'FAISS ಗೆ ಅಪ್‌ಲೋಡ್',
        uploading: 'ಅಪ್‌ಲೋಡ್ ಮಾಡುತ್ತಿದೆ...',
        uploadingToFaiss: 'FAISS ಗೆ ಅಪ್‌ಲೋಡ್ ಮಾಡುತ್ತಿದೆ...',
        success: 'ಯಶಸ್ಸು',
        error: 'ದೋಷ',
        remove: 'ತೆಗೆದುಹಾಕಿ',
        indexInfo: 'ನಿಮ್ಮ CSV ಅಥವಾ Excel ಫೈಲ್ ಹೆಸರನ್ನು DB ಯಲ್ಲಿ ಸೂಚ್ಯಂಕವಾಗಿ ತೆಗೆದುಕೊಳ್ಳಲಾಗುತ್ತದೆ ಮತ್ತು ನಿಮ್ಮ ಡೇಟಾವನ್ನು ಆ ಸೂಚ್ಯಂಕದಲ್ಲಿ ಸಂಗ್ರಹಿಸಲಾಗುತ್ತದೆ'
      }
    };
    return texts[selectedLanguage as keyof typeof texts] || texts.English;
  };

  // Handle file selection
  const handleFileSelect = (fileList: FileList | null) => {
    if (!fileList || fileList.length === 0) return;

    const newFile = fileList[0];

    // Validate file type
    const fileName = newFile.name.toLowerCase();
    const isCSV = newFile.type === 'text/csv' || fileName.endsWith('.csv');
    const isExcel = newFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                   newFile.type === 'application/vnd.ms-excel' ||
                   fileName.endsWith('.xlsx') ||
                   fileName.endsWith('.xls');

    if (!isCSV && !isExcel) {
      alert(`File type not allowed. Please upload a CSV or Excel file.`);
      return;
    }

    // Validate file size
    if (!isFileSizeValid(newFile.size, maxFileSize)) {
      alert(`File size exceeds the ${maxFileSize}MB limit.`);
      return;
    }

    // Create file with status
    const fileWithStatus: FileWithStatus = {
      file: newFile,
      status: 'selected',
      uploadProgress: 0,
      faissProgress: 0
    };

    setFiles([fileWithStatus]);

    // Call onFileUpload callback if provided
    if (onFileUpload) {
      onFileUpload([newFile]);
    }
  };

  // Handle file input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
    if (e.target) e.target.value = '';
  };

  // Handle click on the drop area
  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // Handle drag events
  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.currentTarget === e.target) {
      setIsDragging(false);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(true);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);
    const droppedFiles = e.dataTransfer.files;
    handleFileSelect(droppedFiles);
  };

  // Upload file to FAISS
  const uploadToFaiss = async (fileWithStatus: FileWithStatus) => {
    const { file } = fileWithStatus;
    
    // Update status to uploading
    setFiles(prevFiles => 
      prevFiles.map(f => 
        f.file.name === file.name 
          ? { ...f, status: 'faiss-uploading', faissProgress: 0 }
          : f
      )
    );

    try {
      const response = await uploadFileToFaiss(
        file,
        indexName,
        clientEmail,
        updateMode || 'update',
        embedModel,
        undefined,
        (progress) => {
          // Update progress
          setFiles(prevFiles =>
            prevFiles.map(f =>
              f.file.name === file.name
                ? { ...f, faissProgress: progress }
                : f
            )
          );
        }
      );

      // Update status to success
      setFiles(prevFiles => 
        prevFiles.map(f => 
          f.file.name === file.name 
            ? { 
                ...f, 
                status: 'faiss-success', 
                faissResponse: response,
                faissProgress: 100 
              }
            : f
        )
      );

      if (onFaissUploadSuccess) {
        onFaissUploadSuccess(response);
      }

    } catch (error) {
      // Update status to error
      setFiles(prevFiles => 
        prevFiles.map(f => 
          f.file.name === file.name 
            ? { 
                ...f, 
                status: 'faiss-error', 
                error: error instanceof Error ? error.message : 'Upload failed'
              }
            : f
        )
      );

      if (onFaissUploadError) {
        onFaissUploadError(error instanceof Error ? error.message : 'Upload failed');
      }
    }
  };

  // Handle file removal
  const removeFile = (fileName: string) => {
    setFiles(prevFiles => prevFiles.filter(file => file.file.name !== fileName));
  };

  // Handle sample file download
  const handleSampleFileDownload = async (e: React.MouseEvent) => {
    e.stopPropagation();
    setDownloadError(null);
    try {
      const success = await downloadSampleFile('querry.csv');
      if (!success) {
        setDownloadError('Failed to download sample file. Please try again.');
      }
    } catch (error) {
      console.error('Error downloading sample file:', error);
      setDownloadError('An error occurred while downloading the sample file.');
    }
  };

  const text = getLanguageText();

  return (
    <div className="file-upload-container">
      <div
        ref={dropAreaRef}
        className={`drop-area ${isDragging ? 'dragging' : ''} bg-primaryColor/5 border border-dashed border-primaryColor/30 rounded-xl p-8 flex justify-center items-center flex-col transition-all duration-200 hover:bg-primaryColor/10 cursor-pointer`}
        onClick={handleClick}
        onDragEnter={handleDragEnter}
        onDragLeave={handleDragLeave}
        onDragOver={handleDragOver}
        onDrop={handleDrop}
      >
        <div className="drop-icon text-primaryColor">
          <PiCloudArrowUp size={50} />
        </div>
        <p className="drop-text text-center text-lg font-medium pt-5 text-gray-700 dark:text-gray-300">
          {text.dragDrop}
        </p>
        <div className="file-restriction-notice mt-2 bg-primaryColor/10 px-3 py-1 rounded-full text-xs">
          <span className="restriction-icon">ⓘ</span>
          <span className="ml-1">{text.indexInfo}</span>
        </div>
        <p className="file-types text-gray-500 dark:text-gray-400 text-sm pt-2">
          CSV & Excel files only • Max {maxFileSize}MB
        </p>

        {/* Download Sample File Button */}
        <button
          type="button"
          onClick={handleSampleFileDownload}
          className="download-sample-btn mt-3 bg-white border border-primaryColor text-primaryColor hover:bg-primaryColor/5 px-4 py-2 rounded-md text-sm flex items-center transition-colors"
        >
          <PiDownload className="h-4 w-4 mr-2" />
          {text.downloadSample}
        </button>

        {/* Error message for download */}
        {downloadError && (
          <div className="download-error mt-2 text-red-500 text-xs">
            {downloadError}
          </div>
        )}

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleInputChange}
          className="file-input hidden"
          accept=".csv,.xlsx,.xls,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        />
      </div>

      {files.length > 0 && (
        <div className="file-list mt-4 bg-gray-50 dark:bg-gray-900 p-4 rounded-lg">
          <h3 className="text-lg font-medium mb-2 dark:text-gray-200">{text.selectedFile}</h3>
          <ul className="space-y-2">
            {files.map((fileWithStatus, index) => (
              <li key={`${fileWithStatus.file.name}-${index}`} className="file-item bg-white dark:bg-gray-800 p-4 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <div className="file-info flex justify-between items-center mb-3">
                  <div className="flex items-center gap-2">
                    <PiFile className="text-primaryColor" size={20} />
                    <span className="file-name font-medium dark:text-gray-200">{fileWithStatus.file.name}</span>
                  </div>
                  <span className="file-size text-sm text-gray-500 dark:text-gray-400">
                    {formatFileSize(fileWithStatus.file.size)}
                  </span>
                </div>

                {/* FAISS Upload Button and Status */}
                {showFaissUpload && (
                  <div className="faiss-upload-section">
                    {fileWithStatus.status === 'selected' && (
                      <button
                        className="upload-to-faiss-btn bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 flex items-center gap-2 transition-colors"
                        onClick={(e) => {
                          e.stopPropagation();
                          uploadToFaiss(fileWithStatus);
                        }}
                      >
                        <PiDatabase size={16} />
                        {text.uploadToFaiss}
                      </button>
                    )}

                    {fileWithStatus.status === 'faiss-uploading' && (
                      <div className="faiss-upload-progress">
                        <div className="flex items-center gap-2 mb-2">
                          <PiSpinner className="animate-spin text-blue-600" size={16} />
                          <span className="text-sm text-blue-600">{text.uploadingToFaiss}</span>
                        </div>
                        <div className="progress-container w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                          <div
                            className="progress-bar bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                            style={{ width: `${fileWithStatus.faissProgress || 0}%` }}
                          ></div>
                        </div>
                        <span className="progress-text text-xs text-gray-500 dark:text-gray-400 mt-1">
                          {fileWithStatus.faissProgress || 0}%
                        </span>
                      </div>
                    )}

                    {fileWithStatus.status === 'faiss-success' && (
                      <div className="faiss-success flex items-center gap-2 text-green-600">
                        <PiCheck size={16} />
                        <span className="text-sm">{text.success} - Uploaded to FAISS</span>
                      </div>
                    )}

                    {fileWithStatus.status === 'faiss-error' && (
                      <div className="faiss-error">
                        <div className="flex items-center gap-2 text-red-600 mb-1">
                          <PiX size={16} />
                          <span className="text-sm">{text.error}</span>
                        </div>
                        {fileWithStatus.error && (
                          <div className="error-message text-red-500 text-xs">
                            {fileWithStatus.error}
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {/* Remove button */}
                <div className="file-actions flex justify-end mt-3">
                  <button
                    className="remove-btn text-gray-500 dark:text-gray-400 hover:text-red-500 dark:hover:text-red-400 p-1"
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFile(fileWithStatus.file.name);
                    }}
                  >
                    <PiX size={16} />
                  </button>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default FileUploadWithFaiss;
