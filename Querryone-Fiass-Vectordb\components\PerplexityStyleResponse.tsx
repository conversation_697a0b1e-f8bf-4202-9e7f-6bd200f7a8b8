import React from 'react';

interface PerplexityStyleResponseProps {
  response: string;
}

const PerplexityStyleResponse: React.FC<PerplexityStyleResponseProps> = ({ response }) => {
  const formatResponse = (text: string) => {
    if (!text) return null;

    // Split by double hash (##) for line breaks
    const sections = text.split('###').filter(section => section.trim());

    return sections.map((section, index) => {
      // Handle bullet points (lines starting with - or *)
      if (section.trim().startsWith('-') || section.trim().startsWith('*')) {
        const bulletPoints = section.split('\n').filter(line => line.trim());
        return (
          <ul key={index} className="list-disc pl-5 space-y-1">
            {bulletPoints.map((point, i) => (
              <li key={`${index}-${i}`}>{point.trim().substring(1).trim()}</li>
            ))}
          </ul>
        );
      }

      // Regular paragraph
      return (
        <p key={index} className="mb-2">
          {section.trim()}
        </p>
      );
    });
  };

  return <div className="whitespace-pre-wrap">{formatResponse(response)}</div>;
};

export default PerplexityStyleResponse;