import React, { useState } from 'react';
import ResponseDisplay from './ResponseDisplay';
import { Upload, Search, Database, AlertCircle } from 'lucide-react';

const ResponseFormattingDemo: React.FC = () => {
  const [selectedDemo, setSelectedDemo] = useState<string>('success');

  // Example responses showcasing the improved formatting
  const demoResponses = {
    success: {
      success: true,
      status: 'success' as const,
      message: 'Successfully added 1,247 vectors to index "financial-data"',
      timestamp: new Date().toISOString(),
      data: {
        index_name: 'financial-data',
        vector_count: 1247,
        total_rows_processed: 856,
        processing_time_seconds: 12.34,
        upload_id: 'upload-abc123',
        embedding_model: 'all-MiniLM-L6-v2',
        embedding_dimension: 384,
        update_mode: 'update',
        database_storage_enabled: true,
        client: '<EMAIL>'
      },
      metadata: {
        processing_stats: {
          vectors_per_second: 101.05,
          rows_to_vectors_ratio: 1.46
        },
        storage_info: {
          faiss_index_location: 'faiss_data/financial-data',
          database_table: 'financial-data',
          retrieval_endpoint: '/api/get-csv-data'
        },
        next_steps: [
          "Query your data using /api/query-faiss with index_name='financial-data'",
          "Retrieve raw CSV data using /api/get-csv-data with index_name='financial-data'",
          "Use /financial_query endpoint for AI-powered responses"
        ]
      }
    },
    
    error: {
      success: false,
      status: 'error' as const,
      message: 'Unable to process the CSV file. Please check the file format and try again.',
      timestamp: new Date().toISOString(),
      error: {
        message: 'Unable to process the CSV file. Please check the file format and try again.',
        type: 'csv_parse_error',
        technical_details: 'ParserError: Error tokenizing data. C error: Expected 5 fields in line 10, saw 7',
        details: {
          line_number: 10,
          expected_fields: 5,
          actual_fields: 7,
          supported_formats: ['.csv'],
          received_file: 'malformed_data.csv'
        }
      }
    },
    
    processing: {
      success: true,
      status: 'processing' as const,
      message: 'Processing your CSV file and creating embeddings...',
      timestamp: new Date().toISOString(),
      process_id: 'proc-xyz789',
      progress: {
        percentage: 67,
        current_step: 'Creating embeddings for batch 3/5',
        total_steps: 5
      },
      data: {
        total_rows: 1000,
        processed_rows: 670,
        current_batch: 3,
        total_batches: 5
      }
    },
    
    search_results: {
      success: true,
      status: 'success' as const,
      message: 'Found 8 relevant results for your search query',
      timestamp: new Date().toISOString(),
      data: {
        query: 'artificial intelligence trends',
        index_name: 'tech-articles',
        results: [
          {
            score: 0.92,
            text: 'AI and machine learning are transforming industries...',
            metadata: { source: 'tech-blog', date: '2024-01-15' }
          },
          {
            score: 0.87,
            text: 'The future of artificial intelligence in healthcare...',
            metadata: { source: 'medical-journal', date: '2024-01-10' }
          }
        ],
        total_results: 8,
        search_parameters: {
          top_k: 10,
          search_type: 'semantic_similarity'
        }
      },
      metadata: {
        search_stats: {
          results_found: 8,
          search_time: '< 1 second',
          index_type: 'FAISS'
        },
        result_quality: {
          highest_score: 0.92,
          lowest_score: 0.73,
          average_score: 0.84
        }
      }
    }
  };

  const demoOptions = [
    { key: 'success', label: 'Successful Upload', icon: Upload, color: 'text-green-600' },
    { key: 'error', label: 'Error Response', icon: AlertCircle, color: 'text-red-600' },
    { key: 'processing', label: 'Processing Status', icon: Database, color: 'text-blue-600' },
    { key: 'search_results', label: 'Search Results', icon: Search, color: 'text-purple-600' }
  ];

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Professional API Response Formatting
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Demonstration of improved, user-friendly API response formatting with consistent structure, 
          clear error messages, and comprehensive metadata for better user experience.
        </p>
      </div>

      {/* Demo Selection */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Select Response Type to Preview
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {demoOptions.map((option) => {
            const Icon = option.icon;
            return (
              <button
                key={option.key}
                onClick={() => setSelectedDemo(option.key)}
                className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                  selectedDemo === option.key
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                }`}
              >
                <Icon className={`w-6 h-6 mx-auto mb-2 ${option.color}`} />
                <p className="text-sm font-medium text-gray-900">
                  {option.label}
                </p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Response Preview */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Response Preview
        </h2>
        <ResponseDisplay 
          response={demoResponses[selectedDemo as keyof typeof demoResponses]} 
        />
      </div>

      {/* Implementation Guide */}
      <div className="bg-gray-50 rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Implementation Benefits
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="font-medium text-gray-900 mb-2">✅ Consistent Structure</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Standardized success/error format</li>
              <li>• Predictable field names and types</li>
              <li>• Timestamp for all responses</li>
              <li>• Status indicators (success/error/processing)</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🎯 User-Friendly Messages</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Clear, actionable error messages</li>
              <li>• Technical details hidden by default</li>
              <li>• Progress indicators for long operations</li>
              <li>• Contextual next steps and guidance</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">📊 Rich Metadata</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Performance statistics</li>
              <li>• Processing details and metrics</li>
              <li>• Related endpoints and actions</li>
              <li>• Quality indicators for results</li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900 mb-2">🔧 Developer Experience</h3>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Proper HTTP status codes</li>
              <li>• Error categorization for handling</li>
              <li>• Process IDs for tracking</li>
              <li>• Expandable technical details</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Code Example */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          Backend Implementation Example
        </h2>
        <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
{`# Using the ResponseFormatter class
from response_formatter import ResponseFormatter

# Success response
response, status_code = ResponseFormatter.success_response(
    data={"vectors": 1247, "index": "financial-data"},
    message="Successfully processed your data",
    metadata={"processing_time": 12.34}
)
return jsonify(response), status_code

# Error response  
response, status_code = ResponseFormatter.error_response(
    error="Invalid file format",
    error_type="validation_error",
    details={"supported_formats": [".csv"]}
)
return jsonify(response), status_code`}
        </pre>
      </div>
    </div>
  );
};

export default ResponseFormattingDemo;
