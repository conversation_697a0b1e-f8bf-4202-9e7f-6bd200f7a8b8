'use client';

import React, { useState } from 'react';
import { processPDFDocument, testConnection, checkBackendHealth } from '../services/uploadService';
import toast from 'react-hot-toast';

const UploadTestComponent: React.FC = () => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<string>('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      toast.error('Please select a file first');
      return;
    }

    setIsUploading(true);
    setUploadResult(null);

    try {
      console.log('🚀 Starting upload test for:', selectedFile.name);
      const result = await processPDFDocument(selectedFile, {
        index_name: 'test',
        client_email: '<EMAIL>'
      });

      console.log('📥 Upload result:', result);
      setUploadResult(result);

      if (result.success) {
        toast.success('File uploaded successfully!');
      } else {
        toast.error(result.error || 'Upload failed');
      }
    } catch (error) {
      console.error('❌ Upload error:', error);
      setUploadResult({ success: false, error: 'Upload failed' });
      toast.error('Upload failed');
    } finally {
      setIsUploading(false);
    }
  };

  const testBackendConnection = async () => {
    setConnectionStatus('Testing connection...');
    
    try {
      const result = await testConnection();
      if (result.success) {
        setConnectionStatus('✅ Backend connection successful');
        toast.success('Backend connection successful');
      } else {
        setConnectionStatus(`❌ Connection failed: ${result.error}`);
        toast.error('Connection failed');
      }
    } catch (error) {
      setConnectionStatus(`❌ Connection error: ${error}`);
      toast.error('Connection error');
    }
  };

  const checkHealth = async () => {
    setConnectionStatus('Checking backend health...');
    
    try {
      const result = await checkBackendHealth();
      if (result.success) {
        setConnectionStatus('✅ Backend is healthy');
        toast.success('Backend is healthy');
      } else {
        setConnectionStatus(`❌ Health check failed: ${result.error}`);
        toast.error('Health check failed');
      }
    } catch (error) {
      setConnectionStatus(`❌ Health check error: ${error}`);
      toast.error('Health check error');
    }
  };

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white dark:bg-gray-800 rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
        Upload Test Component
      </h2>

      {/* Connection Test */}
      <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          Backend Connection Test
        </h3>
        <div className="flex gap-3 mb-3">
          {/* <button
            onClick={testBackendConnection}
            className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
          >
            Test Connection
          </button> */}
          <button
            onClick={checkHealth}
            className="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
          >
            Check Health
          </button>
        </div>
        {connectionStatus && (
          <p className="text-sm text-gray-600 dark:text-gray-300">
            {connectionStatus}
          </p>
        )}
      </div>

      {/* File Upload Test */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
          File Upload Test
        </h3>
        
        <div className="mb-4">
          <input
            type="file"
            accept=".pdf,.doc,.docx,.txt,.rtf"
            onChange={handleFileSelect}
            className="block w-full text-sm text-gray-500 dark:text-gray-400
                     file:mr-4 file:py-2 file:px-4
                     file:rounded-lg file:border-0
                     file:text-sm file:font-semibold
                     file:bg-blue-50 file:text-blue-700
                     hover:file:bg-blue-100
                     dark:file:bg-blue-900 dark:file:text-blue-300"
          />
        </div>

        {selectedFile && (
          <div className="mb-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <p className="text-sm text-gray-700 dark:text-gray-300">
              <strong>Selected file:</strong> {selectedFile.name}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Size:</strong> {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              <strong>Type:</strong> {selectedFile.type || 'Unknown'}
            </p>
          </div>
        )}

        <button
          onClick={handleUpload}
          disabled={!selectedFile || isUploading}
          className={`px-6 py-3 rounded-lg font-semibold transition-colors ${
            !selectedFile || isUploading
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-blue-500 text-white hover:bg-blue-600'
          }`}
        >
          {isUploading ? 'Uploading...' : 'Upload File'}
        </button>
      </div>

      {/* Upload Result */}
      {uploadResult && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-3">
            Upload Result
          </h3>
          <div className={`p-4 rounded-lg ${
            uploadResult.success 
              ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
              : 'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
          }`}>
            <p className={`font-semibold ${
              uploadResult.success ? 'text-green-800 dark:text-green-200' : 'text-red-800 dark:text-red-200'
            }`}>
              {uploadResult.success ? '✅ Success' : '❌ Failed'}
            </p>
            {uploadResult.message && (
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                {uploadResult.message}
              </p>
            )}
            {uploadResult.error && (
              <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                Error: {uploadResult.error}
              </p>
            )}
            {uploadResult.upload_id && (
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Upload ID: {uploadResult.upload_id}
              </p>
            )}
            {uploadResult.index_name && (
              <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                Index: {uploadResult.index_name}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="text-sm text-gray-600 dark:text-gray-400">
        <h4 className="font-semibold mb-2">Instructions:</h4>
        <ol className="list-decimal list-inside space-y-1">
          <li>First, test the backend connection to ensure the server is running</li>
          <li>Select a PDF, DOC, DOCX, TXT, or RTF file to upload</li>
          <li>Click "Upload File" to test the upload functionality</li>
          <li>Check the result to see if the upload was successful</li>
        </ol>
      </div>
    </div>
  );
};

export default UploadTestComponent;
