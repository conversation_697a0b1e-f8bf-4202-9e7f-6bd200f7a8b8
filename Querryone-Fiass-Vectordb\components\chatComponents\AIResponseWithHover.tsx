import React from 'react';
import SentenceWithHover from './SentenceWithHover';

interface AIResponseWithHoverProps {
  text: string;
  sentenceAnalysis?: Array<{
    sentence: string;
    url: string;
    summary?: string;
  }>;
}

const AIResponseWithHover: React.FC<AIResponseWithHoverProps> = ({ 
  text, 
  sentenceAnalysis 
}) => {
  // If there's no sentence analysis data, just return the text as is
  if (!sentenceAnalysis || !Array.isArray(sentenceAnalysis) || sentenceAnalysis.length === 0) {
    return <div>{text}</div>;
  }

  // Split the text into paragraphs
  const paragraphs = text.split('\n').filter(p => p.trim() !== '');

  // Function to process a paragraph and wrap sentences with hover functionality
  const processParagraph = (paragraph: string) => {
    // Check if any sentence from sentenceAnalysis is in this paragraph
    const matchingSentences = sentenceAnalysis.filter(item => 
      paragraph.includes(item.sentence)
    );

    if (matchingSentences.length === 0) {
      // If no matching sentences, return the paragraph as is
      return <p key={paragraph.substring(0, 20)}>{paragraph}</p>;
    }

    // Sort matching sentences by their position in the paragraph
    // to ensure we process them in order
    matchingSentences.sort((a, b) => {
      return paragraph.indexOf(a.sentence) - paragraph.indexOf(b.sentence);
    });

    // Process the paragraph to wrap matching sentences
    let lastIndex = 0;
    const parts: React.ReactNode[] = [];

    matchingSentences.forEach((item, index) => {
      const sentenceIndex = paragraph.indexOf(item.sentence, lastIndex);
      
      if (sentenceIndex === -1) return; // Skip if sentence not found
      
      // Add text before the sentence
      if (sentenceIndex > lastIndex) {
        parts.push(
          <span key={`before-${index}`}>
            {paragraph.substring(lastIndex, sentenceIndex)}
          </span>
        );
      }
      
      // Add the sentence with hover functionality
      parts.push(
        <SentenceWithHover 
          key={`sentence-${index}`}
          sentence={item.sentence}
          url={item.url}
          sentenceAnalysis={sentenceAnalysis}
        />
      );
      
      lastIndex = sentenceIndex + item.sentence.length;
    });
    
    // Add any remaining text after the last matched sentence
    if (lastIndex < paragraph.length) {
      parts.push(
        <span key="after">
          {paragraph.substring(lastIndex)}
        </span>
      );
    }
    
    return <p key={paragraph.substring(0, 20)}>{parts}</p>;
  };

  return (
    <div className="whitespace-pre-wrap">
      {paragraphs.map(processParagraph)}
    </div>
  );
};

export default AIResponseWithHover;
