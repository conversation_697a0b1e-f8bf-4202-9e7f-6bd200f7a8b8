// components/chatComponents/AnimationController.tsx
import React, { useEffect, useRef } from 'react';
import { useAnimationState, AnimationStatus } from '@/stores/animationState';

/**
 * AnimationController is a component that manages the animation queue.
 * It doesn't render anything visible but handles the logic of which message
 * should be animated next.
 */
const AnimationController: React.FC = () => {
  const { 
    currentlyAnimating,
    animationQueue,
    getNextMessageToAnimate,
    startAnimation
  } = useAnimationState();
  
  const processingRef = useRef(false);
  
  // Process the animation queue
  useEffect(() => {
    // If already processing or nothing to process, return
    if (processingRef.current || currentlyAnimating || animationQueue.length === 0) {
      return;
    }
    
    // Set processing flag
    processingRef.current = true;
    
    // Get the next message to animate
    const nextMessageId = getNextMessageToAnimate();
    
    if (nextMessageId) {
      // Start animating the next message
      console.log('Starting animation for message:', nextMessageId);
      startAnimation(nextMessageId);
    }
    
    // Clear processing flag
    processingRef.current = false;
  }, [currentlyAnimating, animationQueue, getNextMessageToAnimate, startAnimation]);
  
  // This component doesn't render anything
  return null;
};

export default AnimationController;
