import React, { useState, useEffect, useRef } from "react";
import { usePathname } from "next/navigation";
import { useChatHandler } from "@/stores/chatList";
import BotReply from "./BotReply";
import UserMessage from "./UserMessage";

function ChatDisplay() {
  const [autoScroll, setAutoScroll] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("English");
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const path = usePathname();
  const { chatList, isAnimation } = useChatHandler();

  // Get chat ID from URL
  const chatId = path ? path.split("/chat/")[1] : null;

  // Find the current chat
  const currentChat = chatList.find((chat) => chat.id === chatId);

  useEffect(() => {
    if (autoScroll && chatContainerRef.current) {
      chatContainerRef.current.scrollTop = chatContainerRef.current.scrollHeight;
    }
  }, [autoScroll, chatList]);

  if (!currentChat) {
    return <div className="flex-1 flex items-center justify-center">No chat found</div>;
  }

  return (
    <div
      ref={chatContainerRef}
      className="flex-1 overflow-y-auto py-8 space-y-8"
    >
      {currentChat.messages.map((message, index) => {
        if (message.isUser) {
          return (
            <UserMessage
              key={index}
              message={typeof message.text === 'string' ? message.text : ''}
              timestamp={message.timestamp}
              uploadedFiles={message.uploadedFiles}
              uploadedURLs={message.uploadedURLs}
              selectedLanguage={selectedLanguage}
            />
          );
        } else {
          // Generate a unique message ID for the bot message
          const messageId = `msg-${message.timestamp}-${index}`;

          return (
            <BotReply
              key={index}
              replyType="response"
              setScroll={setAutoScroll}
              isAnimation={isAnimation}
              aiResponse={message.text}
              timestamp={message.timestamp}
              messageId={messageId}
              selectedLanguage={selectedLanguage}
            />
          );
        }
      })}
    </div>
  );
}

export default ChatDisplay;