import React, { useRef, useEffect } from 'react';
import { PiDatabase, PiCaretDown, PiCheck } from 'react-icons/pi';

interface IndexSelectorProps {
  selectedIndex: string;
  setSelectedIndex: (index: string) => void;
  pineconeIndexes: string[]; // Keep name for compatibility but now represents FAISS categories
  setPineconeIndexes: (indexes: string[]) => void;
  showIndexSelector: boolean;
  setShowIndexSelector: (show: boolean) => void;
  showIndexConfirmation: boolean;
  setShowIndexConfirmation: (show: boolean) => void;
  selectedLanguage: string;
  disabled?: boolean;
}

const IndexSelector: React.FC<IndexSelectorProps> = ({
  selectedIndex,
  setSelectedIndex,
  pineconeIndexes,
  setPineconeIndexes,
  showIndexSelector,
  setShowIndexSelector,
  showIndexConfirmation,
  setShowIndexConfirmation,
  selectedLanguage,
  disabled = false
}) => {
  const indexSelectorRef = useRef<HTMLDivElement>(null);

  // <PERSON>le click outside to close index selector
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (indexSelectorRef.current && !indexSelectorRef.current.contains(event.target as Node)) {
        setShowIndexSelector(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [setShowIndexSelector]);

  // Effect to hide index confirmation after 5 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showIndexConfirmation) {
      timer = setTimeout(() => {
        setShowIndexConfirmation(false);
      }, 5000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showIndexConfirmation, setShowIndexConfirmation]);

  const handleIndexSelect = (index: string) => {
    setSelectedIndex(index);
    setShowIndexSelector(false);
    setShowIndexConfirmation(true);
    
    // Store selected index in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('selectedFaissIndex', index);
      localStorage.setItem('faiss_index_name', index); // Also store in new format
    }

    console.log(`Selected FAISS index: ${index}`);
  };

  const getLanguageColor = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return "purple";
      case "Telugu":
        return "green";
      case "Kannada":
        return "orange";
      default:
        return "blue";
    }
  };

  const getButtonColor = () => {
    const color = getLanguageColor();
    if (disabled) return 'text-gray-400 border-gray-300';
    
    switch (color) {
      case 'purple':
        return 'text-purple-600 border-purple-300 hover:border-purple-400';
      case 'green':
        return 'text-green-600 border-green-300 hover:border-green-400';
      case 'orange':
        return 'text-orange-600 border-orange-300 hover:border-orange-400';
      default:
        return 'text-blue-600 border-blue-300 hover:border-blue-400';
    }
  };

  const getDropdownItemColor = () => {
    const color = getLanguageColor();
    switch (color) {
      case 'purple':
        return 'hover:bg-purple-50 hover:text-purple-700';
      case 'green':
        return 'hover:bg-green-50 hover:text-green-700';
      case 'orange':
        return 'hover:bg-orange-50 hover:text-orange-700';
      default:
        return 'hover:bg-blue-50 hover:text-blue-700';
    }
  };

  const getConfirmationColor = () => {
    const color = getLanguageColor();
    switch (color) {
      case 'purple':
        return 'bg-purple-100 border-purple-200 text-purple-800';
      case 'green':
        return 'bg-green-100 border-green-200 text-green-800';
      case 'orange':
        return 'bg-orange-100 border-orange-200 text-orange-800';
      default:
        return 'bg-blue-100 border-blue-200 text-blue-800';
    }
  };

  const getLanguageText = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return {
          selectIndex: "குறியீட்டைத் தேர்ந்தெடுக்கவும்",
          selectedIndex: "தேர்ந்தெடுக்கப்பட்ட குறியீடு",
          noIndexSelected: "எந்த குறியீடும் தேர்ந்தெடுக்கப்படவில்லை"
        };
      case "Telugu":
        return {
          selectIndex: "ఇండెక్స్‌ను ఎంచుకోండి",
          selectedIndex: "ఎంచుకున్న ఇండెక్స్",
          noIndexSelected: "ఏ ఇండెక్స్ ఎంచుకోబడలేదు"
        };
      case "Kannada":
        return {
          selectIndex: "ಸೂಚ್ಯಂಕವನ್ನು ಆಯ್ಕೆಮಾಡಿ",
          selectedIndex: "ಆಯ್ಕೆಮಾಡಿದ ಸೂಚ್ಯಂಕ",
          noIndexSelected: "ಯಾವುದೇ ಸೂಚ್ಯಂಕವನ್ನು ಆಯ್ಕೆಮಾಡಲಾಗಿಲ್ಲ"
        };
      default:
        return {
          selectIndex: "Select Index",
          selectedIndex: "Selected Index",
          noIndexSelected: "No index selected"
        };
    }
  };

  const text = getLanguageText();
  const color = getLanguageColor();

  return (
    <div className="relative" ref={indexSelectorRef}>
      {/* Index confirmation message */}
      {showIndexConfirmation && selectedIndex && (
        <div className={`
          absolute bottom-full left-0 mb-2 px-3 py-2 rounded-lg border text-sm font-medium
          ${getConfirmationColor()}
          animate-fadeIn z-50 whitespace-nowrap
        `}>
          <div className="flex items-center gap-2">
            <PiCheck className="w-4 h-4" />
            <span>{text.selectedIndex}: {selectedIndex}</span>
          </div>
        </div>
      )}

      {/* Index selector button */}
      <button
        type="button"
        onClick={() => !disabled && setShowIndexSelector(!showIndexSelector)}
        disabled={disabled}
        className={`
          flex items-center gap-2 px-3 py-2 border rounded-lg text-sm font-medium transition-all
          ${getButtonColor()}
          ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}
        `}
        title={disabled ? 'Index selection disabled' : 'Select FAISS category'}
      >
        <PiDatabase className="w-4 h-4" />
        <span className="max-w-24 truncate">
          {selectedIndex || text.noIndexSelected}
        </span>
        <PiCaretDown className={`w-3 h-3 transition-transform ${showIndexSelector ? 'rotate-180' : ''}`} />
      </button>

      {/* Index dropdown menu */}
      {showIndexSelector && !disabled && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-600 shadow-lg z-50 overflow-hidden animate-fadeIn">
          <div className="p-2">
            <div className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1 mb-1">
              {text.selectIndex}
            </div>
            {pineconeIndexes.map((index) => (
              <button
                key={index}
                onClick={() => handleIndexSelect(index)}
                className={`
                  w-full flex items-center gap-3 px-3 py-2 text-left text-sm rounded-lg transition-colors
                  ${selectedIndex === index 
                    ? `bg-${color}-100 text-${color}-700 dark:bg-${color}-900/20` 
                    : `text-gray-700 dark:text-gray-300 ${getDropdownItemColor()}`
                  }
                `}
              >
                <PiDatabase className={`w-4 h-4 text-${color}-500`} />
                <span className="truncate">{index}</span>
                {selectedIndex === index && (
                  <PiCheck className={`ml-auto w-4 h-4 text-${color}-500 flex-shrink-0`} />
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default IndexSelector;
