import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>rrowRight } from 'react-icons/pi';

interface URLInputProps {
  type: 'youtube' | 'article';
  value: string;
  onChange: (value: string) => void;
  onSubmit: (url: string) => void;
  selectedLanguage: string;
}

const URLInput: React.FC<URLInputProps> = ({
  type,
  value,
  onChange,
  onSubmit,
  selectedLanguage
}) => {
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [error, setError] = useState<string>('');

  const getLanguageText = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return {
          youtubePlaceholder: 'YouTube வீடியோ URL ஐ உள்ளிடவும்...',
          articlePlaceholder: 'கட்டுரை அல்லது வலைப்பக்க URL ஐ உள்ளிடவும்...',
          addButton: 'சேர்',
          invalidUrl: 'தவறான URL வடிவம்',
          invalidYoutube: 'தவறான YouTube URL',
          validUrl: 'சரியான URL'
        };
      case 'Telugu':
        return {
          youtubePlaceholder: 'YouTube వీడియో URL ని ఎంటర్ చేయండి...',
          articlePlaceholder: 'వ్యాసం లేదా వెబ్‌పేజీ URL ని ఎంటర్ చేయండి...',
          addButton: 'జోడించు',
          invalidUrl: 'చెల్లని URL ఫార్మాట్',
          invalidYoutube: 'చెల్లని YouTube URL',
          validUrl: 'చెల్లుబాటు అయ్యే URL'
        };
      case 'Kannada':
        return {
          youtubePlaceholder: 'YouTube ವೀಡಿಯೊ URL ಅನ್ನು ನಮೂದಿಸಿ...',
          articlePlaceholder: 'ಲೇಖನ ಅಥವಾ ವೆಬ್‌ಪುಟದ URL ಅನ್ನು ನಮೂದಿಸಿ...',
          addButton: 'ಸೇರಿಸಿ',
          invalidUrl: 'ಅಮಾನ್ಯ URL ಸ್ವರೂಪ',
          invalidYoutube: 'ಅಮಾನ್ಯ YouTube URL',
          validUrl: 'ಮಾನ್ಯ URL'
        };
      default:
        return {
          youtubePlaceholder: 'Enter YouTube video URL...',
          articlePlaceholder: 'Enter article or webpage URL...',
          addButton: 'Add',
          invalidUrl: 'Invalid URL format',
          invalidYoutube: 'Invalid YouTube URL',
          validUrl: 'Valid URL'
        };
    }
  };

  const validateURL = (url: string): { isValid: boolean; error: string } => {
    if (!url.trim()) {
      return { isValid: false, error: '' };
    }

    try {
      const urlObj = new URL(url);
      
      if (type === 'youtube') {
        const isYouTube = 
          urlObj.hostname === 'www.youtube.com' ||
          urlObj.hostname === 'youtube.com' ||
          urlObj.hostname === 'youtu.be' ||
          urlObj.hostname === 'm.youtube.com';
        
        if (!isYouTube) {
          return { isValid: false, error: getLanguageText().invalidYoutube };
        }
      }
      
      return { isValid: true, error: '' };
    } catch {
      return { isValid: false, error: getLanguageText().invalidUrl };
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue);
    
    const validation = validateURL(newValue);
    setIsValid(validation.isValid);
    setError(validation.error);
  };

  const handleSubmit = () => {
    if (isValid && value.trim()) {
      onSubmit(value.trim());
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && isValid && value.trim()) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const getBorderColor = () => {
    if (isValid === null) {
      switch (selectedLanguage) {
        case 'Tamil':
          return 'border-purple-300 focus:border-purple-500';
        case 'Telugu':
          return 'border-green-300 focus:border-green-500';
        case 'Kannada':
          return 'border-orange-300 focus:border-orange-500';
        default:
          return 'border-blue-300 focus:border-blue-500';
      }
    }
    
    return isValid 
      ? 'border-green-300 focus:border-green-500' 
      : 'border-red-300 focus:border-red-500';
  };

  const getButtonColor = () => {
    switch (selectedLanguage) {
      case 'Tamil':
        return 'bg-purple-500 hover:bg-purple-600 focus:ring-purple-500';
      case 'Telugu':
        return 'bg-green-500 hover:bg-green-600 focus:ring-green-500';
      case 'Kannada':
        return 'bg-orange-500 hover:bg-orange-600 focus:ring-orange-500';
      default:
        return 'bg-blue-500 hover:bg-blue-600 focus:ring-blue-500';
    }
  };

  const text = getLanguageText();

  return (
    <div className="space-y-2">
      <div className="relative">
        <input
          type="url"
          value={value}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          placeholder={type === 'youtube' ? text.youtubePlaceholder : text.articlePlaceholder}
          className={`
            w-full px-3 py-2 pr-10 border rounded-lg
            focus:outline-none focus:ring-2 focus:ring-opacity-50
            ${getBorderColor()}
            dark:bg-gray-700 dark:text-white dark:placeholder-gray-400
          `}
        />

        {/* Validation icon */}
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          {isValid === true && (
            <PiCheck className="w-5 h-5 text-green-500" />
          )}
          {isValid === false && value.trim() && (
            <PiX className="w-5 h-5 text-red-500" />
          )}
        </div>
      </div>

      {/* Error message */}
      {error && (
        <p className="text-xs text-red-500 dark:text-red-400">
          {error}
        </p>
      )}

      {/* Success message */}
      {isValid && value.trim() && (
        <p className="text-xs text-green-500 dark:text-green-400">
          {text.validUrl}
        </p>
      )}

      {/* Add button */}
      <button
        type="button"
        onClick={handleSubmit}
        disabled={!isValid || !value.trim()}
        className={`
          w-full flex items-center justify-center gap-2 px-4 py-2 rounded-lg
          text-white font-medium text-sm transition-all
          ${isValid && value.trim()
            ? getButtonColor()
            : 'bg-gray-400 cursor-not-allowed'
          }
          disabled:opacity-50
        `}
      >
        {text.addButton}
        <PiArrowRight className="w-4 h-4" />
      </button>
    </div>
  );
};

export default URLInput;
