"use client";
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, PiArrowClockwise } from "react-icons/pi";

interface UserIndex {
  _id: string;
  email: string;
  index_name: string;
  embed_model: string;
  [key: string]: any;
}

interface UserIndexDisplayProps {
  onIndexSelect?: (indexName: string) => void;
  selectedIndex?: string;
  userEmail?: string;
}

const UserIndexDisplay: React.FC<UserIndexDisplayProps> = ({
  onIndexSelect,
  selectedIndex,
  userEmail
}) => {
  const [userIndexes, setUserIndexes] = useState<UserIndex[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Get user email from localStorage if not provided
  const getCurrentUserEmail = () => {
    if (userEmail) return userEmail;
    if (typeof window !== 'undefined') {
      return localStorage.getItem('user_email') || '';
    }
    return '';
  };

  // Fetch user indexes from FAISS collection
  const fetchUserIndexes = async (showRefreshLoader = false) => {
    try {
      if (showRefreshLoader) {
        setRefreshing(true);
      } else {
        setLoading(true);
      }
      setError(null);

      const currentUserEmail = getCurrentUserEmail();
      if (!currentUserEmail) {
        console.warn("No user email found - cannot fetch user indexes");
        setUserIndexes([]);
        return;
      }

      console.log("Fetching indexes for user:", currentUserEmail);

      // Use filtered API endpoint to get all entries for this user
      const filterUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(currentUserEmail.trim())}`;

      const response = await fetch(filterUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'xxxid': 'FAISS'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch user indexes: ${response.status}`);
      }

      const data = await response.json();
      console.log("User indexes response:", data);

      if (data.statusCode === 200 && data.source && data.source.length > 0) {
        // Parse each item in the source array (they are JSON strings)
        const parsedIndexes = data.source.map((item: string, index: number) => {
          try {
            const parsed = JSON.parse(item);
            return {
              _id: parsed._id?.$oid || parsed._id || `index-${index}`,
              email: parsed.client || parsed.email || currentUserEmail,
              index_name: parsed.index_name || 'Unknown',
              embed_model: parsed.embed_model || 'all-MiniLM-L6-v2',
              originalData: parsed
            };
          } catch (e) {
            console.error(`Error parsing index item ${index}:`, e, item);
            return null;
          }
        }).filter((item: any) => item !== null);

        // Remove duplicates based on index_name
        const uniqueIndexes = parsedIndexes.reduce((acc: UserIndex[], current: UserIndex) => {
          const existingIndex = acc.find(item => item.index_name === current.index_name);
          if (!existingIndex) {
            acc.push(current);
          }
          return acc;
        }, []);

        console.log("Parsed user indexes:", uniqueIndexes);
        setUserIndexes(uniqueIndexes);

        // Store indexes in localStorage for quick access
        if (typeof window !== 'undefined') {
          const indexNames = uniqueIndexes.map(idx => idx.index_name);
          localStorage.setItem('userFaissIndexes', JSON.stringify(indexNames));
          console.log("Stored user indexes in localStorage:", indexNames);
        }
      } else {
        console.log("No indexes found for user:", currentUserEmail);
        setUserIndexes([]);
      }
    } catch (err) {
      console.error("Error fetching user indexes:", err);
      setError(err instanceof Error ? err.message : 'Failed to fetch indexes');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle index selection
  const handleIndexSelect = (indexName: string) => {
    console.log("Index selected:", indexName);
    
    // Store selected index in localStorage
    if (typeof window !== 'undefined') {
      localStorage.setItem('faiss_index_name', indexName);
      
      // Also store the embed model for the selected index
      const selectedIndexData = userIndexes.find(idx => idx.index_name === indexName);
      if (selectedIndexData && selectedIndexData.embed_model) {
        localStorage.setItem('faiss_embed_model', selectedIndexData.embed_model);
      }
    }

    // Call parent callback if provided
    if (onIndexSelect) {
      onIndexSelect(indexName);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    fetchUserIndexes(true);
  };

  // Fetch indexes on component mount
  useEffect(() => {
    fetchUserIndexes();
  }, [userEmail]);

  if (loading) {
    return (
      <div className="user-index-display bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-center py-4">
          <PiSpinner className="animate-spin text-primaryColor mr-2" />
          <span className="text-gray-600 dark:text-gray-300 text-sm">Loading your indexes...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-index-display bg-white dark:bg-gray-800 border border-red-200 dark:border-red-600 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <PiWarning className="text-red-500 mr-2" />
            <span className="text-red-600 dark:text-red-400 text-sm">{error}</span>
          </div>
          <button
            onClick={handleRefresh}
            className="text-primaryColor hover:text-primaryColor/80 transition-colors"
            title="Retry"
          >
            <PiArrowClockwise className="w-4 h-4" />
          </button>
        </div>
      </div>
    );
  }

  if (userIndexes.length === 0) {
    return (
      <div className="user-index-display bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <PiDatabase className="text-gray-400 mr-2" />
            <span className="text-gray-600 dark:text-gray-300 text-sm">No indexes found. Create one to get started.</span>
          </div>
          <button
            onClick={handleRefresh}
            disabled={refreshing}
            className="text-primaryColor hover:text-primaryColor/80 transition-colors disabled:opacity-50"
            title="Refresh"
          >
            <PiArrowClockwise className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="user-index-display bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center">
          <PiDatabase className="text-primaryColor mr-2" />
          <span className="text-gray-700 dark:text-gray-200 text-sm font-medium">
            Your Indexes ({userIndexes.length})
          </span>
        </div>
        <button
          onClick={handleRefresh}
          disabled={refreshing}
          className="text-primaryColor hover:text-primaryColor/80 transition-colors disabled:opacity-50"
          title="Refresh indexes"
        >
          <PiArrowClockwise className={`w-4 h-4 ${refreshing ? 'animate-spin' : ''}`} />
        </button>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
        {userIndexes.map((index) => (
          <button
            key={index._id}
            onClick={() => handleIndexSelect(index.index_name)}
            className={`
              flex items-center justify-between p-3 rounded-lg border transition-all text-left
              ${selectedIndex === index.index_name
                ? 'border-primaryColor bg-primaryColor/10 text-primaryColor'
                : 'border-gray-200 dark:border-gray-600 hover:border-primaryColor/50 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200'
              }
            `}
          >
            <div className="flex-1 min-w-0">
              <div className="font-medium text-sm truncate">{index.index_name}</div>
              <div className="text-xs opacity-75 truncate">{index.embed_model}</div>
            </div>
            {selectedIndex === index.index_name && (
              <PiCheck className="w-4 h-4 flex-shrink-0 ml-2" />
            )}
          </button>
        ))}
      </div>

      {selectedIndex && (
        <div className="mt-3 p-2 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md">
          <div className="flex items-center">
            <PiCheck className="text-green-600 dark:text-green-400 mr-2 w-4 h-4" />
            <span className="text-green-700 dark:text-green-300 text-sm">
              Selected: <span className="font-medium">{selectedIndex}</span>
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserIndexDisplay;
