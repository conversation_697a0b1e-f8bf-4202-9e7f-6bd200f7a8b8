// components/chatComponents/UserMessage.tsx
import React from "react";
import Image from "next/image";
import { PiArrowsCounterClockwise, PiCopy, PiShareFat, PiThumbsDown, PiThumbsUp } from "react-icons/pi";
import ReactMarkdown from 'react-markdown';
import UserUploadedContent from './UserUploadedContent';
import { UploadedFile, UploadedURL } from '@/stores/chatList';

type UserMessageProps = {
  message: string;
  timestamp?: string;
  uploadedFiles?: UploadedFile[];
  uploadedURLs?: UploadedURL[];
  selectedLanguage?: string;
};

const UserMessage: React.FC<UserMessageProps> = ({
  message,
  timestamp,
  uploadedFiles,
  uploadedURLs,
  selectedLanguage = "English"
}) => {
  const formattedTime = timestamp
    ? new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
    : 'just now';

  return (
    <div className="flex justify-end items-start gap-1 sm:gap-3 w-full">
      <div className="flex flex-col justify-end items-end gap-3 flex-1">
        <p className="text-xs text-n100">You, {formattedTime}</p>

        {/* Display uploaded content above the message */}
        <div className="w-full sm:max-w-[90%]">
          <UserUploadedContent
            uploadedFiles={uploadedFiles}
            uploadedURLs={uploadedURLs}
            selectedLanguage={selectedLanguage}
          />

          <div className="bg-primaryColor text-white py-3 px-5 rounded-lg w-full">
            <div className="whitespace-pre-wrap text-sm">
              <ReactMarkdown>{message}</ReactMarkdown>
            </div>
          </div>
        </div>
{/* 
        <div className="flex justify-end items-center gap-2 cursor-pointer">
          <PiThumbsUp />
          <PiThumbsDown />
          <PiCopy />
          <PiArrowsCounterClockwise />
          <PiShareFat />
        </div> */}
      </div>
      {/* <div className="size-7 sm:size-9 rounded-full bg-primaryColor flex justify-center items-center text-white">
        U
      </div> */}
    </div>
  );
};

export default UserMessage;