export interface CachedResponse {
  ai_response: string;
  related_questions?: string[];
  sentence_analysis?: Array<{ sentence: string; url: string; summary?: string }>;
  pinecone_indexes?: string[];
  faiss_categories?: any[];
  has_uploaded_content?: boolean;
  upload_sources?: any[];
  timestamp: number;
  query: string;
  context?: string; // For context-aware caching (index, email, etc.)
  language?: string; // Language of the response
  query_language?: string; // Language of the original query
  translation_applied?: boolean; // Whether translation was applied
  translation_metadata?: any; // Translation metadata
}

export interface CacheStats {
  totalQueries: number;
  cacheHits: number;
  cacheMisses: number;
  hitRate: number;
  cacheSize: number;
}

export class CacheService {
  private static readonly CACHE_KEY = 'financial_query_cache';
  private static readonly MAX_CACHE_SIZE = 100; // Maximum number of cached responses
  private static readonly CACHE_EXPIRY_HOURS = 24; // Cache expires after 24 hours
  private static readonly STATS_KEY = 'financial_query_cache_stats';
  public static readonly ARTIFICIAL_DELAY_MS = 2000; // 2-second delay for cached responses

  /**
   * Generate a cache key based on query, context, and language
   */
  private static generateCacheKey(query: string, context?: string, language?: string): string {
    const normalizedQuery = query.trim().toLowerCase();
    const contextStr = context || '';
    const languageStr = language || 'en';
    return `${normalizedQuery}|${contextStr}|${languageStr}`;
  }

  /**
   * Get cached response for a query with language support
   */
  static getCachedResponse(query: string, context?: string, language?: string): CachedResponse | null {
    try {
      if (typeof window === 'undefined') return null;

      const cacheKey = this.generateCacheKey(query, context, language);
      const cacheData = localStorage.getItem(this.CACHE_KEY);

      if (!cacheData) {
        this.updateStats('miss');
        return null;
      }

      const cache: Record<string, CachedResponse> = JSON.parse(cacheData);
      const cachedItem = cache[cacheKey];

      if (!cachedItem) {
        this.updateStats('miss');
        return null;
      }

      // Check if cache has expired
      const now = Date.now();
      const expiryTime = cachedItem.timestamp + (this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000);

      if (now > expiryTime) {
        // Remove expired item
        delete cache[cacheKey];
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
        this.updateStats('miss');
        return null;
      }

      this.updateStats('hit');
      console.log(`🎯 Cache HIT for query: "${query.substring(0, 50)}..." (Language: ${language || 'default'})`);
      console.log(`⚡ Cached response found - will apply ${this.ARTIFICIAL_DELAY_MS}ms delay for consistent UX`);
      return cachedItem;
    } catch (error) {
      console.error('Error retrieving cached response:', error);
      this.updateStats('miss');
      return null;
    }
  }

  /**
   * Cache a response for a query with language support
   */
  static setCachedResponse(query: string, response: any, context?: string, language?: string): void {
    try {
      if (typeof window === 'undefined') return;

      const cacheKey = this.generateCacheKey(query, context, language);
      const cacheData = localStorage.getItem(this.CACHE_KEY);

      let cache: Record<string, CachedResponse> = {};
      if (cacheData) {
        cache = JSON.parse(cacheData);
      }

      // Create cached response object with language metadata
      const cachedResponse: CachedResponse = {
        ai_response: response.ai_response || '',
        related_questions: response.related_questions || [],
        sentence_analysis: response.sentence_analysis || [],
        pinecone_indexes: response.pinecone_indexes || [],
        faiss_categories: response.faiss_categories || [],
        has_uploaded_content: response.has_uploaded_content,
        upload_sources: response.upload_sources || [],
        timestamp: Date.now(),
        query: query.trim(),
        context: context,
        language: language,
        query_language: response.query_language,
        translation_applied: response.translation_applied || false,
        translation_metadata: response.translation_metadata
      };

      // Add to cache
      cache[cacheKey] = cachedResponse;

      // Implement LRU eviction if cache is too large
      const cacheKeys = Object.keys(cache);
      if (cacheKeys.length > this.MAX_CACHE_SIZE) {
        // Sort by timestamp and remove oldest entries
        const sortedEntries = cacheKeys
          .map(key => ({ key, timestamp: cache[key].timestamp }))
          .sort((a, b) => a.timestamp - b.timestamp);

        // Remove oldest entries to make room
        const entriesToRemove = sortedEntries.slice(0, cacheKeys.length - this.MAX_CACHE_SIZE + 1);
        entriesToRemove.forEach(entry => {
          delete cache[entry.key];
        });
      }

      localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
      console.log(`💾 Cached response for query: "${query.substring(0, 50)}..." (Language: ${language || 'default'})`);
    } catch (error) {
      console.error('Error caching response:', error);
    }
  }

  /**
   * Update cache statistics
   */
  private static updateStats(type: 'hit' | 'miss'): void {
    try {
      if (typeof window === 'undefined') return;

      const statsData = localStorage.getItem(this.STATS_KEY);
      let stats = {
        totalQueries: 0,
        cacheHits: 0,
        cacheMisses: 0
      };

      if (statsData) {
        stats = JSON.parse(statsData);
      }

      stats.totalQueries++;
      if (type === 'hit') {
        stats.cacheHits++;
      } else {
        stats.cacheMisses++;
      }

      localStorage.setItem(this.STATS_KEY, JSON.stringify(stats));
    } catch (error) {
      console.error('Error updating cache stats:', error);
    }
  }

  /**
   * Get cache statistics
   */
  static getCacheStats(): CacheStats {
    try {
      if (typeof window === 'undefined') {
        return { totalQueries: 0, cacheHits: 0, cacheMisses: 0, hitRate: 0, cacheSize: 0 };
      }

      const statsData = localStorage.getItem(this.STATS_KEY);
      const cacheData = localStorage.getItem(this.CACHE_KEY);
      
      let stats = {
        totalQueries: 0,
        cacheHits: 0,
        cacheMisses: 0
      };

      if (statsData) {
        stats = JSON.parse(statsData);
      }

      const cacheSize = cacheData ? Object.keys(JSON.parse(cacheData)).length : 0;
      const hitRate = stats.totalQueries > 0 ? (stats.cacheHits / stats.totalQueries) * 100 : 0;

      return {
        totalQueries: stats.totalQueries,
        cacheHits: stats.cacheHits,
        cacheMisses: stats.cacheMisses,
        hitRate: Math.round(hitRate * 100) / 100,
        cacheSize
      };
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return { totalQueries: 0, cacheHits: 0, cacheMisses: 0, hitRate: 0, cacheSize: 0 };
    }
  }

  /**
   * Clear all cached responses
   */
  static clearCache(): void {
    try {
      if (typeof window === 'undefined') return;
      
      localStorage.removeItem(this.CACHE_KEY);
      localStorage.removeItem(this.STATS_KEY);
      console.log('🗑️ Cache cleared successfully');
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  /**
   * Remove expired cache entries
   */
  static cleanupExpiredCache(): void {
    try {
      if (typeof window === 'undefined') return;

      const cacheData = localStorage.getItem(this.CACHE_KEY);
      if (!cacheData) return;

      const cache: Record<string, CachedResponse> = JSON.parse(cacheData);
      const now = Date.now();
      const expiryTime = this.CACHE_EXPIRY_HOURS * 60 * 60 * 1000;

      let removedCount = 0;
      Object.keys(cache).forEach(key => {
        if (now - cache[key].timestamp > expiryTime) {
          delete cache[key];
          removedCount++;
        }
      });

      if (removedCount > 0) {
        localStorage.setItem(this.CACHE_KEY, JSON.stringify(cache));
        console.log(`🧹 Cleaned up ${removedCount} expired cache entries`);
      }
    } catch (error) {
      console.error('Error cleaning up expired cache:', error);
    }
  }

  /**
   * Apply artificial delay for cached responses to ensure consistent UX
   */
  static async applyCachedResponseDelay(): Promise<void> {
    console.log(`⏳ Applying ${this.ARTIFICIAL_DELAY_MS}ms artificial delay for cached response...`);
    return new Promise(resolve => {
      setTimeout(() => {
        console.log(`✅ Artificial delay completed - returning cached response`);
        resolve();
      }, this.ARTIFICIAL_DELAY_MS);
    });
  }

  /**
   * Get cached response with automatic delay application
   */
  static async getCachedResponseWithDelay(query: string, context?: string, language?: string): Promise<CachedResponse | null> {
    const cachedResponse = this.getCachedResponse(query, context, language);

    if (cachedResponse) {
      // Apply artificial delay for consistent UX
      await this.applyCachedResponseDelay();
      return cachedResponse;
    }

    return null;
  }
}
