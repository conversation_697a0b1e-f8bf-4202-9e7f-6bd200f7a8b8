"use client";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState, useRef } from "react";
import { createPortal } from "react-dom";
import {
  PiPencilLine,
  PiSignOut,

} from "react-icons/pi";
import { PiUserCircle } from 'react-icons/pi';


import { useMainModal } from "@/stores/modal";
import { baseUrl, uid } from "@/components/api/api";
import { useRouter } from "next/navigation";

type User = {
  name: string;
  mobileno: string;
};

// Reuse your fetchUserLogo function here
export async function fetchUserLogo(username: string, mobileno: string): Promise<string | null> {
  if (!username || !mobileno) return null;

  const apiUrl = `${baseUrl}/eRetrieve?filtercount=2&f1_field=phonenumber_S&f1_op=eq&f1_value=${mobileno}&f2_field=name_S&f2_op=eq&f2_value=${username}`;

  try {
    const response = await fetch(apiUrl, {
      method: "GET",
      headers: {
        xxxid: uid,
      },
    });

    if (!response.ok) throw new Error("Failed to fetch user logo");

    const contentType = response.headers.get("content-type") || "";

    if (contentType.includes("image")) {
      const blob = await response.blob();
      return URL.createObjectURL(blob);
    } else {
      const data = await response.json();
      return data.logoUrl || null;
    }
  } catch (error) {
    console.error("Error fetching user logo:", error);
    return null;
  }
};


function UserModal() {
  const { modalOpen } = useMainModal();
  const router = useRouter();

  // Custom modal state instead of using useModalOpen hook to avoid conflicts
  const [modal, setModal] = useState(false);
  const modalRef = useRef<HTMLDivElement | null>(null);
  const [profileImg, setProfileImg] = useState<string | null>(null);
  const [userName, setUserName] = useState<string>("User");
  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false);
  const [modalPosition, setModalPosition] = useState({ top: 0, right: 0 });
  const [isLogoutProcessing, setIsLogoutProcessing] = useState(false);

  useEffect(() => {
    const storedData = sessionStorage.getItem("resultUser");
    if (!storedData) return;

    try {
      const user: User = JSON.parse(storedData);
      setUserName(user.name || "User");

      if (user.name && user.mobileno) {
        fetchUserLogo(user.name, user.mobileno).then((imgUrl) => {
          if (imgUrl && typeof imgUrl === "string") {
            setProfileImg(imgUrl);
          } else {
            // fallback: use default image
            setProfileImg(null); // just don't set it if invalid
          }
        });
      }
    } catch (error) {
      console.error("Failed to parse session user data:", error);
    }
  }, []);



  useEffect(() => {
    const handleImageUpdate = (e: Event) => {
      const customEvent = e as CustomEvent<string>;
      setProfileImg(customEvent.detail); // update state with new image
    };

    window.addEventListener("profileImageUpdated", handleImageUpdate);

    return () => {
      window.removeEventListener("profileImageUpdated", handleImageUpdate);
    };
  }, []);

  // Custom click outside handler for portal modal
  useEffect(() => {
    if (!modal) return;

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;

      // Check if click is outside both the button and the modal
      if (modalRef.current && !modalRef.current.contains(target)) {
        // Also check if the click is not on the portal modal or logout confirmation
        const portalModal = document.querySelector('[data-user-modal-portal]');
        const logoutModal = document.querySelector('[data-logout-confirmation]');
        
        if ((!portalModal || !portalModal.contains(target)) && 
            (!logoutModal || !logoutModal.contains(target))) {
          console.log("🔄 Clicked outside modal, closing");
          setModal(false);
        }
      }
    };

    // Use capture phase to ensure we get the event first
    document.addEventListener("mousedown", handleClickOutside, true);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside, true);
    };
  }, [modal, setModal]);

  // Handle logout confirmation - Using the working solution from header
  const handleLogout = () => {
    if (isLogoutProcessing) {
      console.log("🔄 Logout already in progress, ignoring...");
      return;
    }

    setIsLogoutProcessing(true);
    console.log("🔄 Profile logout clicked - using working solution");

    try {
      // Clear all storage immediately (same as working header solution)
      console.log("🔄 Clearing all storage...");
      sessionStorage.clear();
      localStorage.clear();

      // Close modals first
      setModal(false);
      setShowLogoutConfirm(false);

      // Force immediate redirect (same as working header solution)
      console.log("🔄 Forcing redirect to sign-in...");
      
      // Use setTimeout to ensure state updates are processed
      setTimeout(() => {
        window.location.href = "/sign-in";
      }, 100);
      
    } catch (error) {
      console.error("❌ Error during logout:", error);
      // Even if there's an error, try to redirect
      setTimeout(() => {
        window.location.href = "/sign-in";
      }, 100);
    }
  };

  // Handle logout click
  const handleLogoutClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log("🔄 Logout button clicked");
    
    // Force focus to ensure the modal is interactive
    if (e.currentTarget) {
      (e.currentTarget as HTMLElement).focus();
    }
    
    setShowLogoutConfirm(true);
  };

  // fallback user image url
  const defaultProfileImg = "/images/logodefault.png";

  return (
    <div className="relative size-9" ref={modalRef}>
      <button
        onClick={(e) => {
          console.log("🔄 User modal button clicked, current modal state:", modal);

          // Calculate position for portal modal
          const rect = e.currentTarget.getBoundingClientRect();
          setModalPosition({
            top: rect.bottom + 8,
            right: window.innerWidth - rect.right
          });

          setModal((prev) => !prev);
        }}
        className="relative z-[1]"
      >
        <img
          src={profileImg || defaultProfileImg}
          onError={(e) => {
            e.currentTarget.src = defaultProfileImg;
          }}
          alt=""
          className="rounded-full object-cover w-full h-full"
          loading="lazy"
          decoding="async"
        />
      </button>

      {/* User Modal Dropdown - Using Portal to render at document body level */}
      {modal && typeof window !== 'undefined' && createPortal(
        <div
          data-user-modal-portal
          className={`fixed bg-white dark:bg-n0 border border-primaryColor/30 p-3 rounded-xl text-sm duration-300 text-n500 dark:text-n30 w-[240px] shadow-lg transition-all ${modal ? "visible translate-y-0 opacity-100" : "invisible translate-y-2 opacity-0"
            }`}
          style={{
            pointerEvents: 'auto',
            position: 'fixed',
            top: `${modalPosition.top}px`,
            right: `${modalPosition.right}px`,
            zIndex: **********, // Maximum safe z-index value
            isolation: 'isolate' // Create new stacking context
          }}
        >
        <ul className="flex flex-col gap-1 justify-start items-start">
          <li 
            className="flex justify-start items-center gap-2 p-3 border-b border-primaryColor/30 cursor-pointer w-full hover:bg-primaryColor/5 duration-300"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log("🔄 Username clicked - opening profile");
              console.log("🔄 modalOpen function:", modalOpen);
              try {
                setModal(false);
                // Small delay to ensure modal closes before opening new one
                setTimeout(() => {
                  modalOpen("Profile");
                  console.log("✅ Profile modal opened successfully");
                }, 100);
              } catch (error) {
                console.error("❌ Error opening profile modal:", error);
              }
            }}
            onMouseDown={(e) => e.stopPropagation()}
            onTouchStart={(e) => e.stopPropagation()}
            style={{
              pointerEvents: 'auto',
              position: 'relative',
              zIndex: **********,
              touchAction: 'manipulation'
            }}
            tabIndex={0}
          >
            <img
              src={profileImg || defaultProfileImg}
              onError={(e) => {
                e.currentTarget.src = defaultProfileImg;
              }}
              alt=""
              className="size-7 rounded-full"
              loading="lazy"
              decoding="async"
            />
            <span>{userName}</span>
          </li>

          <li
            className="flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-primaryColor/30 hover:bg-primaryColor/5 duration-300 cursor-pointer w-full"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log("🔄 Profile button clicked");
              console.log("🔄 modalOpen function:", modalOpen);
              try {
                setModal(false);
                // Small delay to ensure modal closes before opening new one
                setTimeout(() => {
                  modalOpen("Profile");
                  console.log("✅ Profile modal opened successfully");
                }, 100);
              } catch (error) {
                console.error("❌ Error opening profile modal:", error);
              }
            }}
            onMouseDown={(e) => e.stopPropagation()}
            onTouchStart={(e) => e.stopPropagation()}
            style={{
              pointerEvents: 'auto',
              position: 'relative',
              zIndex: **********,
              touchAction: 'manipulation'
            }}
            tabIndex={0}
          >
            <PiUserCircle className="text-xl" />
            <span>Profile</span>
          </li>

          <li
            className="flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-primaryColor/30 hover:bg-primaryColor/5 duration-300 cursor-pointer w-full"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              console.log("🔄 Change Password button clicked");
              console.log("🔄 modalOpen function:", modalOpen);
              try {
                setModal(false);
                // Small delay to ensure modal closes before opening new one
                setTimeout(() => {
                  modalOpen("Change Password");
                  console.log("✅ Change Password modal opened successfully");
                }, 100);
              } catch (error) {
                console.error("❌ Error opening Change Password modal:", error);
              }
            }}
            onMouseDown={(e) => e.stopPropagation()}
            onTouchStart={(e) => e.stopPropagation()}
            style={{
              pointerEvents: 'auto',
              position: 'relative',
              zIndex: **********,
              touchAction: 'manipulation'
            }}
            tabIndex={0}
          >
            <PiPencilLine className="text-xl" />
            <span>Change Password</span>
          </li>


          <li className="w-full">
            <button
              onClick={handleLogoutClick}
              onDoubleClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log("🔄 Double-click logout - bypassing confirmation");
                handleLogout();
              }}
              onMouseDown={(e) => {
                // Ensure the event is captured
                e.stopPropagation();
              }}
              onTouchStart={(e) => {
                // Handle touch events for mobile
                e.stopPropagation();
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  e.stopPropagation();
                  handleLogoutClick(e as any);
                }
              }}
              className="flex justify-start items-center gap-2 p-3 rounded-lg border border-transparent hover:border-errorColor/30 hover:bg-errorColor/5 duration-300 cursor-pointer w-full text-errorColor relative"
              style={{
                pointerEvents: 'auto',
                position: 'relative',
                zIndex: **********,
                touchAction: 'manipulation' // Prevent touch delays
              }}
              title="Click to confirm logout, double-click to logout immediately"
              tabIndex={0} // Ensure it's focusable
            >
              <PiSignOut className="text-xl" />
              <span>Log Out</span>
            </button>
          </li>

        </ul>
        </div>,
        document.body
      )}

      {/* Logout Confirmation Modal - Using Portal to render at document body level */}
      {showLogoutConfirm && typeof window !== 'undefined' && createPortal(
        <div 
          data-logout-confirmation
          className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm" 
          style={{ 
            pointerEvents: 'auto',
            zIndex: **********
          }}
        >
          <div className="bg-white dark:bg-n0 p-6 rounded-xl shadow-2xl max-w-sm w-full mx-4 border border-gray-200 dark:border-n800 transform transition-all duration-200 scale-100">
            <h2 className="text-lg font-semibold text-n800 dark:text-n10 mb-4">
              Confirm Logout
            </h2>
            <p className="text-sm text-n600 dark:text-n40 mb-6">
              Are you sure you want to log out? You will need to sign in again to access your account.
            </p>
            <div className="flex justify-end gap-3">
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log("🔄 Cancel logout clicked");
                  setShowLogoutConfirm(false);
                }}
                className="px-4 py-2 text-sm font-medium text-n700 dark:text-n20 bg-gray-200 dark:bg-n800 rounded-md hover:bg-gray-300 dark:hover:bg-n700 transition-colors focus:outline-none focus:ring-2 focus:ring-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log("🔄 Confirm logout clicked");
                  handleLogout();
                }}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors flex items-center focus:outline-none focus:ring-2 focus:ring-red-500"
                style={{ pointerEvents: 'auto', zIndex: ********** }}
              >
                <PiSignOut className="mr-2" />
                Log Out
              </button>
            </div>
          </div>
        </div>,
        document.body
      )}

      {/* CSS to ensure modal is always clickable */}
      <style jsx global>{`
        [data-user-modal-portal] {
          pointer-events: auto !important;
          z-index: ********** !important;
          position: fixed !important;
        }

        [data-user-modal-portal] * {
          pointer-events: auto !important;
        }

        [data-user-modal-portal] button,
        [data-user-modal-portal] li,
        [data-user-modal-portal] div {
          pointer-events: auto !important;
          position: relative !important;
          z-index: ********** !important;
          cursor: pointer !important;
        }

        [data-user-modal-portal] li:hover {
          background-color: rgba(var(--primary-color-rgb, 59, 130, 246), 0.05) !important;
        }

        [data-logout-confirmation] {
          pointer-events: auto !important;
          z-index: ********** !important;
          position: fixed !important;
        }

        [data-logout-confirmation] * {
          pointer-events: auto !important;
        }

        [data-logout-confirmation] button {
          pointer-events: auto !important;
          cursor: pointer !important;
          z-index: ********** !important;
        }

        .user-modal-logout-btn {
          pointer-events: auto !important;
          cursor: pointer !important;
          z-index: ********** !important;
        }

        .user-modal-logout-btn:hover {
          background-color: rgba(244, 67, 54, 0.2) !important;
          border-color: rgba(244, 67, 54, 0.7) !important;
        }

        /* Ensure modals are above everything else */
        [data-user-modal-portal],
        [data-logout-confirmation] {
          isolation: isolate !important;
        }
      `}</style>
    </div>
  );
}

export default UserModal;
