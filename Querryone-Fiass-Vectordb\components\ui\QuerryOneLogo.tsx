import React, { useEffect, useRef } from 'react';
import Hexa<PERSON><PERSON>ogo from './HexagonLogo';
import '../../styles/snowfall.css';

interface AIQuillLogoProps {
  showSnowfall?: boolean;
  darkMode?: boolean;
  className?: string;
}

const AIQuillLogo: React.FC<AIQuillLogoProps> = ({
  showSnowfall = true,
  darkMode = true,
  className = '',
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const snowflakesRef = useRef<HTMLDivElement[]>([]);

  // Create snowfall effect
  useEffect(() => {
    // Only run on client side to prevent hydration errors
    if (typeof window === 'undefined') return;
    if (!showSnowfall || !containerRef.current) return;

    // Clear any existing snowflakes
    snowflakesRef.current.forEach(flake => flake.remove());
    snowflakesRef.current = [];

    const container = containerRef.current;
    const containerRect = container.getBoundingClientRect();
    const snowflakeCount = 30;

    // Create snowflakes
    for (let i = 0; i < snowflakeCount; i++) {
      const snowflake = document.createElement('div');
      const size = Math.floor(Math.random() * 3) + 1; // 1, 2, or 3

      snowflake.className = `snowflake size-${size}`;
      snowflake.style.left = `${Math.random() * 100}%`;
      snowflake.style.top = `-${Math.random() * 10 + 5}px`;
      snowflake.style.opacity = `${Math.random() * 0.7 + 0.3}`;

      // Add some randomness to animation
      snowflake.style.animationDelay = `${Math.random() * 5}s`;
      snowflake.style.animationDuration = `${5 + Math.random() * 7}s`;

      container.appendChild(snowflake);
      snowflakesRef.current.push(snowflake);
    }

    return () => {
      // Clean up snowflakes
      snowflakesRef.current.forEach(flake => flake.remove());
      snowflakesRef.current = [];
    };
  }, [showSnowfall]);

  return (
    <div
      ref={containerRef}
      className={`logo-container ${darkMode ? 'bg-[#1a2231]' : 'bg-gray-100'} ${className}`}
    >
      <div className="flex justify-start items-center gap-1.5 p-3 relative overflow-hidden">
        <HexagonLogo size={32} color={darkMode ? '#4d6bfe' : '#3d5afe'} />
        <span
          className={`text-2xl font-semibold logo-text ${
            darkMode ? 'text-gray-200' : 'text-gray-800'
          }`}
        >
          QuerryOne
        </span>
      </div>
    </div>
  );
};

export default AIQuillLogo;
