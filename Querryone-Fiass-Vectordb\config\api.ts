/**
 * API Configuration
 * Centralized configuration for all API endpoints
 */

// Environment-based configuration
const isDevelopment = process.env.NODE_ENV === 'development';

// Backend server configuration
export const BACKEND_CONFIG = {
  // Base URL for the backend server
  BASE_URL: process.env.NEXT_PUBLIC_BACKEND_URL || 'http://localhost:5010',
  
  // API endpoints
  ENDPOINTS: {
    FINANCIAL_QUERY: '/financial_query',
    HEALTH: '/api/health',
    TRANSLATE: '/api/translate',
    UPLOAD_CSV: '/api/upload-csv',
    QUERY_FAISS: '/api/query-faiss',
    LIST_FAISS_INDEXES: '/api/list-faiss-indexes',
    LIST_EMBEDDING_MODELS: '/api/list-embedding-models',
    PROCESS_YOUTUBE: '/api/process_youtube',
    PROCESS_ARTICLE: '/api/process_article',
    PROCESS_DOCUMENT: '/api/process_document',
    PROCESS_PDF: '/api/process_pdf',
    PROCESS_AUDIO: '/api/process_audio',
  }
};

// Full API URLs
export const API_URLS = {
  FINANCIAL_QUERY: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.FINANCIAL_QUERY}`,
  HEALTH: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.HEALTH}`,
  TRANSLATE: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.TRANSLATE}`,
  UPLOAD_CSV: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.UPLOAD_CSV}`,
  QUERY_FAISS: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.QUERY_FAISS}`,
  LIST_FAISS_INDEXES: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.LIST_FAISS_INDEXES}`,
  LIST_EMBEDDING_MODELS: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.LIST_EMBEDDING_MODELS}`,
  PROCESS_YOUTUBE: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.PROCESS_YOUTUBE}`,
  PROCESS_ARTICLE: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.PROCESS_ARTICLE}`,
  PROCESS_DOCUMENT: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.PROCESS_DOCUMENT}`,
  PROCESS_PDF: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.PROCESS_PDF}`,
  PROCESS_AUDIO: `${BACKEND_CONFIG.BASE_URL}${BACKEND_CONFIG.ENDPOINTS.PROCESS_AUDIO}`,
};

// External API endpoints (not affected by backend URL)
export const EXTERNAL_APIS = {
  PINE_COLLECTION: "https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=FAISS",
  MANNIT_BASE: "https://dev-commonmannit.mannit.co/mannit"
};

// CORS origins for backend configuration
export const CORS_ORIGINS = [
  "http://localhost:3000",
  "http://localhost:3001", 
  "http://127.0.0.1:3000",
  "http://127.0.0.1:3001",
  "http://**************:3000",
  "http://**************:3001",
  BACKEND_CONFIG.BASE_URL
];

// Default configuration values
export const DEFAULT_CONFIG = {
  EMBEDDING_MODEL: "all-MiniLM-L6-v2",
  INDEX_NAME: "default",
  CHUNK_SIZE: 500,
  BATCH_SIZE: 10
};

export default {
  BACKEND_CONFIG,
  API_URLS,
  EXTERNAL_APIS,
  CORS_ORIGINS,
  DEFAULT_CONFIG
};
