# PINE Collection Email-Based Filtering

This document describes the comprehensive email-based filtering system implemented for the PINE collection to ensure data isolation between users based on email authentication.

## Overview

The email-based filtering system ensures that users can only access PINE collection records that belong to their authenticated email address. This provides secure data isolation in a multi-user environment.

## Architecture

### Backend Components

1. **Database Layer** (`python-fiass-backend/database.py`)
   - Enhanced `list_pine_categories()` with case-insensitive email filtering
   - New `get_user_pine_indices()` function for retrieving user-specific indices
   - New `validate_user_access_to_index()` function for access validation

2. **Filter Service** (`python-fiass-backend/pine_filter_service.py`)
   - Centralized `PineFilterService` class
   - Email validation and normalization
   - User access validation
   - Record filtering by email

3. **API Endpoints** (`python-fiass-backend/full_code.py`)
   - Enhanced `/api/list-categories` endpoint
   - New `/api/user-indices` endpoint
   - New `/api/validate-user-access` endpoint

### Frontend Components

1. **Filter Service** (`services/pineFilterService.ts`)
   - TypeScript service for frontend filtering operations
   - Email validation and user session management
   - API communication with backend filtering endpoints

2. **React Hooks** (`hooks/usePineFilter.ts`)
   - `usePineFilter` - Main hook for filtering functionality
   - `usePineIndexAccess` - Hook for validating index access
   - `useFilteredIndices` - Hook for filtering index lists

3. **Example Component** (`components/examples/PineFilterExample.tsx`)
   - Comprehensive demonstration of filtering features
   - Interactive UI for testing filtering functionality

## Key Features

### 1. Email-Based Data Isolation
- Users only see records where `email` field matches their authenticated email
- Case-insensitive email comparison with whitespace handling
- Prevents cross-user data access

### 2. Enhanced Security
- Email validation and normalization
- Access validation before data operations
- Secure session management

### 3. Performance Optimized
- Database-level filtering reduces data transfer
- Efficient SQL queries with proper indexing
- Caching of user permissions

### 4. Developer-Friendly
- Easy-to-use React hooks
- Comprehensive TypeScript types
- Backward compatibility with existing code

## Usage Examples

### Backend Usage

```python
from pine_filter_service import PineFilterService

# Get user's accessible indices
success, message, indices = PineFilterService.get_user_indices("<EMAIL>")

# Validate user access to specific index
has_access, message = PineFilterService.validate_user_access("<EMAIL>", "my-index")

# Get user's records with filtering
success, message, records = PineFilterService.get_user_records("<EMAIL>", limit=10)
```

### Frontend Usage

```typescript
import { usePineFilter } from '../hooks/usePineFilter';
import { PineFilterService } from '../services/pineFilterService';

// Using React Hook
const MyComponent = () => {
  const { userIndices, validateAccess, filterIndices } = usePineFilter();
  
  // Component logic here
};

// Using Service Directly
const indices = await PineFilterService.getUserIndices();
const hasAccess = await PineFilterService.validateUserAccess('my-index');
```

## API Endpoints

### GET /api/user-indices
Get all PINE collection indices for the authenticated user.

**Request:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Found 3 indices for user",
  "indices": ["default", "documents", "articles"],
  "count": 3,
  "user_email": "<EMAIL>"
}
```

### POST /api/validate-user-access
Validate if a user has access to a specific index.

**Request:**
```json
{
  "email": "<EMAIL>",
  "index_name": "my-index"
}
```

**Response:**
```json
{
  "success": true,
  "has_access": true,
  "message": "Access granted to index my-index",
  "user_email": "<EMAIL>",
  "index_name": "my-index"
}
```

## Database Schema

The `pine_collection` table structure:

```sql
CREATE TABLE pine_collection (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    index_name TEXT NOT NULL,
    email TEXT,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(index_name, email)
);
```

## Security Considerations

1. **Email Validation**: All email inputs are validated and normalized
2. **Case-Insensitive Matching**: Prevents bypass through case variations
3. **SQL Injection Prevention**: Parameterized queries used throughout
4. **Session Management**: Secure handling of user authentication state
5. **Access Control**: Validation at multiple layers (database, service, API)

## Error Handling

The system provides comprehensive error handling:

- **Invalid Email**: Clear error messages for malformed emails
- **Access Denied**: Specific messages when user lacks access
- **Database Errors**: Graceful handling of database connection issues
- **Network Errors**: Proper error propagation in frontend

## Performance Considerations

1. **Database Indexing**: Proper indexes on email and index_name columns
2. **Query Optimization**: Efficient SQL queries with minimal data transfer
3. **Caching**: User permissions cached in frontend session
4. **Lazy Loading**: Data loaded only when needed

## Testing

The system includes comprehensive testing capabilities:

1. **Unit Tests**: Individual function testing
2. **Integration Tests**: End-to-end filtering workflow
3. **Security Tests**: Access control validation
4. **Performance Tests**: Query optimization verification

## Migration Guide

For existing applications, follow these steps:

1. **Database Migration**: Run database schema updates
2. **Backend Integration**: Import and use new filtering services
3. **Frontend Updates**: Replace existing filtering with new hooks/services
4. **Testing**: Verify filtering works correctly for all users
5. **Deployment**: Deploy with proper environment configuration

## Troubleshooting

Common issues and solutions:

1. **User sees no data**: Check email authentication and database records
2. **Access denied errors**: Verify user has proper records in pine_collection
3. **Performance issues**: Check database indexes and query optimization
4. **Frontend errors**: Verify API endpoints are accessible and user is authenticated

## Future Enhancements

Planned improvements:

1. **Role-Based Access**: Additional access control beyond email matching
2. **Audit Logging**: Track access attempts and data operations
3. **Real-Time Updates**: WebSocket-based real-time filtering updates
4. **Advanced Caching**: Redis-based caching for improved performance
