import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiTranslate, PiChartLine } from 'react-icons/pi';
import Header from '@/components/Header';
import MultilingualFinancialQuery from '@/components/chatComponents/MultilingualFinancialQuery';

const KannadaFinancialPage: React.FC = () => {
  const router = useRouter();
  const [showSidebar, setShowSidebar] = useState(false);
  const [queryHistory, setQueryHistory] = useState<any[]>([]);

  const handleBack = () => {
    router.back();
  };

  const handleResponse = (response: any) => {
    console.log('Kannada Financial Query Response:', response);
    // Add to history
    setQueryHistory(prev => [response, ...prev.slice(0, 4)]); // Keep last 5 queries
  };

  const handleError = (error: string) => {
    console.error('Kannada Financial Query Error:', error);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100 transition-colors"
              >
                <PiArrowLeft className="mr-2" />
                ಹಿಂದೆ
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                  <PiChartLine className="mr-3 text-blue-600" />
                  ಕನ್ನಡ ಹಣಕಾಸು ಸಹಾಯಕ
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-1">
                  ನಿಮ್ಮ ಹಣಕಾಸಿನ ಪ್ರಶ್ನೆಗಳನ್ನು ಕನ್ನಡದಲ್ಲಿ ಕೇಳಿ ಮತ್ತು ಕನ್ನಡದಲ್ಲೇ ಉತ್ತರಗಳನ್ನು ಪಡೆಯಿರಿ
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
            <PiTranslate className="mr-2" />
            ಇದು ಹೇಗೆ ಕೆಲಸ ಮಾಡುತ್ತದೆ
          </h2>
          <div className="text-blue-800 dark:text-blue-200 space-y-2">
            <p>• ನಿಮ್ಮ ಹಣಕಾಸಿನ ಪ್ರಶ್ನೆಯನ್ನು ಕನ್ನಡದಲ್ಲಿ ಟೈಪ್ ಮಾಡಿ</p>
            <p>• ನಮ್ಮ ಸಿಸ್ಟಂ ಅದನ್ನು ಇಂಗ್ಲಿಷ್‌ಗೆ ಅನುವಾದಿಸುತ್ತದೆ</p>
            <p>• ಹಣಕಾಸು ಡೇಟಾಬೇಸ್‌ನಲ್ಲಿ ಹುಡುಕುತ್ತದೆ</p>
            <p>• ಉತ್ತರವನ್ನು ಮತ್ತೆ ಕನ್ನಡಕ್ಕೆ ಅನುವಾದಿಸುತ್ತದೆ</p>
            <p>• ನಿಮಗೆ ಕನ್ನಡದಲ್ಲಿ ಸಂಪೂರ್ಣ ಉತ್ತರವನ್ನು ನೀಡುತ್ತದೆ</p>
          </div>
        </div>

        {/* Main Query Component */}
        <MultilingualFinancialQuery
          language="kannada"
          onResponse={handleResponse}
          onError={handleError}
        />

        {/* Query History */}
        {queryHistory.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              ಇತ್ತೀಚಿನ ಪ್ರಶ್ನೆಗಳು
            </h2>
            <div className="space-y-4">
              {queryHistory.map((item, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                  {item.translation_metadata && (
                    <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                      <strong>ಪ್ರಶ್ನೆ:</strong> {item.translation_metadata.original_query}
                    </div>
                  )}
                  {item.ai_response && (
                    <div className="text-gray-800 dark:text-gray-200">
                      <strong>ಉತ್ತರ:</strong>
                      <div className="mt-2 whitespace-pre-wrap">{item.ai_response}</div>
                    </div>
                  )}
                  {item.related_questions && item.related_questions.length > 0 && (
                    <div className="mt-4">
                      <strong className="text-gray-700 dark:text-gray-300">ಸಂಬಂಧಿತ ಪ್ರಶ್ನೆಗಳು:</strong>
                      <ul className="mt-2 list-disc list-inside text-gray-600 dark:text-gray-400">
                        {item.related_questions.map((question: string, qIndex: number) => (
                          <li key={qIndex}>{question}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sample Questions */}
        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            ಮಾದರಿ ಪ್ರಶ್ನೆಗಳು
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">ಹೂಡಿಕೆ ಬಗ್ಗೆ:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• ಸ್ಟಾಕ್ ಮಾರುಕಟ್ಟೆಯಲ್ಲಿ ಹೇಗೆ ಹೂಡಿಕೆ ಮಾಡಬೇಕು?</li>
                <li>• ಮ್ಯೂಚುಯಲ್ ಫಂಡ್‌ಗಳು ಎಂದರೇನು?</li>
                <li>• SIP ಮೂಲಕ ಹೂಡಿಕೆ ಹೇಗೆ ಮಾಡಬೇಕು?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">ಬ್ಯಾಂಕಿಂಗ್ ಬಗ್ಗೆ:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• ಮನೆ ಸಾಲ ಹೇಗೆ ತೆಗೆದುಕೊಳ್ಳಬೇಕು?</li>
                <li>• ಕ್ರೆಡಿಟ್ ಕಾರ್ಡ್ ಹೇಗೆ ಬಳಸಬೇಕು?</li>
                <li>• ಫಿಕ್ಸ್‌ಡ್ ಡಿಪಾಸಿಟ್ ಬಡ್ಡಿ ದರಗಳು ಎಷ್ಟು?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">ತೆರಿಗೆ ಬಗ್ಗೆ:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• ಆದಾಯ ತೆರಿಗೆ ಹೇಗೆ ಕಟ್ಟಬೇಕು?</li>
                <li>• GST ರಿಟರ್ನ್ ಹೇಗೆ ಫೈಲ್ ಮಾಡಬೇಕು?</li>
                <li>• ತೆರಿಗೆ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಯಾವುವು?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">ವಿಮೆ ಬಗ್ಗೆ:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• ಜೀವ ವಿಮೆ ಏಕೆ ಅವಶ್ಯಕ?</li>
                <li>• ಆರೋಗ್ಯ ವಿಮೆ ಹೇಗೆ ಆಯ್ಕೆ ಮಾಡಬೇಕು?</li>
                <li>• ವಾಹನ ವಿಮೆ ಹೇಗೆ ಮಾಡಿಸಿಕೊಳ್ಳಬೇಕು?</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KannadaFinancialPage;