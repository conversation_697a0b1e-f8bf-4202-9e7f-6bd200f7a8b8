import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiTranslate, PiChartLine } from 'react-icons/pi';
import Header from '@/components/Header';
import MultilingualFinancialQuery from '@/components/chatComponents/MultilingualFinancialQuery';

const TamilFinancialPage: React.FC = () => {
  const router = useRouter();
  const [showSidebar, setShowSidebar] = useState(false);
  const [queryHistory, setQueryHistory] = useState<any[]>([]);

  const handleBack = () => {
    router.back();
  };

  const handleResponse = (response: any) => {
    console.log('Tamil Financial Query Response:', response);
    // Add to history
    setQueryHistory(prev => [response, ...prev.slice(0, 4)]); // Keep last 5 queries
  };

  const handleError = (error: string) => {
    console.error('Tamil Financial Query Error:', error);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100 transition-colors"
              >
                <PiArrowLeft className="mr-2" />
                திரும்பிச் செல்
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                  <PiChartLine className="mr-3 text-blue-600" />
                  தமிழ் நிதி உதவியாளர்
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-1">
                  உங்கள் நிதி கேள்விகளை தமிழில் கேளுங்கள் மற்றும் தமிழில் பதில்களைப் பெறுங்கள்
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
            <PiTranslate className="mr-2" />
            எப்படி வேலை செய்கிறது
          </h2>
          <div className="text-blue-800 dark:text-blue-200 space-y-2">
            <p>• உங்கள் நிதி கேள்வியை தமிழில் தட்டச்சு செய்யவும்</p>
            <p>• எங்கள் அமைப்பு அதை ஆங்கிலத்தில் மொழிபெயர்க்கும்</p>
            <p>• நிதி தரவுத்தளத்தில் தேடும்</p>
            <p>• பதிலை மீண்டும் தமிழில் மொழிபெயர்க்கும்</p>
            <p>• உங்களுக்கு தமிழில் முழு பதிலை வழங்கும்</p>
          </div>
        </div>

        {/* Main Query Component */}
        <MultilingualFinancialQuery
          language="tamil"
          onResponse={handleResponse}
          onError={handleError}
        />

        {/* Query History */}
        {queryHistory.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              சமீபத்திய கேள்விகள்
            </h2>
            <div className="space-y-4">
              {queryHistory.map((item, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                  {item.translation_metadata && (
                    <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                      <strong>கேள்வி:</strong> {item.translation_metadata.original_query}
                    </div>
                  )}
                  {item.ai_response && (
                    <div className="text-gray-800 dark:text-gray-200">
                      <strong>பதில்:</strong>
                      <div className="mt-2 whitespace-pre-wrap">{item.ai_response}</div>
                    </div>
                  )}
                  {item.related_questions && item.related_questions.length > 0 && (
                    <div className="mt-4">
                      <strong className="text-gray-700 dark:text-gray-300">தொடர்புடைய கேள்விகள்:</strong>
                      <ul className="mt-2 list-disc list-inside text-gray-600 dark:text-gray-400">
                        {item.related_questions.map((question: string, qIndex: number) => (
                          <li key={qIndex}>{question}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sample Questions */}
        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            மாதிரி கேள்விகள்
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">முதலீடு பற்றி:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• பங்குச் சந்தையில் எப்படி முதலீடு செய்வது?</li>
                <li>• மியூச்சுவல் ஃபண்ட்ஸ் என்றால் என்ன?</li>
                <li>• SIP மூலம் எப்படி முதலீடு செய்வது?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">வங்கி பற்றி:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• வீடு கடன் எப்படி பெறுவது?</li>
                <li>• கிரெடிட் கார்டு எப்படி பயன்படுத்துவது?</li>
                <li>• ஃபிக்ஸ்ட் டெபாசிட் வட்டி விகிதங்கள் எவ்வளவு?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">வரி பற்றி:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• வருமான வரி எப்படி செலுத்துவது?</li>
                <li>• GST ரிட்டர்ன் எப்படி தாக்கல் செய்வது?</li>
                <li>• வரி சேமிப்பு திட்டங்கள் எவை?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">காப்பீடு பற்றி:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• லைப் இன்ஷூரன்ஸ் ஏன் தேவை?</li>
                <li>• ஹெல்த் இன்ஷூரன்ஸ் எப்படி தேர்வு செய்வது?</li>
                <li>• வாகன காப்பீடு எப்படி செய்வது?</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TamilFinancialPage;
