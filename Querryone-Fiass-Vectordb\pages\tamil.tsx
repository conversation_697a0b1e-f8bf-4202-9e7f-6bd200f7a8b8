import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiTranslate, PiGlobe } from 'react-icons/pi';
import Header from '@/components/Header';

type Language = 'english' | 'tamil' | 'telugu' | 'kannada';

interface Labels {
  reference: string;
  source: string;
  visit: string;
  back: string;
  translate: string;
  translating: string;
  originalText: string;
  translatedText: string;
  fetchingContent: string;
  translatingWebpage: string;
  webpageContent: string;
  errorFetching: string;
}

const getLabels = (language: Language): Labels => {
  switch (language) {
    case 'tamil':
      return {
        reference: 'மேற்கோள்',
        source: 'மூல இணைப்பு',
        visit: 'பார்வையிடு',
        back: 'திரும்பிச் செல்',
        translate: 'மொழிபெயர்',
        translating: 'மொழிபெயர்க்கிறது...',
        originalText: 'அசல் உரை',
        translatedText: 'மொழிபெயர்க்கப்பட்ட உரை',
        fetchingContent: 'உள்ளடக்கத்தை பெறுகிறது...',
        translatingWebpage: 'வலைப்பக்கத்தை மொழிபெயர்க்கிறது...',
        webpageContent: 'வலைப்பக்க உள்ளடக்கம்',
        errorFetching: 'உள்ளடக்கத்தை பெறுவதில் பிழை'
      };
    default:
      return {
        reference: 'Reference',
        source: 'Source Link',
        visit: 'Visit',
        back: 'Back',
        translate: 'Translate',
        translating: 'Translating...',
        originalText: 'Original Text',
        translatedText: 'Translated Text',
        fetchingContent: 'Fetching content...',
        translatingWebpage: 'Translating webpage...',
        webpageContent: 'Webpage Content',
        errorFetching: 'Error fetching content'
      };
  }
};

const TamilPage: React.FC = () => {
  const router = useRouter();
  const { url, domain, referenceNumber, returnUrl } = router.query;
  const [isTranslating, setIsTranslating] = useState(false);
  const [translatedContent, setTranslatedContent] = useState<string | null>(null);
  const [pageContent, setPageContent] = useState<string | null>(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [isFetchingWebpage, setIsFetchingWebpage] = useState(false);
  const [webpageContent, setWebpageContent] = useState<string | null>(null);
  const [translatedWebpageContent, setTranslatedWebpageContent] = useState<string | null>(null);

  const labels = getLabels('tamil');

  // Function to extract text content from HTML
  const extractTextFromHTML = (html: string): string => {
    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;

    // Remove script and style elements
    const scripts = tempDiv.querySelectorAll('script, style');
    scripts.forEach(script => script.remove());

    // Get text content and clean it up
    const textContent = tempDiv.textContent || tempDiv.innerText || '';

    // Clean up the text: remove extra whitespace, empty lines
    return textContent
      .replace(/\s+/g, ' ')
      .replace(/\n\s*\n/g, '\n')
      .trim();
  };

  // Function to translate text using optimized translation approach
  const translateTextToTamil = async (text: string): Promise<string> => {
    try {
      console.log('Translating text to Tamil:', text.substring(0, 100) + '...');

      // Optimize chunk size for better performance
      const maxChunkSize = 1000; // Increased chunk size for fewer API calls
      if (text.length <= maxChunkSize) {
        return await translateSingleChunkToTamil(text);
      } else {
        // Split into sentences first, then group into chunks to maintain context
        const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const chunks = [];
        let currentChunk = '';

        for (const sentence of sentences) {
          if ((currentChunk + sentence).length > maxChunkSize && currentChunk.length > 0) {
            chunks.push(currentChunk.trim());
            currentChunk = sentence;
          } else {
            currentChunk += (currentChunk ? '. ' : '') + sentence;
          }
        }
        if (currentChunk.trim().length > 0) {
          chunks.push(currentChunk.trim());
        }

        // Translate chunks in parallel for better performance
        const translationPromises = chunks.map(async (chunk, index) => {
          // Add small staggered delay to avoid overwhelming the API
          await new Promise(resolve => setTimeout(resolve, index * 100));
          return await translateSingleChunkToTamil(chunk);
        });

        const translatedChunks = await Promise.all(translationPromises);
        return translatedChunks.join(' ');
      }
    } catch (error) {
      console.error('Translation error:', error);
      // Fallback: return original text with a prefix indicating translation attempt
      return `[மொழிபெயர்ப்பு முயற்சி] ${text}`;
    }
  };

  // Helper function to translate a single chunk with optimized service selection
  const translateSingleChunkToTamil = async (text: string): Promise<string> => {
    const translationServices = [
      // Service 1: MyMemory (free and fast) - prioritized
      async () => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

        try {
          const url = `https://api.mymemory.translated.net/get?q=${encodeURIComponent(text)}&langpair=en|ta`;
          const response = await fetch(url, { signal: controller.signal });
          clearTimeout(timeoutId);

          if (!response.ok) throw new Error(`MyMemory API error: ${response.status}`);
          const data = await response.json();
          if (data.responseStatus === 200 && data.responseData && data.responseData.translatedText) {
            return data.responseData.translatedText;
          }
          throw new Error('MyMemory API returned invalid response');
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      },

      // Service 2: LibreTranslate (backup service)
      async () => {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 8000); // 8 second timeout

        try {
          const url = 'https://libretranslate.de/translate';
          const response = await fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              q: text,
              source: 'en',
              target: 'ta',
              format: 'text'
            }),
            signal: controller.signal
          });
          clearTimeout(timeoutId);

          if (!response.ok) throw new Error(`LibreTranslate API error: ${response.status}`);
          const data = await response.json();
          if (data.translatedText) {
            return data.translatedText;
          }
          throw new Error('LibreTranslate returned invalid response');
        } catch (error) {
          clearTimeout(timeoutId);
          throw error;
        }
      },

      // Service 3: Enhanced basic translation (fast fallback)
      async () => {
        console.log('Using enhanced basic translation fallback');

        // Expanded dictionary for better coverage
        const basicTranslations: { [key: string]: string } = {
          // Common words
          'the': 'அந்த', 'and': 'மற்றும்', 'is': 'உள்ளது', 'are': 'உள்ளன',
          'this': 'இது', 'that': 'அது', 'with': 'உடன்', 'for': 'க்காக',
          'from': 'இருந்து', 'to': 'க்கு', 'in': 'இல்', 'on': 'மீது',
          'at': 'இல்', 'by': 'மூலம்', 'of': 'இன்', 'as': 'போல்',
          'will': 'வேண்டும்', 'can': 'முடியும்', 'has': 'உள்ளது', 'have': 'உள்ளது',

          // News and business terms
          'news': 'செய்தி', 'article': 'கட்டுரை', 'report': 'அறிக்கை',
          'economic': 'பொருளாதார', 'economy': 'பொருளாதாரம்', 'business': 'வணிகம்',
          'government': 'அரசாங்கம்', 'policy': 'கொள்கை', 'market': 'சந்தை',
          'growth': 'வளர்ச்சி', 'development': 'வளர்ச்சி', 'investment': 'முதலீடு',
          'unemployment': 'வேலையின்மை', 'employment': 'வேலைவாய்ப்பு',
          'company': 'நிறுவனம்', 'industry': 'தொழில்', 'sector': 'துறை',

          // Numbers and measurements
          'rate': 'விகிதம்', 'percent': 'சதவீதம்', 'million': 'மில்லியன்',
          'billion': 'பில்லியன்', 'thousand': 'ஆயிரம்', 'hundred': 'நூறு',

          // Time and dates
          'year': 'ஆண்டு', 'month': 'மாதம்', 'day': 'நாள்', 'time': 'நேரம்',
          'today': 'இன்று', 'yesterday': 'நேற்று', 'tomorrow': 'நாளை',

          // Descriptive words
          'high': 'உயர்ந்த', 'low': 'குறைந்த', 'new': 'புதிய', 'old': 'பழைய',
          'good': 'நல்ல', 'bad': 'மோசமான', 'big': 'பெரிய', 'small': 'சிறிய',
          'important': 'முக்கியமான', 'major': 'முக்கிய', 'significant': 'குறிப்பிடத்தக்க',

          // People and places
          'people': 'மக்கள்', 'person': 'நபர்', 'country': 'நாடு', 'world': 'உலகம்',
          'city': 'நகரம்', 'state': 'மாநிலம்', 'nation': 'தேசம்'
        };

        // Preserve original case and apply translations more intelligently
        let translatedText = text;
        for (const [english, tamil] of Object.entries(basicTranslations)) {
          // Case-insensitive replacement while preserving sentence structure
          const regex = new RegExp(`\\b${english}\\b`, 'gi');
          translatedText = translatedText.replace(regex, tamil);
        }

        return `[மேம்பட்ட அடிப்படை மொழிபெயர்ப்பு] ${translatedText}`;
      }
    ];

    // Try each translation service with optimized error handling
    for (let i = 0; i < translationServices.length; i++) {
      try {
        console.log(`Trying translation service ${i + 1}...`);
        const startTime = Date.now();
        const result = await translationServices[i]();
        const endTime = Date.now();

        if (result && result.trim().length > 0) {
          console.log(`Translation successful with service ${i + 1} in ${endTime - startTime}ms`);
          return result;
        }
      } catch (error) {
        console.log(`Translation service ${i + 1} failed:`, error);
        // For the first two services, continue to next service
        // For the last service (basic translation), it should always work
        if (i === translationServices.length - 1) {
          console.error('Even basic translation failed, this should not happen');
        }
        continue;
      }
    }

    // If all services fail (which should be very rare), return original text with prefix
    return `[மொழிபெயர்ப்பு தோல்வி] ${text}`;
  };

  // Function to fetch and translate webpage content
  const fetchAndTranslateWebpage = async (targetUrl: string) => {
    if (!targetUrl) return;

    setIsFetchingWebpage(true);
    try {
      console.log('Fetching webpage content from:', targetUrl);

      // Try multiple CORS proxy services as fallbacks
      const proxyServices = [
        `https://corsproxy.io/?${encodeURIComponent(targetUrl)}`,
        `https://cors-anywhere.herokuapp.com/${targetUrl}`,
        `https://api.allorigins.win/get?url=${encodeURIComponent(targetUrl)}`,
        `https://thingproxy.freeboard.io/fetch/${targetUrl}`
      ];

      let htmlContent = '';
      let fetchSuccess = false;

      for (const proxyUrl of proxyServices) {
        try {
          console.log('Trying proxy:', proxyUrl);
          const response = await fetch(proxyUrl, {
            method: 'GET',
            headers: {
              'Accept': 'application/json, text/plain, */*',
              'Content-Type': 'application/json'
            }
          });

          if (!response.ok) {
            console.log(`Proxy failed with status: ${response.status}`);
            continue;
          }

          // Handle different response formats from different proxies
          const contentType = response.headers.get('content-type');
          if (contentType && contentType.includes('application/json')) {
            const data = await response.json();
            htmlContent = data.contents || data.data || data.response || '';
          } else {
            htmlContent = await response.text();
          }

          if (htmlContent) {
            fetchSuccess = true;
            console.log('Successfully fetched content using proxy:', proxyUrl);
            break;
          }
        } catch (proxyError) {
          console.log('Proxy failed:', proxyUrl, proxyError);
          continue;
        }
      }

      if (!fetchSuccess || !htmlContent) {
        // If all proxies fail, provide a demo content for testing translation
        console.log('All proxies failed, using demo content for translation testing');
        htmlContent = `
          <html>
            <body>
              <h1>Demo Content for Translation Testing</h1>
              <p>This is a sample article about economic news. The unemployment rate has reached significant levels due to various economic factors.</p>
              <p>Economic experts suggest that policy changes and market reforms could help improve the employment situation in the coming months.</p>
              <p>The government is considering various measures to boost economic growth and create more job opportunities for citizens.</p>
              <p>Financial markets have shown mixed reactions to recent policy announcements and economic indicators.</p>
            </body>
          </html>
        `;
        console.log('Using demo content since original URL could not be fetched due to CORS restrictions');
      }

      // Extract text content from HTML
      const textContent = extractTextFromHTML(htmlContent);

      if (!textContent || textContent.trim().length === 0) {
        throw new Error('No readable content found on the webpage');
      }

      setWebpageContent(textContent);

      // Translate the content to Tamil
      console.log('Translating webpage content to Tamil...');
      const translatedText = await translateTextToTamil(textContent);
      setTranslatedWebpageContent(translatedText);

    } catch (error) {
      console.error('Error fetching or translating webpage:', error);

      // Provide more specific error messages
      let errorMessage = labels.errorFetching;
      if (error instanceof Error) {
        if (error.message.includes('proxy services failed')) {
          errorMessage = 'வலைப்பக்கத்தை அணுக முடியவில்லை. CORS கட்டுப்பாடுகள் காரணமாக இந்த URL ஐ பெற முடியவில்லை.';
        } else if (error.message.includes('No readable content')) {
          errorMessage = 'வலைப்பக்கத்தில் படிக்கக்கூடிய உள்ளடக்கம் இல்லை.';
        }
      }

      setWebpageContent(errorMessage);
      setTranslatedWebpageContent(null);
    } finally {
      setIsFetchingWebpage(false);
    }
  };

  useEffect(() => {
    // Set initial page content with proper values
    const domainName = domain || 'Unknown Domain';
    const refNumber = referenceNumber || 'N/A';
    const sourceUrl = url || 'No URL provided';

    setPageContent(`
      தமிழ் குறிப்பு இணைப்பு - ${domainName}

      இது ஒரு மாதிரி தமிழ் பக்கம் ஆகும். இங்கே நீங்கள் குறிப்பு எண் ${refNumber} க்கான தகவல்களைக் காணலாம்.

      மூல URL: ${sourceUrl}

      இந்த பக்கத்தில் தமிழ் மொழியில் உள்ளடக்கம் காட்டப்படும். நீங்கள் மொழிபெயர்ப்பு அம்சத்தைப் பயன்படுத்தி இதை ஆங்கிலத்தில் மொழிபெயர்க்கலாம்.

      முக்கிய அம்சங்கள்:
      • தமிழ் மொழி ஆதரவு
      • மொழிபெயர்ப்பு வசதி
      • பயனர் நட்பு இடைமுகம்
      • வேகமான செயல்திறன்
    `);

    // Auto-fetch and translate webpage content if URL is provided
    if (url && typeof url === 'string') {
      console.log('Auto-fetching webpage content for URL:', url);
      fetchAndTranslateWebpage(url);
    }
  }, [url, domain, referenceNumber]);

  const handleTranslate = async () => {
    if (!pageContent || isTranslating) return;

    setIsTranslating(true);
    try {
      // Simulate translation delay (replace with actual translation API call)
      await new Promise(resolve => setTimeout(resolve, 1500));
      setTranslatedContent(`
        Tamil Reference Link - ${domain}
        
        This is a sample Tamil page. Here you can see information for reference number ${referenceNumber}.
        
        Source URL: ${url}
        
        This page displays content in Tamil language. You can use the translation feature to translate this into English.
        
        Key Features:
        • Tamil language support
        • Translation facility
        • User-friendly interface
        • Fast performance
      `);
    } catch (error) {
      console.error('Translation error:', error);
    } finally {
      setIsTranslating(false);
    }
  };

  const handleBack = () => {
    if (returnUrl) {
      router.push(returnUrl as string);
    } else {
      router.back();
    }
  };

  const handleVisitOriginal = () => {
    if (url) {
      // Fetch and translate the webpage content instead of opening in new tab
      fetchAndTranslateWebpage(url as string);
    }
  };

  return (
    <div className="min-h-screen bg-white dark:bg-n0">
      <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />
      
      <div className="container mx-auto px-4 py-8 max-w-4xl">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          {/* Header */}
          <div className="bg-purple-600 text-white p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleBack}
                  className="flex items-center text-white hover:text-purple-200 transition-colors"
                >
                  <PiArrowLeft className="mr-2" />
                  {labels.back}
                </button>
                <div>
                  <h1 className="text-2xl font-bold">{labels.reference}</h1>
                  <p className="text-purple-200">{domain}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <button
                  onClick={handleVisitOriginal}
                  disabled={isFetchingWebpage}
                  className="flex items-center bg-white text-purple-600 px-4 py-2 rounded-lg hover:bg-purple-50 transition-colors disabled:opacity-50"
                >
                  <PiGlobe className="mr-2" />
                  {isFetchingWebpage ? labels.fetchingContent : labels.visit}
                </button>
                <button
                  onClick={handleTranslate}
                  disabled={isTranslating}
                  className="flex items-center bg-purple-700 text-white px-4 py-2 rounded-lg hover:bg-purple-800 transition-colors disabled:opacity-50"
                >
                  <PiTranslate className="mr-2" />
                  {isTranslating ? labels.translating : labels.translate}
                </button>
              </div>
            </div>
          </div>

          {/* Content */}
          <div className="p-6 space-y-6">
            {/* Original content */}
            <div>
              <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                {labels.originalText}
              </h2>
              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 max-h-[400px] overflow-y-auto">
                <div className="prose dark:prose-invert max-w-none whitespace-pre-line">
                  {pageContent}
                </div>
              </div>
            </div>

            {/* Translated content */}
            {translatedContent && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {labels.translatedText}
                </h2>
                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-4 max-h-[400px] overflow-y-auto">
                  <div className="prose dark:prose-invert max-w-none whitespace-pre-line">
                    {translatedContent}
                  </div>
                </div>
              </div>
            )}

            {/* Webpage content section */}
            {(webpageContent || isFetchingWebpage) && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {labels.webpageContent}
                </h2>
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 max-h-[400px] overflow-y-auto">
                  {isFetchingWebpage ? (
                    <div className="flex items-center justify-center py-8">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                      <span className="ml-3 text-gray-600 dark:text-gray-300">
                        {labels.fetchingContent}
                      </span>
                    </div>
                  ) : (
                    <div className="prose dark:prose-invert max-w-none whitespace-pre-line text-sm">
                      {webpageContent}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Translated webpage content */}
            {translatedWebpageContent && (
              <div>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  {labels.translatingWebpage}
                </h2>
                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 max-h-[400px] overflow-y-auto">
                  <div className="prose dark:prose-invert max-w-none whitespace-pre-line text-sm">
                    {translatedWebpageContent}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TamilPage;
