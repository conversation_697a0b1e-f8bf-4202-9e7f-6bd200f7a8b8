import React, { useState } from 'react';
import { useRouter } from 'next/router';
import { PiArrowLeft, PiTranslate, PiChartLine } from 'react-icons/pi';
import Header from '@/components/Header';
import MultilingualFinancialQuery from '@/components/chatComponents/MultilingualFinancialQuery';

const TeluguFinancialPage: React.FC = () => {
  const router = useRouter();
  const [showSidebar, setShowSidebar] = useState(false);
  const [queryHistory, setQueryHistory] = useState<any[]>([]);

  const handleBack = () => {
    router.back();
  };

  const handleResponse = (response: any) => {
    console.log('Telugu Financial Query Response:', response);
    // Add to history
    setQueryHistory(prev => [response, ...prev.slice(0, 4)]); // Keep last 5 queries
  };

  const handleError = (error: string) => {
    console.error('Telugu Financial Query Error:', error);
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Header showSidebar={showSidebar} setShowSidebar={setShowSidebar} />
      
      <div className="container mx-auto px-4 py-8">
        {/* Page Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-800 dark:text-gray-300 dark:hover:text-gray-100 transition-colors"
              >
                <PiArrowLeft className="mr-2" />
                వెనుకకు
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center">
                  <PiChartLine className="mr-3 text-blue-600" />
                  తెలుగు ఆర్థిక సహాయకుడు
                </h1>
                <p className="text-gray-600 dark:text-gray-300 mt-1">
                  మీ ఆర్థిక ప్రశ్నలను తెలుగులో అడగండి మరియు తెలుగులోనే సమాధానాలు పొందండి
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="mb-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
            <PiTranslate className="mr-2" />
            ఎలా పని చేస్తుంది
          </h2>
          <div className="text-blue-800 dark:text-blue-200 space-y-2">
            <p>• మీ ఆర్థిక ప్రశ్నను తెలుగులో టైప్ చేయండి</p>
            <p>• మా సిస్టమ్ దానిని ఆంగ్లంలోకి అనువదిస్తుంది</p>
            <p>• ఆర్థిక డేటాబేస్‌లో శోధిస్తుంది</p>
            <p>• సమాధానాన్ని తిరిగి తెలుగులోకి అనువదిస్తుంది</p>
            <p>• మీకు తెలుగులో పూర్తి సమాధానం అందిస్తుంది</p>
          </div>
        </div>

        {/* Main Query Component */}
        <MultilingualFinancialQuery
          language="telugu"
          onResponse={handleResponse}
          onError={handleError}
        />

        {/* Query History */}
        {queryHistory.length > 0 && (
          <div className="mt-12">
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
              ఇటీవలి ప్రశ్నలు
            </h2>
            <div className="space-y-4">
              {queryHistory.map((item, index) => (
                <div key={index} className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                  {item.translation_metadata && (
                    <div className="mb-4 text-sm text-gray-600 dark:text-gray-400">
                      <strong>ప్రశ్న:</strong> {item.translation_metadata.original_query}
                    </div>
                  )}
                  {item.ai_response && (
                    <div className="text-gray-800 dark:text-gray-200">
                      <strong>సమాధానం:</strong>
                      <div className="mt-2 whitespace-pre-wrap">{item.ai_response}</div>
                    </div>
                  )}
                  {item.related_questions && item.related_questions.length > 0 && (
                    <div className="mt-4">
                      <strong className="text-gray-700 dark:text-gray-300">సంబంధిత ప్రశ్నలు:</strong>
                      <ul className="mt-2 list-disc list-inside text-gray-600 dark:text-gray-400">
                        {item.related_questions.map((question: string, qIndex: number) => (
                          <li key={qIndex}>{question}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Sample Questions */}
        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            నమూనా ప్రశ్నలు
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">పెట్టుబడి గురించి:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• స్టాక్ మార్కెట్‌లో ఎలా పెట్టుబడి పెట్టాలి?</li>
                <li>• మ్యూచువల్ ఫండ్స్ అంటే ఏమిటి?</li>
                <li>• SIP ద్వారా పెట్టుబడి ఎలా చేయాలి?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">బ్యాంకింగ్ గురించి:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• హోమ్ లోన్ ఎలా తీసుకోవాలి?</li>
                <li>• క్రెడిట్ కార్డ్ ఎలా వాడాలి?</li>
                <li>• ఫిక్స్‌డ్ డిపాజిట్ వడ్డీ రేట్లు ఎంత?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">పన్నుల గురించి:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• ఇన్కమ్ టాక్స్ ఎలా కట్టాలి?</li>
                <li>• GST రిటర్న్ ఎలా ఫైల్ చేయాలి?</li>
                <li>• టాక్స్ సేవింగ్ స్కీమ్స్ ఏవి?</li>
              </ul>
            </div>
            <div className="space-y-2">
              <h3 className="font-medium text-gray-800 dark:text-gray-200">బీమా గురించి:</h3>
              <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                <li>• లైఫ్ ఇన్షూరెన్స్ ఎందుకు అవసరం?</li>
                <li>• హెల్త్ ఇన్షూరెన్స్ ఎలా ఎంచుకోవాలి?</li>
                <li>• వాహన బీమా ఎలా చేయించుకోవాలి?</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TeluguFinancialPage;