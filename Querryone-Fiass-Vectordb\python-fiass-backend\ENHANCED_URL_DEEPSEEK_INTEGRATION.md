# Enhanced URL Handling and DeepSeek API Integration

## Overview

This document describes the enhanced functionality implemented for handling URLs when they are N/A and the DeepSeek API integration for generating proper related questions.

## Key Features Implemented

### 1. Enhanced URL Handling

When a document's URL field is "N/A" or empty, the system now:

- **Uses `file_uploaded` as the URL**: Provides the filename as the source reference
- **Uses `vector_id` as the source title**: Provides the unique identifier as the source reference
- **Sets source type to 'excel_upload'**: Indicates this content came from an uploaded Excel file

#### Implementation Details

**Location**: `python-fiass-backend/full_code.py` - `enrich_ai_response_with_urls()` function
**Location**: `python-fiass-backend/tamil-fiass/api-tamil-fiass.py` - `enrich_ai_response_with_urls()` function

```python
# Handle case when URL is N/A - use file_uploaded as URL and vector_id as source
if source_url == "N/A" or not source_url:
    file_uploaded = top_match.get("file_uploaded")
    vector_id = top_match.get("vector_id")
    
    if file_uploaded:
        source_url = file_uploaded
        print(f"   🔄 URL was N/A, using file_uploaded as URL: {file_uploaded}")
        
        # Update source_title to use vector_id if available
        if vector_id:
            source_title = vector_id
            print(f"   🔄 Using vector_id as source: {vector_id}")
        
        # Update source_type to indicate this is from uploaded file
        if source_type == "unknown":
            source_type = "excel_upload"
```

### 2. DeepSeek API Integration for Related Questions

The system uses the DeepSeek API to generate contextually relevant related questions:

#### Features:
- **Multi-language support**: Generates questions in English, Tamil, Telugu, and Kannada
- **Contextual relevance**: Uses the original query and AI response to generate related questions
- **Fallback mechanism**: Provides default questions if API fails
- **Error handling**: Gracefully handles API errors, network issues, and authentication problems

#### Implementation Details

**Location**: `python-fiass-backend/full_code.py` - `generate_related_questions()` function

```python
def generate_related_questions(query, answer, selected_language="English"):
    """
    Generate related questions using DeepSeek API.
    
    Args:
        query: The original question
        answer: The AI-generated answer
        selected_language: The language for generating questions (English/Tamil)
    
    Returns:
        List of related questions (up to 7 questions)
    """
```

#### Language-Specific Prompts:

**English Prompt**:
```
Based on the following question and answer, generate 7 contextually relevant follow-up questions.

QUESTION: {query}
ANSWER: {answer}

RELATED QUESTIONS:
1.
```

**Tamil Prompt**:
```
கொடுக்கப்பட்ட கேள்வி மற்றும் பதிலின் அடிப்படையில், 7 சூழல் சார்ந்த தொடர்ச்சி கேள்விகளை உருவாக்கவும். கேள்விகள் தமிழில் மட்டுமே இருக்க வேண்டும்.

கேள்வி: {query}
பதில்: {answer}

தொடர்புடைய கேள்விகள்:
1.
```

### 3. Error Handling and Fallback

#### API Error Types Handled:
- **401 Unauthorized**: Invalid API key
- **402 Payment Required**: Insufficient balance
- **Network errors**: Timeout and connection issues
- **General errors**: Any other API failures

#### Fallback Questions:

**English Fallback**:
1. What are the key points from this information?
2. Can you provide more details about this topic?
3. What are the implications of this information?
4. How does this relate to current trends?
5. What are the potential benefits and drawbacks?
6. What additional research would be helpful?
7. How can this information be applied practically?

**Tamil Fallback**:
1. இந்த தகவலின் முக்கிய அம்சங்கள் என்ன?
2. இந்த தலைப்பு பற்றி மேலும் விவரங்கள் தர முடியுமா?
3. இந்த தகவலின் தாக்கங்கள் என்ன?
4. இது தற்போதைய போக்குகளுடன் எவ்வாறு தொடர்புடையது?
5. இதன் சாத்தியமான நன்மைகள் மற்றும் தீமைகள் என்ன?
6. என்ன கூடுதல் ஆராய்ச்சி உதவியாக இருக்கும்?
7. இந்த தகவலை நடைமுறையில் எவ்வாறு பயன்படுத்தலாம்?

## Configuration

### Environment Variables Required:
- `DEEPSEEK_API_KEY`: Your DeepSeek API key for generating related questions

### API Endpoint:
- **Base URL**: `https://api.deepseek.com`
- **Model**: `deepseek-chat`
- **Max Tokens**: 300
- **Temperature**: 0.7

## Testing

### Test Files Created:
1. `test_url_handling.py`: Tests URL handling logic
2. `test_deepseek_integration.py`: Tests DeepSeek API integration

### Test Results:
✅ URL handling works correctly for N/A URLs
✅ DeepSeek API generates contextual questions in multiple languages
✅ Fallback mechanisms work when API is unavailable
✅ Error handling is robust and user-friendly

## Usage Example

When a user queries the system and the response contains references to documents with N/A URLs:

**Before Enhancement**:
```json
{
  "sentence": "AI is transforming industries.",
  "url": "N/A",
  "summary": "No summary found"
}
```

**After Enhancement**:
```json
{
  "sentence": "AI is transforming industries.",
  "url": "financial_data_2024.xlsx",
  "summary": "Source: Excel Upload - news-123-chunk-0 | Technology trends analysis",
  "source_type": "excel_upload",
  "source_title": "news-123-chunk-0"
}
```

## Benefits

1. **Better Source Attribution**: Users can identify the source file even when URLs are not available
2. **Improved User Experience**: Related questions help users explore topics more deeply
3. **Multi-language Support**: Works seamlessly with Tamil, Telugu, Kannada, and English
4. **Robust Error Handling**: System continues to work even when external APIs fail
5. **Contextual Relevance**: Generated questions are based on the actual query and response content

## Files Modified

1. `python-fiass-backend/full_code.py`
   - Enhanced `enrich_ai_response_with_urls()` function
   - Improved `generate_related_questions()` function

2. `python-fiass-backend/tamil-fiass/api-tamil-fiass.py`
   - Enhanced `enrich_ai_response_with_urls()` function

## Future Enhancements

1. **Caching**: Implement caching for frequently generated questions
2. **Question Quality Scoring**: Add scoring mechanism for generated questions
3. **User Feedback**: Allow users to rate question relevance
4. **Additional Languages**: Extend support to more regional languages
