"""
Compatibility module for Python 3.13+ to provide the removed cgi module functionality.
This is a minimal implementation to support packages that still depend on cgi.
"""

import urllib.parse
from typing import Dict, List, Union, Optional, Any
import io
import sys

# Constants from the original cgi module
maxlen = 0

class FieldStorage:
    """A minimal implementation of cgi.FieldStorage for compatibility."""
    
    def __init__(self, fp=None, headers=None, outerboundary=b"", 
                 environ=None, keep_blank_values=0, strict_parsing=0,
                 limit=None, encoding='utf-8', errors='replace', 
                 max_num_fields=None, separator='&'):
        self.list = []
        self.file = None
        self.filename = None
        self.name = None
        self.value = None
        self.type = None
        self.type_options = {}
        self.disposition = None
        self.disposition_options = {}
        self.headers = headers or {}
        
    def __getitem__(self, key):
        """Dictionary-style access."""
        for item in self.list:
            if item.name == key:
                return item
        raise KeyError(key)
        
    def getvalue(self, key, default=None):
        """Get the value of a field."""
        try:
            return self[key].value
        except KeyError:
            return default
            
    def getlist(self, key):
        """Get all values for a field as a list."""
        return [item.value for item in self.list if item.name == key]

def parse_qs(qs, keep_blank_values=False, strict_parsing=False, 
             encoding='utf-8', errors='replace', max_num_fields=None, separator='&'):
    """Parse a query string into a dictionary."""
    return urllib.parse.parse_qs(qs, keep_blank_values, strict_parsing, 
                                encoding, errors, max_num_fields, separator)

def parse_qsl(qs, keep_blank_values=False, strict_parsing=False,
              encoding='utf-8', errors='replace', max_num_fields=None, separator='&'):
    """Parse a query string into a list of (name, value) pairs."""
    return urllib.parse.parse_qsl(qs, keep_blank_values, strict_parsing,
                                 encoding, errors, max_num_fields, separator)

def escape(s, quote=False):
    """Escape special characters in HTML."""
    s = s.replace("&", "&amp;")
    s = s.replace("<", "&lt;")
    s = s.replace(">", "&gt;")
    if quote:
        s = s.replace('"', "&quot;")
        s = s.replace("'", "&#x27;")
    return s

def parse_header(line):
    """Parse a Content-Type like header."""
    parts = line.split(';')
    main_type = parts[0].strip()
    pdict = {}
    for p in parts[1:]:
        if '=' in p:
            name, value = p.split('=', 1)
            name = name.strip().lower()
            value = value.strip()
            if value.startswith('"') and value.endswith('"'):
                value = value[1:-1]
            pdict[name] = value
    return main_type, pdict

def parse_multipart(fp, pdict, encoding='utf-8', errors='replace', separator='&'):
    """Parse multipart data (minimal implementation)."""
    return []

# For backward compatibility
def print_exception(type=None, value=None, tb=None, limit=None, file=None):
    """Print exception information."""
    import traceback
    if file is None:
        file = sys.stderr
    traceback.print_exception(type, value, tb, limit, file)

def print_environ(environ=None):
    """Print the environment."""
    import os
    if environ is None:
        environ = os.environ
    for key, value in environ.items():
        print(f"{key}={value}")

def print_form(form):
    """Print form data."""
    for key in form:
        print(f"{key}: {form.getvalue(key)}")

def print_directory():
    """Print directory listing."""
    pass

def print_arguments():
    """Print command line arguments."""
    print(sys.argv)

def print_environ_usage():
    """Print environment usage."""
    pass

# Additional compatibility functions
def valid_boundary(s, _vb_pattern=None):
    """Check if boundary is valid."""
    import re
    if _vb_pattern is None:
        _vb_pattern = re.compile(rb"^[ -~]{0,200}[!-~]$")
    return _vb_pattern.match(s)
