#!/usr/bin/env python3
"""
Enhanced Features Demo Script
Demonstrates the improved Character Diversity Analysis and Related Questions Generation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Import the enhanced functions
from full_code import (
    calculate_script_aware_char_diversity,
    detect_text_corruption,
    generate_related_questions,
    DEEPSEEK_API_KEY
)

def demo_character_diversity():
    """Demonstrate enhanced character diversity analysis"""
    print("🔍 Enhanced Character Diversity Analysis Demo")
    print("=" * 50)
    
    test_texts = [
        {
            "name": "Clean English Text",
            "text": "The financial markets are experiencing unprecedented growth with technology stocks leading the surge."
        },
        {
            "name": "Clean Tamil Text",
            "text": "நிதிச் சந்தைகள் முன்னோடியில்லாத வளர்ச்சியை அனுபவித்து வருகின்றன. தொழில்நுட்ப பங்குகள் உயர்வுக்கு வழிவகுக்கின்றன."
        },
        {
            "name": "Corrupted Text (Word Repetition)",
            "text": "The the market market is is showing showing good good performance performance with with strong strong indicators indicators."
        },
        {
            "name": "Low Diversity Text",
            "text": "aaaa bbbb cccc dddd eeee ffff gggg hhhh iiii jjjj kkkk llll mmmm nnnn oooo pppp"
        },
        {
            "name": "Mixed Script Text",
            "text": "The market முதலீடு shows good performance మంచి results in crypto ಕ್ರಿಪ್ಟೋ investments."
        }
    ]
    
    for test_case in test_texts:
        print(f"\n📝 Testing: {test_case['name']}")
        print(f"Text: {test_case['text'][:60]}...")
        
        # Perform enhanced analysis
        diversity_analysis = calculate_script_aware_char_diversity(test_case['text'])
        is_corrupted, cleaned_text, corruption_details = detect_text_corruption(test_case['text'], diversity_analysis)
        
        print(f"📊 Analysis Results:")
        print(f"   Script Type: {diversity_analysis['script_type']}")
        print(f"   Basic Diversity: {diversity_analysis['diversity_ratio']:.3f}")
        print(f"   Normalized Diversity: {diversity_analysis['normalized_diversity']:.3f}")
        print(f"   Complexity Score: {diversity_analysis['complexity_score']:.3f}")
        print(f"   Entropy: {diversity_analysis['char_distribution']['entropy']:.3f}")
        
        print(f"🔍 Corruption Detection:")
        print(f"   Status: {'❌ Corrupted' if is_corrupted else '✅ Clean'}")
        print(f"   Corruption Score: {corruption_details['corruption_score']:.3f}")
        print(f"   Confidence: {corruption_details['confidence']:.3f}")
        
        if corruption_details['indicators']:
            print(f"   Indicators: {[ind['type'] for ind in corruption_details['indicators']]}")
        
        if is_corrupted and cleaned_text != test_case['text']:
            print(f"   Cleaned Text: {cleaned_text[:60]}...")

def demo_related_questions():
    """Demonstrate enhanced related questions generation"""
    print("\n\n🤔 Enhanced Related Questions Generation Demo")
    print("=" * 50)
    
    if not DEEPSEEK_API_KEY:
        print("⚠️ DEEPSEEK_API_KEY not configured. Using fallback questions.")
    
    test_scenarios = [
        {
            "name": "English Financial Query",
            "query": "What are the best investment strategies for retirement planning?",
            "answer": "Retirement planning requires a diversified approach combining stocks, bonds, and alternative investments. Dollar-cost averaging and tax-advantaged accounts like 401(k) and IRA are essential. Consider your risk tolerance and time horizon when allocating assets.",
            "language": "English",
            "context": {
                'retrieved_documents': [
                    {
                        'rank': 1,
                        'score': '94%',
                        'date': '2024-01-15',
                        'category': 'Retirement Planning',
                        'text': 'Diversified portfolios show 8% average annual returns over 30-year periods.'
                    },
                    {
                        'rank': 2,
                        'score': '89%',
                        'date': '2024-01-10',
                        'category': 'Investment Strategy',
                        'text': 'Tax-advantaged accounts can save investors 20-30% in taxes over time.'
                    }
                ],
                'index_used': 'retirement_planning',
                'search_engine': 'FAISS',
                'has_uploaded_content': True,
                'upload_sources': ['Retirement Guide 2024', 'Investment Strategies'],
                'query_language': 'English',
                'response_language': 'English',
                'data_language': 'English'
            }
        },
        {
            "name": "Tamil Financial Query",
            "query": "ஓய்வூதிய திட்டமிடலுக்கான சிறந்த முதலீட்டு உத்திகள் என்ன?",
            "answer": "ஓய்வூதிய திட்டமிடலுக்கு பங்குகள், பத்திரங்கள் மற்றும் மாற்று முதலீடுகளை இணைக்கும் பல்வகைப்படுத்தப்பட்ட அணுகுமுறை தேவை. டாலர்-செலவு சராசரி மற்றும் வரி-சாதகமான கணக்குகள் அவசியம்.",
            "language": "Tamil",
            "context": {
                'retrieved_documents': [
                    {
                        'rank': 1,
                        'score': '91%',
                        'date': '2024-01-12',
                        'category': 'ஓய்வூதிய திட்டமிடல்',
                        'text': 'பல்வகைப்படுத்தப்பட்ட போர்ட்ஃபோலியோக்கள் 30 ஆண்டு காலத்தில் 8% சராசரி வருடாந்திர வருமானத்தைக் காட்டுகின்றன.'
                    }
                ],
                'index_used': 'tamil_financial',
                'search_engine': 'FAISS',
                'has_uploaded_content': False,
                'query_language': 'Tamil',
                'response_language': 'Tamil',
                'data_language': 'Tamil'
            }
        }
    ]
    
    for scenario in test_scenarios:
        print(f"\n📝 Testing: {scenario['name']}")
        print(f"Query: {scenario['query'][:50]}...")
        print(f"Answer: {scenario['answer'][:50]}...")
        
        try:
            # Generate related questions
            related_questions = generate_related_questions(
                scenario['query'],
                scenario['answer'],
                scenario['language'],
                scenario['context']
            )
            
            print(f"✅ Generated {len(related_questions)} related questions:")
            for i, question in enumerate(related_questions, 1):
                print(f"   {i}. {question}")
                
        except Exception as e:
            print(f"❌ Error generating questions: {str(e)}")

def demo_integration():
    """Demonstrate how the features work together"""
    print("\n\n🔗 Integration Demo")
    print("=" * 50)
    
    # Simulate a financial query response
    sample_response = """
    Based on current market analysis, cryptocurrency investments show promising trends with institutional adoption increasing. 
    Bitcoin and Ethereum continue to lead the market, while DeFi protocols are gaining significant traction. 
    However, investors should be aware of volatility risks and regulatory uncertainties.
    """
    
    sample_query = "What are the current trends in cryptocurrency investments?"
    
    print("📊 Step 1: Analyzing Response Quality")
    diversity_analysis = calculate_script_aware_char_diversity(sample_response)
    is_corrupted, cleaned_response, corruption_details = detect_text_corruption(sample_response, diversity_analysis)
    
    print(f"   Response Quality: {'❌ Needs Cleaning' if is_corrupted else '✅ Good Quality'}")
    print(f"   Script Type: {diversity_analysis['script_type']}")
    print(f"   Complexity Score: {diversity_analysis['complexity_score']:.3f}")
    
    # Use cleaned response if needed
    final_response = cleaned_response if is_corrupted else sample_response
    
    print("\n🤔 Step 2: Generating Related Questions")
    context = {
        'retrieved_documents': [
            {'category': 'Cryptocurrency', 'date': '2024-01-15'},
            {'category': 'DeFi', 'date': '2024-01-14'}
        ],
        'index_used': 'crypto_financial',
        'has_uploaded_content': True
    }
    
    try:
        questions = generate_related_questions(sample_query, final_response, "English", context)
        print(f"   Generated {len(questions)} contextual questions:")
        for i, q in enumerate(questions[:3], 1):  # Show first 3
            print(f"   {i}. {q}")
        if len(questions) > 3:
            print(f"   ... and {len(questions) - 3} more questions")
            
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
    
    print("\n✅ Integration Complete: Quality analysis + Contextual questions")

def main():
    """Run all demonstrations"""
    print("🚀 Enhanced Features Demonstration")
    print("Showcasing Character Diversity Analysis and Related Questions Generation")
    print("=" * 80)
    
    try:
        # Demo 1: Character Diversity Analysis
        demo_character_diversity()
        
        # Demo 2: Related Questions Generation
        demo_related_questions()
        
        # Demo 3: Integration
        demo_integration()
        
        print("\n\n🎉 Demonstration Complete!")
        print("\nKey Benefits:")
        print("✅ Script-aware text quality analysis")
        print("✅ Advanced corruption detection")
        print("✅ Context-aware question generation")
        print("✅ Multi-language support")
        print("✅ Seamless integration with financial queries")
        
    except KeyboardInterrupt:
        print("\n\n⚠️ Demo interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Demo error: {str(e)}")

if __name__ == "__main__":
    main()