#!/usr/bin/env python3
"""
Integration script to add enhanced upload endpoints to the existing Flask application.
Run this script to automatically integrate the enhanced document processing system.
"""

import os
import sys
import shutil
from pathlib import Path

def backup_file(file_path):
    """Create a backup of the original file"""
    backup_path = f"{file_path}.backup"
    if os.path.exists(file_path) and not os.path.exists(backup_path):
        shutil.copy2(file_path, backup_path)
        print(f"✅ Created backup: {backup_path}")
    return backup_path

def add_enhanced_endpoints_to_app():
    """Add enhanced endpoints to the main Flask application"""
    app_file = "full_code.py"
    
    if not os.path.exists(app_file):
        print(f"❌ {app_file} not found. Please run this script from the python-fiass-backend directory.")
        return False
    
    # Create backup
    backup_file(app_file)
    
    # Read the current file
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Check if already integrated
    if "from services.enhanced_upload_endpoints import create_enhanced_upload_endpoints" in content:
        print("✅ Enhanced endpoints already integrated!")
        return True
    
    # Find the import section and add our import
    import_line = "from services.enhanced_upload_endpoints import create_enhanced_upload_endpoints"
    
    # Add import after existing imports
    if "from openai import OpenAI" in content:
        content = content.replace(
            "from openai import OpenAI",
            f"from openai import OpenAI\n{import_line}"
        )
    else:
        # Add at the beginning of imports
        lines = content.split('\n')
        for i, line in enumerate(lines):
            if line.startswith('import ') or line.startswith('from '):
                lines.insert(i, import_line)
                break
        content = '\n'.join(lines)
    
    # Find where to add the endpoint creation
    app_creation_line = "app = create_enhanced_upload_endpoints(app)"
    
    # Add after CORS configuration
    if "vary_header=True" in content:
        content = content.replace(
            "vary_header=True\n)",
            f"vary_header=True\n)\n\n# Add enhanced upload endpoints\n{app_creation_line}"
        )
    else:
        # Fallback: add after app initialization
        content = content.replace(
            "app = Flask(__name__)",
            f"app = Flask(__name__)\n\n# Enhanced endpoints will be added after CORS configuration"
        )
        # Add after CORS setup
        if "CORS(app" in content:
            cors_end = content.find(")", content.find("CORS(app")) + 1
            content = content[:cors_end] + f"\n\n# Add enhanced upload endpoints\n{app_creation_line}" + content[cors_end:]
    
    # Write the updated content
    with open(app_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"✅ Enhanced endpoints integrated into {app_file}")
    return True

def create_requirements_file():
    """Create or update requirements.txt with enhanced dependencies"""
    requirements = [
        "# Core dependencies",
        "flask>=2.0.0",
        "flask-cors>=3.0.0",
        "faiss-cpu>=1.7.0",
        "sentence-transformers>=2.0.0",
        "numpy>=1.21.0",
        "python-dotenv>=0.19.0",
        "",
        "# PDF processing (install at least one)",
        "pdfplumber>=0.7.0  # Recommended for best quality",
        "PyPDF2>=3.0.0      # Fallback option",
        "# PyMuPDF>=1.23.0  # Fastest option (uncomment if needed)",
        "",
        "# Document processing",
        "python-docx>=0.8.11",
        "# textract>=1.6.0   # Optional for additional formats",
        "",
        "# Web scraping and HTTP",
        "requests>=2.28.0",
        "beautifulsoup4>=4.11.0",
        "urllib3>=1.26.0",
        "",
        "# YouTube processing (already in your system)",
        "faster-whisper>=0.9.0",
        "yt-dlp>=2023.1.0",
        "youtube-transcript-api>=0.6.0",
        "",
        "# Database and other dependencies",
        "pandas>=1.5.0",
        "openai>=1.0.0",
        "# pinecone-client>=2.0.0  # Optional",
        "",
        "# Additional utilities",
        "pathlib2>=2.3.0",
        "typing-extensions>=4.0.0"
    ]
    
    with open("requirements_enhanced.txt", 'w') as f:
        f.write('\n'.join(requirements))
    
    print("✅ Created requirements_enhanced.txt with all dependencies")

def create_env_template():
    """Create environment template with enhanced configuration"""
    env_template = [
        "# Enhanced Document Processing Configuration",
        "",
        "# Processing Configuration",
        "MAX_CHUNK_SIZE=1000",
        "CHUNK_OVERLAP=100",
        "MAX_WORKERS=4",
        "REQUEST_TIMEOUT=30",
        "MAX_RETRIES=3",
        "",
        "# FAISS Configuration",
        "FAISS_DATA_DIR=faiss_data",
        "",
        "# API Keys (add your actual keys)",
        "DEEPSEEK_API_KEY=your_deepseek_api_key_here",
        "FINANCIAL_PINECONE_API_KEY=your_pinecone_api_key_here",
        "NEWS_API_KEY=your_news_api_key_here",
        "",
        "# Index Configuration",
        "FINANCIAL_INDEX_NAME=financialnews",
        "API_ENVIRONMENT=development",
        "",
        "# Embedding Model Configuration",
        "DEFAULT_EMBED_MODEL=all-MiniLM-L6-v2",
        "CHUNK_SIZE=500",
        "BATCH_SIZE=10"
    ]
    
    env_file = ".env.template"
    with open(env_file, 'w') as f:
        f.write('\n'.join(env_template))
    
    print(f"✅ Created {env_file} with enhanced configuration")

def run_integration():
    """Run the complete integration process"""
    print("🚀 Starting Enhanced Document Processing System Integration")
    print("=" * 60)
    
    # Check if we're in the right directory
    if not os.path.exists("services"):
        print("❌ Services directory not found. Please run this script from the python-fiass-backend directory.")
        return False
    
    # Check if enhanced files exist
    enhanced_files = [
        "services/enhanced_upload_endpoints.py",
        "integration_guide.md"
    ]
    
    missing_files = [f for f in enhanced_files if not os.path.exists(f)]
    if missing_files:
        print(f"❌ Missing enhanced files: {missing_files}")
        print("Please ensure all enhanced files are in place before running integration.")
        return False
    
    print("📁 Enhanced files found:")
    for file in enhanced_files:
        print(f"   ✅ {file}")
    
    # Run integration steps
    steps = [
        ("Integrating enhanced endpoints", add_enhanced_endpoints_to_app),
        ("Creating requirements file", create_requirements_file),
        ("Creating environment template", create_env_template)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        try:
            if step_func():
                print(f"   ✅ {step_name} completed")
            else:
                print(f"   ❌ {step_name} failed")
                return False
        except Exception as e:
            print(f"   ❌ {step_name} failed: {e}")
            return False
    
    print("\n🎉 Integration completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Install dependencies: pip install -r requirements_enhanced.txt")
    print("2. Update your .env file with the template values")
    print("3. Test the enhanced endpoints")
    print("4. Update your frontend to use progress tracking")
    print("\n📖 See integration_guide.md for detailed instructions")
    
    return True

if __name__ == "__main__":
    success = run_integration()
    sys.exit(0 if success else 1)
