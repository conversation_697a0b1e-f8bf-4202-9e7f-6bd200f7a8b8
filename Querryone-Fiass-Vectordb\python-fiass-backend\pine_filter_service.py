"""
PINE Collection Email-Based Filtering Service

This service provides centralized email-based filtering functionality for the PINE collection
to ensure users only see their own data based on email authentication.
"""

import os
import sqlite3
from typing import List, Dict, Any, Optional, Tuple, Union
import logging
from database import get_connection

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PineFilterService:
    """
    Centralized service for email-based filtering of PINE collection data.
    Ensures data isolation between users based on email authentication.
    """
    
    @staticmethod
    def normalize_email(email: str) -> str:
        """
        Normalize email for consistent comparison.
        
        Args:
            email: Raw email string
            
        Returns:
            str: Normalized email (trimmed and lowercase)
        """
        if not email:
            return ""
        return email.strip().lower()
    
    @staticmethod
    def validate_email(email: str) -> Tuple[bool, str]:
        """
        Validate email format and content.
        
        Args:
            email: Email to validate
            
        Returns:
            Tuple[bool, str]: Validation status and message
        """
        if not email or not email.strip():
            return False, "Email is required and cannot be empty"
        
        normalized_email = PineFilterService.normalize_email(email)
        
        # Basic email validation
        if "@" not in normalized_email or "." not in normalized_email:
            return False, "Invalid email format"
        
        if len(normalized_email) < 5:  # Minimum reasonable email length
            return False, "Email too short"
            
        return True, "Email is valid"
    
    @staticmethod
    def get_user_indices(email: str) -> Tuple[bool, str, List[str]]:
        """
        Get all PINE collection indices that belong to a specific user.
        
        Args:
            email: User email to filter by
            
        Returns:
            Tuple[bool, str, List[str]]: Success status, message, and list of index names
        """
        try:
            # Validate email
            is_valid, validation_msg = PineFilterService.validate_email(email)
            if not is_valid:
                return False, validation_msg, []
            
            conn = get_connection()
            cursor = conn.cursor()
            
            email_normalized = PineFilterService.normalize_email(email)
            
            cursor.execute(
                """SELECT DISTINCT index_name 
                   FROM pine_collection 
                   WHERE LOWER(TRIM(email)) = ? 
                   ORDER BY index_name""",
                (email_normalized,)
            )
            
            rows = cursor.fetchall()
            indices = [row[0] for row in rows if row[0] and row[0].strip()]
            
            conn.close()
            
            logger.info(f"Retrieved {len(indices)} indices for user {email}")
            return True, f"Found {len(indices)} indices for user", indices
            
        except Exception as e:
            logger.error(f"Error retrieving user indices: {str(e)}")
            return False, f"Error retrieving user indices: {str(e)}", []
    
    @staticmethod
    def get_user_records(email: str, limit: Optional[int] = None) -> Tuple[bool, str, List[Dict[str, Any]]]:
        """
        Get all PINE collection records that belong to a specific user.
        
        Args:
            email: User email to filter by
            limit: Optional limit on number of records to return
            
        Returns:
            Tuple[bool, str, List[Dict]]: Success status, message, and list of records
        """
        try:
            # Validate email
            is_valid, validation_msg = PineFilterService.validate_email(email)
            if not is_valid:
                return False, validation_msg, []
            
            conn = get_connection()
            cursor = conn.cursor()
            
            email_normalized = PineFilterService.normalize_email(email)
            
            # Build query with optional limit
            query = """SELECT id, index_name, email, upload_date 
                      FROM pine_collection 
                      WHERE LOWER(TRIM(email)) = ? 
                      ORDER BY upload_date DESC"""
            
            params = [email_normalized]
            
            if limit and limit > 0:
                query += " LIMIT ?"
                params.append(limit)
            
            cursor.execute(query, params)
            rows = cursor.fetchall()
            
            records = []
            for row in rows:
                records.append({
                    "id": row[0],
                    "index_name": row[1],
                    "email": row[2],
                    "upload_date": row[3],
                    "category": row[1]  # For UI compatibility
                })
            
            conn.close()
            
            logger.info(f"Retrieved {len(records)} records for user {email}")
            return True, f"Found {len(records)} records for user", records
            
        except Exception as e:
            logger.error(f"Error retrieving user records: {str(e)}")
            return False, f"Error retrieving user records: {str(e)}", []
    
    @staticmethod
    def validate_user_access(email: str, index_name: str) -> Tuple[bool, str]:
        """
        Validate that a user has access to a specific index.
        
        Args:
            email: User email
            index_name: Index name to validate access for
            
        Returns:
            Tuple[bool, str]: Access granted status and message
        """
        try:
            # Validate inputs
            is_valid, validation_msg = PineFilterService.validate_email(email)
            if not is_valid:
                return False, validation_msg
            
            if not index_name or not index_name.strip():
                return False, "Index name is required and cannot be empty"
            
            conn = get_connection()
            cursor = conn.cursor()
            
            email_normalized = PineFilterService.normalize_email(email)
            index_normalized = index_name.strip()
            
            cursor.execute(
                """SELECT COUNT(*) FROM pine_collection 
                   WHERE LOWER(TRIM(email)) = ? AND index_name = ?""",
                (email_normalized, index_normalized)
            )
            
            count = cursor.fetchone()[0]
            conn.close()
            
            if count > 0:
                logger.info(f"Access granted: User {email} can access index {index_name}")
                return True, f"Access granted to index {index_name}"
            else:
                logger.warning(f"Access denied: User {email} cannot access index {index_name}")
                return False, f"Access denied to index {index_name}"
                
        except Exception as e:
            logger.error(f"Error validating user access: {str(e)}")
            return False, f"Error validating user access: {str(e)}"
    
    @staticmethod
    def filter_indices_by_user(email: str, indices: List[str]) -> Tuple[bool, str, List[str]]:
        """
        Filter a list of indices to only include those accessible by the user.
        
        Args:
            email: User email
            indices: List of index names to filter
            
        Returns:
            Tuple[bool, str, List[str]]: Success status, message, and filtered indices
        """
        try:
            # Validate email
            is_valid, validation_msg = PineFilterService.validate_email(email)
            if not is_valid:
                return False, validation_msg, []
            
            if not indices:
                return True, "No indices to filter", []
            
            # Get user's accessible indices
            success, message, user_indices = PineFilterService.get_user_indices(email)
            if not success:
                return False, message, []
            
            # Filter the provided indices to only include user's accessible ones
            filtered_indices = [idx for idx in indices if idx in user_indices]
            
            logger.info(f"Filtered {len(indices)} indices to {len(filtered_indices)} for user {email}")
            return True, f"Filtered to {len(filtered_indices)} accessible indices", filtered_indices
            
        except Exception as e:
            logger.error(f"Error filtering indices: {str(e)}")
            return False, f"Error filtering indices: {str(e)}", []


# Convenience functions for backward compatibility
def get_user_pine_indices(email: str) -> Tuple[bool, str, List[str]]:
    """Backward compatibility wrapper for get_user_indices."""
    return PineFilterService.get_user_indices(email)


def validate_user_access_to_index(email: str, index_name: str) -> Tuple[bool, str]:
    """Backward compatibility wrapper for validate_user_access."""
    return PineFilterService.validate_user_access(email, index_name)
