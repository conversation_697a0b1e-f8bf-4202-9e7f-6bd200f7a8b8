import os
import json
import datetime
import faiss
import numpy as np
from dotenv import load_dotenv
from sentence_transformers import SentenceTransformer
import requests
from bs4 import BeautifulSoup
import hashlib
from urllib.parse import urlparse, urljoin
import asyncio
import aiohttp
import concurrent.futures
from typing import Optional, Dict, Any, List
import time
import threading
import re
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# Import language detection utilities
from language_utils import (
    detect_language_from_text,
    get_index_name_for_language,
    get_language_statistics
)

# Load environment variables
load_dotenv()

# Config
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", "faiss_data")
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), FAISS_DATA_DIR)
ARTICLES_DIR = os.path.join(OUTPUT_DIR, "articles")
EMBED_MODEL = "all-MiniLM-L6-v2"
NLIST = 10  # Number of clusters for IVF
REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "45"))  # Longer timeout for deployment
MAX_RETRIES = int(os.getenv("MAX_RETRIES", "5"))  # More retries for deployment
MAX_WORKERS = int(os.getenv("MAX_WORKERS", "4"))

# Deployment environment detection
IS_DEPLOYMENT = os.getenv("DEPLOYMENT_ENV", "false").lower() == "true"
VERIFY_SSL = os.getenv("VERIFY_SSL", "true").lower() == "true"

# Ensure directories exist
os.makedirs(ARTICLES_DIR, exist_ok=True)

# Initialize embedding model
embedder = SentenceTransformer(EMBED_MODEL)

# Thread-safe progress tracking
progress_tracker = {}
progress_lock = threading.Lock()

# Enhanced headers for better compatibility with various websites
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.9',
    'Accept-Encoding': 'gzip, deflate, br',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Sec-Fetch-User': '?1',
    'Cache-Control': 'max-age=0',
    'Referer': 'https://www.google.com/'
}

def update_progress(process_id: str, progress: int, message: str = ""):
    """Update progress for a processing task"""
    with progress_lock:
        progress_tracker[process_id] = {
            'progress': progress,
            'message': message,
            'timestamp': time.time()
        }

def get_progress(process_id: str) -> Dict[str, Any]:
    """Get progress for a processing task"""
    with progress_lock:
        return progress_tracker.get(process_id, {'progress': 0, 'message': 'Not found'})

def get_article_id(url: str) -> str:
    """Generate a unique ID for the article based on URL"""
    return hashlib.md5(url.encode()).hexdigest()[:12]

def create_session_with_retries() -> requests.Session:
    """Create a requests session with retry strategy and deployment-friendly settings"""
    session = requests.Session()

    # Enhanced retry strategy for deployment environments
    retry_strategy = Retry(
        total=MAX_RETRIES,
        status_forcelist=[429, 500, 502, 503, 504, 520, 521, 522, 523, 524],
        method_whitelist=["HEAD", "GET", "OPTIONS"],
        backoff_factor=2,  # Exponential backoff
        raise_on_status=False
    )

    adapter = HTTPAdapter(
        max_retries=retry_strategy,
        pool_connections=10,
        pool_maxsize=20
    )
    session.mount("http://", adapter)
    session.mount("https://", adapter)

    # Deployment-specific settings
    if IS_DEPLOYMENT:
        print("🚀 Configuring session for deployment environment")
        # Disable SSL verification if needed (for some deployment environments)
        if not VERIFY_SSL:
            session.verify = False
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            print("⚠️ SSL verification disabled for deployment")

    return session

def clean_text(text: str) -> str:
    """Clean and normalize extracted text"""
    if not text:
        return ""

    # Remove extra whitespace and normalize
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()

    # Remove common unwanted patterns
    text = re.sub(r'(Advertisement|ADVERTISEMENT)', '', text, flags=re.IGNORECASE)
    text = re.sub(r'(Subscribe|SUBSCRIBE)', '', text, flags=re.IGNORECASE)
    text = re.sub(r'(Cookie|COOKIE)', '', text, flags=re.IGNORECASE)

    return text

def extract_text_from_url_fast(url: str, process_id: str = None) -> Optional[str]:
    """
    Fast text extraction from URL using optimized approach.
    Uses requests with retry strategy and better content parsing.
    """
    try:
        if process_id:
            update_progress(process_id, 10, "Fetching webpage content...")

        session = create_session_with_retries()

        print(f"🌐 Fetching content from: {url}")
        response = session.get(url, headers=HEADERS, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()

        if process_id:
            update_progress(process_id, 30, "Parsing HTML content...")

        soup = BeautifulSoup(response.content, 'html.parser')

        # Remove unwanted elements
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside', 'advertisement']):
            element.decompose()

        # Extract text from <p> tags (most reliable for articles)
        paragraphs = soup.find_all('p')
        article_text = ' '.join([clean_text(para.get_text()) for para in paragraphs])

        if process_id:
            update_progress(process_id, 50, "Text extraction completed")

        print(f"✅ Extracted {len(article_text)} characters from URL")
        return article_text if article_text.strip() else None

    except requests.exceptions.RequestException as e:
        print(f"❌ Network error extracting content from {url}: {e}")
        if process_id:
            update_progress(process_id, 0, f"Network error: {str(e)}")
        return None
    except Exception as e:
        print(f"❌ Error extracting content from {url}: {e}")
        if process_id:
            update_progress(process_id, 0, f"Error: {str(e)}")
        return None

def extract_text_from_url(url: str) -> Optional[str]:
    """Legacy function for backward compatibility"""
    return extract_text_from_url_fast(url)

def extract_article_content_enhanced(url: str, process_id: str = None) -> Optional[Dict[str, Any]]:
    """
    Enhanced article content extraction with multiple strategies and better error handling.

    Args:
        url: URL to extract content from
        process_id: Optional process ID for progress tracking

    Returns:
        Dict with title, content, url, and domain, or None if extraction fails
    """
    try:
        if process_id:
            update_progress(process_id, 10, "Starting article extraction...")

        session = create_session_with_retries()

        print(f"🌐 Extracting article content from: {url}")
        print(f"📤 Using headers: {HEADERS}")
        response = session.get(url, headers=HEADERS, timeout=REQUEST_TIMEOUT)
        print(f"📥 Response status: {response.status_code}")
        print(f"📏 Response content length: {len(response.content)} bytes")
        response.raise_for_status()

        if process_id:
            update_progress(process_id, 30, "Parsing HTML structure...")

        soup = BeautifulSoup(response.content, 'html.parser')

        # Remove unwanted elements
        for element in soup(['script', 'style', 'nav', 'header', 'footer', 'aside', 'advertisement', 'iframe']):
            element.decompose()

        # Extract title with multiple strategies
        title = ""
        title_strategies = [
            lambda: soup.find('title'),
            lambda: soup.find('h1'),
            lambda: soup.find('meta', property='og:title'),
            lambda: soup.find('meta', name='twitter:title'),
            lambda: soup.select_one('.article-title, .post-title, .entry-title')
        ]

        for strategy in title_strategies:
            try:
                title_elem = strategy()
                if title_elem:
                    if title_elem.name == 'meta':
                        title = title_elem.get('content', '').strip()
                    else:
                        title = title_elem.get_text().strip()
                    if title:
                        break
            except:
                continue

        if process_id:
            update_progress(process_id, 50, "Extracting main content...")

        # Enhanced content extraction with multiple strategies
        content = ""

        # Strategy 1: Look for article-specific containers
        article_selectors = [
            'article',
            '[role="main"]',
            '.article-content',
            '.post-content',
            '.entry-content',
            '.content',
            '.main-content',
            '.article-body',
            '.post-body',
            '.story-body',
            # MoneyControl specific selectors
            '.arti-flow',
            '.content_wrapper',
            '.article_desc',
            '.news_content',
            # Economic Times specific
            '.artText',
            '.Normal',
            # Hindu BusinessLine specific
            '.content-body',
            '.story-element-text'
        ]

        for selector in article_selectors:
            try:
                content_elem = soup.select_one(selector)
                if content_elem:
                    print(f"🎯 Found content container with selector: {selector}")
                    # Extract paragraphs from within the container
                    paragraphs = content_elem.find_all('p')
                    if paragraphs:
                        content = ' '.join([clean_text(p.get_text()) for p in paragraphs])
                        print(f"📝 Extracted {len(content)} characters from {len(paragraphs)} paragraphs")
                        if len(content.strip()) > 200:  # Minimum content threshold
                            print(f"✅ Content meets minimum threshold, using selector: {selector}")
                            break
                    else:
                        # Try extracting all text from the container if no paragraphs
                        content = clean_text(content_elem.get_text())
                        print(f"📝 Extracted {len(content)} characters from container text")
                        if len(content.strip()) > 200:
                            print(f"✅ Container text meets threshold, using selector: {selector}")
                            break
            except Exception as e:
                print(f"⚠️ Error with selector {selector}: {e}")
                continue

        # Strategy 2: If no good container found, extract all paragraphs
        if not content or len(content.strip()) < 200:
            print("🔄 Fallback to Strategy 2: Extracting all paragraphs")
            paragraphs = soup.find_all('p')
            content = ' '.join([clean_text(para.get_text()) for para in paragraphs])
            print(f"📝 Strategy 2: Extracted {len(content)} characters from {len(paragraphs)} paragraphs")

        # Strategy 3: If still no content, try div elements with text
        if not content or len(content.strip()) < 100:
            print("🔄 Fallback to Strategy 3: Extracting div elements")
            text_divs = soup.find_all('div')
            div_texts = []
            for div in text_divs:
                div_text = clean_text(div.get_text())
                if len(div_text.strip()) > 50:
                    div_texts.append(div_text)
            content = ' '.join(div_texts)
            print(f"📝 Strategy 3: Extracted {len(content)} characters from {len(div_texts)} div elements")

        # Strategy 4: Last resort - extract all visible text
        if not content or len(content.strip()) < 50:
            print("🔄 Fallback to Strategy 4: Extracting all visible text")
            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.extract()
            content = clean_text(soup.get_text())
            print(f"📝 Strategy 4: Extracted {len(content)} characters from all visible text")

        # Final cleanup
        content = clean_text(content)

        if process_id:
            update_progress(process_id, 80, "Content extraction completed")

        # Validate extracted content
        if not content or len(content.strip()) < 50:
            print(f"⚠️ Insufficient content extracted from {url}")
            print(f"📊 Content length: {len(content) if content else 0} characters")
            print(f"🔍 First 200 chars of HTML: {str(soup)[:200]}...")

            # Try to identify the issue
            if not soup.find_all('p'):
                print("❌ No paragraph tags found in HTML")
            if not soup.find_all('div'):
                print("❌ No div tags found in HTML")

            return None

        result = {
            "title": title or "Untitled Article",
            "content": content,
            "url": url,
            "domain": urlparse(url).netloc,
            "content_length": len(content),
            "extraction_method": "enhanced"
        }

        print(f"✅ Successfully extracted article: {result['title'][:60]}...")
        print(f"📄 Content length: {result['content_length']} characters")

        if process_id:
            update_progress(process_id, 100, "Article extraction completed")

        return result

    except requests.exceptions.RequestException as e:
        error_msg = f"Network error extracting article from {url}: {e}"
        print(f"❌ {error_msg}")

        # Deployment-specific fallback: Try with different headers
        if IS_DEPLOYMENT:
            print("🔄 Attempting deployment fallback with simplified headers...")
            try:
                fallback_headers = {
                    'User-Agent': 'Mozilla/5.0 (compatible; ArticleBot/1.0)',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
                }
                session = requests.Session()
                response = session.get(url, headers=fallback_headers, timeout=REQUEST_TIMEOUT, verify=VERIFY_SSL)
                if response.status_code == 200:
                    print("✅ Fallback request successful, attempting content extraction...")
                    soup = BeautifulSoup(response.content, 'html.parser')

                    # Simple content extraction for fallback
                    paragraphs = soup.find_all('p')
                    content = ' '.join([clean_text(p.get_text()) for p in paragraphs])

                    if content and len(content.strip()) > 50:
                        title = soup.find('title')
                        title_text = title.get_text().strip() if title else "Article"

                        return {
                            "title": title_text,
                            "content": content,
                            "url": url,
                            "domain": urlparse(url).netloc,
                            "content_length": len(content),
                            "extraction_method": "deployment_fallback"
                        }
            except Exception as fallback_error:
                print(f"❌ Fallback also failed: {fallback_error}")

        if process_id:
            update_progress(process_id, 0, f"Network error: {str(e)}")
        return None
    except Exception as e:
        error_msg = f"Error extracting article from {url}: {e}"
        print(f"❌ {error_msg}")
        if process_id:
            update_progress(process_id, 0, f"Error: {str(e)}")
        return None

def extract_article_content(url: str) -> Optional[Dict[str, Any]]:
    """Legacy function for backward compatibility"""
    return extract_article_content_enhanced(url)

def chunk_text(text: str, size: int = 500):
    """Split text into chunks"""
    chunks, start = [], 0
    L = len(text)
    while start < L:
        end = min(start + size, L)
        if end < L:
            while end > start and not text[end].isspace():
                end -= 1
            if end == start:
                end = min(start + size, L)
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    return chunks

def process_article_url(article_url, index_name="default", user_selected_language=None):
    """Process an article URL and add to selected FAISS index with multi-language support"""
    print(f"📰 Processing Article URL: {article_url}")
    print(f"🎯 Target index: {index_name}")
    if user_selected_language:
        print(f"🌐 User selected language: {user_selected_language}")

    # Extract article content
    article_data = extract_article_content(article_url)
    if not article_data or not article_data["content"]:
        print("❌ Could not extract article content")
        return False

    print(f"✅ Extracted article: {article_data['title'][:60]}...")
    print(f"📄 Content length: {len(article_data['content'])} characters")

    # Detect language from article content
    detected_language = detect_language_from_text(article_data["content"])
    print(f"🔍 Detected language: {detected_language}")

    # Get language statistics for detailed analysis
    lang_stats = get_language_statistics(article_data["content"])
    if lang_stats:
        print("📊 Language distribution:")
        for lang, ratio in sorted(lang_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {lang}: {ratio:.3f} ({ratio*100:.1f}%)")

    # Determine final index name based on language detection and user preference
    if index_name == "default" or index_name == "auto":
        # Auto-detect index based on content language
        final_index_name = get_index_name_for_language(detected_language, user_selected_language)
    else:
        # Use user-specified index
        final_index_name = index_name
        print(f"🎯 Using user-specified index: {final_index_name}")

    print(f"📁 Final target index: {final_index_name}")

    # Generate article ID
    article_id = get_article_id(article_url)
    
    # Load or create the target FAISS index
    try:
        # Import FAISS index management functions
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from full_code import load_faiss_index, create_faiss_index

        # Try to load existing index using final index name
        faiss_index, existing_metadata, success = load_faiss_index(final_index_name)

        if not success or faiss_index is None:
            print(f"📁 Creating new FAISS index: {final_index_name}")
            # Create new index if it doesn't exist
            dummy_embedding = embedder.encode(["hello world"])
            dim = len(dummy_embedding[0])
            result = create_faiss_index(final_index_name, dimension=dim, embed_model=EMBED_MODEL)
            if not result.get("success"):
                print(f"❌ Failed to create index: {result.get('error')}")
                return False

            # Load the newly created index
            faiss_index, existing_metadata, success = load_faiss_index(final_index_name)
            if not success:
                print(f"❌ Failed to load newly created index")
                return False

        print(f"✅ Loaded FAISS index: {final_index_name} with {len(existing_metadata)} existing entries")

    except Exception as e:
        print(f"❌ Error loading FAISS index: {e}")
        return False

    # Process content chunks
    chunks = chunk_text(article_data["content"])
    record_date = datetime.datetime.now().isoformat()
    new_vectors = []
    new_metadata = []

    for chunk_idx, chunk in enumerate(chunks):
        vec_list = embedder.encode([chunk])[0]
        vec = np.array(vec_list, dtype="float32")
        new_vectors.append(vec)

        new_metadata.append({
            "chunk_text": chunk,
            "record_date": record_date,
            "category": "article",
            "url": article_url,
            "article_id": article_id,
            "title": article_data["title"],
            "domain": article_data["domain"],
            "vector_id": f"article-{article_id}-chunk-{chunk_idx}",
            "source_type": "article",
            "upload_source": "article_upload",
            "detected_language": detected_language,
            "index_name": final_index_name,
            "language_stats": lang_stats,
            "user_selected_language": user_selected_language
        })

        print(f"📝 Embedded chunk {chunk_idx + 1}/{len(chunks)}: {chunk[:60]}...")

    if not new_vectors:
        print("❌ No content to index")
        return False

    # Stack & normalize for cosine similarity
    xb = np.vstack(new_vectors)
    faiss.normalize_L2(xb)

    # Add vectors to the existing index
    faiss_index.add(xb)

    # Combine existing and new metadata
    combined_metadata = existing_metadata + new_metadata

    # Save updated index and metadata
    index_dir = os.path.join(OUTPUT_DIR, final_index_name)
    os.makedirs(index_dir, exist_ok=True)

    faiss_file_path = os.path.join(index_dir, f"{final_index_name}.faiss")
    metadata_file_path = os.path.join(index_dir, f"{final_index_name}.json")

    # Save FAISS index
    faiss.write_index(faiss_index, faiss_file_path)
    print(f"🧠 Updated FAISS index saved to {faiss_file_path}")

    # Save metadata
    with open(metadata_file_path, "w", encoding="utf-8") as f:
        json.dump(combined_metadata, f, ensure_ascii=False, indent=2)
    print(f"🗃️ Updated metadata saved to {metadata_file_path}")

    print(f"✅ Finished: Added {len(new_vectors)} chunks to index '{final_index_name}' (total: {faiss_index.ntotal})")
    print(f"🌐 Content language: {detected_language}")
    print(f"📁 Stored in index: {final_index_name}")
    return True

if __name__ == "__main__":
    # Test with an article URL
    test_url = input("Enter Article URL: ").strip()
    if test_url:
        success = process_article_url(test_url)
        if success:
            print("🎉 Article processed successfully!")
        else:
            print("❌ Failed to process article")
