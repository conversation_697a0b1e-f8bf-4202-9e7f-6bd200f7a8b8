#!/usr/bin/env python3
"""
Audio processor for MP3 and WAV files using Whisper transcription
Follows the same patterns as other processors in the system
"""

import os
import json
import datetime
import hashlib
import tempfile
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Try importing Faster Whisper
try:
    from faster_whisper import WhisperModel
    WHISPER_AVAILABLE = True
    print("✅ Faster Whisper available for audio processing")
except ImportError:
    WHISPER_AVAILABLE = False
    print("⚠️ Faster Whisper not available - audio processing will not work")

# Configuration
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", "faiss_data")
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), FAISS_DATA_DIR)
AUDIO_DIR = os.path.join(OUTPUT_DIR, "audio")
EMBED_MODEL = "all-MiniLM-L6-v2"

# Supported audio formats
SUPPORTED_AUDIO_FORMATS = {'.mp3', '.wav', '.m4a', '.flac', '.ogg'}

# Ensure directories exist
os.makedirs(AUDIO_DIR, exist_ok=True)

# Initialize embedding model
embedder = SentenceTransformer(EMBED_MODEL)

def is_supported_audio_file(file_path):
    """Check if the file is a supported audio format"""
    file_ext = Path(file_path).suffix.lower()
    return file_ext in SUPPORTED_AUDIO_FORMATS

def get_audio_id(file_path):
    """Generate a unique ID for the audio file based on file path and name"""
    file_name = os.path.basename(file_path)
    return hashlib.md5(file_name.encode()).hexdigest()[:12]

def get_audio_info(file_path):
    """Get basic information about the audio file"""
    try:
        file_stat = os.stat(file_path)
        file_name = os.path.basename(file_path)
        file_ext = Path(file_path).suffix.lower()

        return {
            "name": file_name,
            "size": file_stat.st_size,
            "extension": file_ext,
            "type": f"audio{file_ext}",
            "modified": datetime.datetime.fromtimestamp(file_stat.st_mtime).isoformat()
        }
    except Exception as e:
        print(f"Error getting audio file info: {e}")
        return None

def transcribe_audio_with_whisper(audio_path):
    """Transcribe audio file using Faster Whisper"""
    if not WHISPER_AVAILABLE:
        print("❌ Faster Whisper not available for transcription")
        return None

    try:
        print(f"🎤 Loading Faster Whisper model...")
        # Use base model for balance of speed and accuracy
        model = WhisperModel("base", device="cpu", compute_type="int8")

        print(f"🎵 Transcribing audio file: {audio_path}")
        segments, info = model.transcribe(audio_path, beam_size=5)

        # Convert segments to list and extract text
        segments_list = list(segments)
        transcript = " ".join([segment.text for segment in segments_list])
        language = info.language

        print(f"✅ Transcription completed. Language: {language}")
        print(f"📝 Transcript length: {len(transcript)} characters")

        # Convert segments to format compatible with original code
        formatted_segments = []
        for segment in segments_list:
            formatted_segments.append({
                "text": segment.text,
                "start": segment.start,
                "end": segment.end
            })

        return {
            "text": transcript.strip(),
            "language": language,
            "segments": formatted_segments
        }

    except FileNotFoundError as e:
        print(f"❌ File not found error: {e}")
        return None
    except Exception as e:
        print(f"❌ Error transcribing audio: {e}")
        print("💡 Common solutions:")
        print("   - Check if audio file format is supported")
        print("   - Ensure audio file is not corrupted")
        print("   - Try with a different audio file")
        return None

def chunk_text(text, chunk_size=500, overlap=50):
    """Split text into overlapping chunks for better context preservation"""
    if len(text) <= chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        end = start + chunk_size

        # If this isn't the last chunk, try to break at a sentence or word boundary
        if end < len(text):
            # Look for sentence boundary
            sentence_end = text.rfind('.', start, end)
            if sentence_end > start + chunk_size // 2:
                end = sentence_end + 1
            else:
                # Look for word boundary
                word_end = text.rfind(' ', start, end)
                if word_end > start + chunk_size // 2:
                    end = word_end

        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)

        # Move start position with overlap
        start = end - overlap
        if start >= len(text):
            break

    return chunks

def process_audio_file(file_path, original_filename=None, index_name="default"):
    """Process an audio file and add to selected FAISS index"""
    print(f"🎵 Processing Audio File: {file_path}")
    print(f"🎯 Target index: {index_name}")

    # Check if Whisper is available
    if not WHISPER_AVAILABLE:
        print("❌ Whisper not available - cannot process audio files")
        return False

    # Check if file exists and is supported
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False

    if not is_supported_audio_file(file_path):
        file_ext = Path(file_path).suffix.lower()
        print(f"❌ Unsupported audio format: {file_ext}")
        print(f"Supported formats: {list(SUPPORTED_AUDIO_FORMATS)}")
        return False

    # Get file information
    file_info = get_audio_info(file_path)
    if not file_info:
        print("❌ Could not get audio file information")
        return False

    print(f"📊 Audio info: {file_info['name']} ({file_info['size']} bytes, {file_info['type']})")

    # Transcribe audio file
    transcription_result = transcribe_audio_with_whisper(file_path)
    if not transcription_result:
        print("❌ Could not transcribe audio file")
        return False

    transcript = transcription_result["text"]
    language = transcription_result["language"]

    print(f"✅ Transcribed audio: {len(transcript)} characters in {language}")

    # Generate audio ID
    audio_id = get_audio_id(file_path)

    # Load or create the target FAISS index
    try:
        # Import FAISS index management functions
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from full_code import load_faiss_index, create_faiss_index

        # Try to load existing index
        faiss_index, existing_metadata, success = load_faiss_index(index_name)

        if not success or faiss_index is None:
            print(f"📁 Creating new FAISS index: {index_name}")
            # Create new index if it doesn't exist
            dummy_embedding = embedder.encode(["hello world"])
            dim = len(dummy_embedding[0])
            result = create_faiss_index(index_name, dimension=dim, embed_model=EMBED_MODEL)
            if not result.get("success"):
                print(f"❌ Failed to create index: {result.get('error')}")
                return False

            # Load the newly created index
            faiss_index, existing_metadata, success = load_faiss_index(index_name)
            if not success:
                print(f"❌ Failed to load newly created index")
                return False

        print(f"✅ Loaded FAISS index: {index_name} with {len(existing_metadata)} existing entries")

    except Exception as e:
        print(f"❌ Error loading FAISS index: {e}")
        return False

    # Process transcript chunks
    chunks = chunk_text(transcript)
    record_date = datetime.datetime.now().isoformat()
    new_vectors = []
    new_metadata = []

    for chunk_idx, chunk in enumerate(chunks):
        # Create embedding for this chunk
        vec_list = embedder.encode([chunk])
        vec = np.array(vec_list[0], dtype="float32")
        new_vectors.append(vec)

        new_metadata.append({
            "chunk_text": chunk,
            "record_date": record_date,
            "category": "audio",
            "url": f"file://{file_path}",
            "audio_id": audio_id,
            "title": original_filename or file_info["name"],
            "file_name": file_info["name"],
            "file_type": file_info["type"],
            "file_extension": file_info["extension"],
            "file_size": file_info["size"],
            "language": language,
            "vector_id": f"audio-{audio_id}-chunk-{chunk_idx}",
            "source_type": "audio",
            "transcription_method": "whisper",
            "upload_source": "audio_upload"
        })

        print(f"📝 Embedded chunk {chunk_idx + 1}/{len(chunks)}: {chunk[:60]}...")

    if not new_vectors:
        print("❌ No content to index")
        return False

    # Stack & normalize for cosine similarity
    xb = np.vstack(new_vectors)
    faiss.normalize_L2(xb)

    # Add vectors to the existing index
    faiss_index.add(xb)

    # Combine existing and new metadata
    combined_metadata = existing_metadata + new_metadata

    # Save updated index and metadata
    index_dir = os.path.join(OUTPUT_DIR, index_name)
    os.makedirs(index_dir, exist_ok=True)

    faiss_file_path = os.path.join(index_dir, f"{index_name}.faiss")
    metadata_file_path = os.path.join(index_dir, f"{index_name}.json")

    # Save FAISS index
    faiss.write_index(faiss_index, faiss_file_path)
    print(f"🧠 Updated FAISS index saved to {faiss_file_path}")

    # Save metadata
    with open(metadata_file_path, "w", encoding="utf-8") as f:
        json.dump(combined_metadata, f, ensure_ascii=False, indent=2)
    print(f"🗃️ Updated metadata saved to {metadata_file_path}")

    print(f"✅ Finished: Added {len(new_vectors)} chunks to index '{index_name}' (total: {faiss_index.ntotal})")
    return True

def main():
    """Test function for audio processing"""
    print("🎵 Audio Processor Test")
    print(f"📁 Audio directory: {AUDIO_DIR}")
    print(f"🎤 Whisper available: {WHISPER_AVAILABLE}")
    print(f"🎧 Supported formats: {list(SUPPORTED_AUDIO_FORMATS)}")

    # Test with the provided WAV file
    test_file = r"C:\Users\<USER>\Downloads\python-fiass\03-01-02-01-01-01-01.wav"
    if os.path.exists(test_file):
        print(f"\n🧪 Testing with: {test_file}")
        success = process_audio_file(test_file)
        if success:
            print("🎉 Audio file processed successfully!")
        else:
            print("❌ Failed to process audio file")
    else:
        print(f"❌ Test file not found: {test_file}")
        # Allow manual input
        test_file = input("\nEnter audio file path: ").strip()
        if test_file and os.path.exists(test_file):
            success = process_audio_file(test_file)
            if success:
                print("🎉 Audio file processed successfully!")
            else:
                print("❌ Failed to process audio file")
        else:
            print("❌ File not found or invalid path")

if __name__ == "__main__":
    main()