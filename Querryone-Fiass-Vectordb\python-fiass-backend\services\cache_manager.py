"""
Cache Manager for FAISS Backend
Provides in-memory caching for processed documents and search results
"""

import hashlib
import time
import threading
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass
from collections import OrderedDict
import json
import os

@dataclass
class CacheItem:
    """Cache item with metadata"""
    data: Any
    timestamp: float
    access_count: int
    size_bytes: int
    key: str
    expires_at: float

class LRUCache:
    """Thread-safe LRU Cache implementation"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.cache: OrderedDict[str, CacheItem] = OrderedDict()
        self.lock = threading.RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'total_size_bytes': 0
        }
    
    def _calculate_size(self, data: Any) -> int:
        """Calculate approximate size of data in bytes"""
        try:
            if isinstance(data, str):
                return len(data.encode('utf-8'))
            elif isinstance(data, (dict, list)):
                return len(json.dumps(data, default=str).encode('utf-8'))
            else:
                return len(str(data).encode('utf-8'))
        except:
            return 1024  # Default size if calculation fails
    
    def get(self, key: str) -> Optional[Any]:
        """Get item from cache"""
        with self.lock:
            if key not in self.cache:
                self.stats['misses'] += 1
                return None
            
            item = self.cache[key]
            
            # Check if expired
            if time.time() > item.expires_at:
                del self.cache[key]
                self.stats['total_size_bytes'] -= item.size_bytes
                self.stats['misses'] += 1
                return None
            
            # Move to end (most recently used)
            self.cache.move_to_end(key)
            item.access_count += 1
            self.stats['hits'] += 1
            
            return item.data
    
    def set(self, key: str, data: Any, ttl: Optional[int] = None) -> None:
        """Set item in cache"""
        with self.lock:
            ttl = ttl or self.default_ttl
            expires_at = time.time() + ttl
            size_bytes = self._calculate_size(data)
            
            # Remove existing item if present
            if key in self.cache:
                old_item = self.cache[key]
                self.stats['total_size_bytes'] -= old_item.size_bytes
                del self.cache[key]
            
            # Create new cache item
            item = CacheItem(
                data=data,
                timestamp=time.time(),
                access_count=1,
                size_bytes=size_bytes,
                key=key,
                expires_at=expires_at
            )
            
            self.cache[key] = item
            self.stats['total_size_bytes'] += size_bytes
            
            # Evict oldest items if necessary
            while len(self.cache) > self.max_size:
                oldest_key, oldest_item = self.cache.popitem(last=False)
                self.stats['total_size_bytes'] -= oldest_item.size_bytes
                self.stats['evictions'] += 1
    
    def delete(self, key: str) -> bool:
        """Delete item from cache"""
        with self.lock:
            if key in self.cache:
                item = self.cache[key]
                self.stats['total_size_bytes'] -= item.size_bytes
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Clear all items from cache"""
        with self.lock:
            self.cache.clear()
            self.stats['total_size_bytes'] = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'hit_rate': round(hit_rate, 2),
                'evictions': self.stats['evictions'],
                'total_size_bytes': self.stats['total_size_bytes'],
                'total_size_mb': round(self.stats['total_size_bytes'] / (1024 * 1024), 2)
            }

class CacheManager:
    """Main cache manager for the FAISS backend"""
    
    def __init__(self):
        # Different caches for different types of data
        self.document_cache = LRUCache(max_size=500, default_ttl=86400)  # 24 hours for documents
        self.search_cache = LRUCache(max_size=1000, default_ttl=3600)   # 1 hour for search results
        self.embedding_cache = LRUCache(max_size=2000, default_ttl=7200) # 2 hours for embeddings
        self.file_hash_cache = LRUCache(max_size=1000, default_ttl=86400) # 24 hours for file hashes
        
        self.enabled = True
    
    def generate_file_hash(self, file_content: bytes, filename: str) -> str:
        """Generate hash for file content"""
        hasher = hashlib.sha256()
        hasher.update(file_content)
        hasher.update(filename.encode('utf-8'))
        return hasher.hexdigest()
    
    def generate_search_key(self, query: str, index_name: str, k: int = 5, **kwargs) -> str:
        """Generate cache key for search queries"""
        key_data = f"{query}:{index_name}:{k}:{json.dumps(kwargs, sort_keys=True)}"
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()
    
    def generate_embedding_key(self, text: str, model_name: str) -> str:
        """Generate cache key for embeddings"""
        key_data = f"{text}:{model_name}"
        return hashlib.md5(key_data.encode('utf-8')).hexdigest()
    
    # Document processing cache
    def get_processed_document(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached processed document"""
        if not self.enabled:
            return None
        return self.document_cache.get(file_hash)
    
    def cache_processed_document(self, file_hash: str, result: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """Cache processed document result"""
        if not self.enabled:
            return
        self.document_cache.set(file_hash, result, ttl)
    
    # Search results cache
    def get_search_results(self, search_key: str) -> Optional[Dict[str, Any]]:
        """Get cached search results"""
        if not self.enabled:
            return None
        return self.search_cache.get(search_key)
    
    def cache_search_results(self, search_key: str, results: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """Cache search results"""
        if not self.enabled:
            return
        self.search_cache.set(search_key, results, ttl)
    
    # Embedding cache
    def get_embedding(self, embedding_key: str) -> Optional[List[float]]:
        """Get cached embedding"""
        if not self.enabled:
            return None
        return self.embedding_cache.get(embedding_key)
    
    def cache_embedding(self, embedding_key: str, embedding: List[float], ttl: Optional[int] = None) -> None:
        """Cache embedding"""
        if not self.enabled:
            return
        self.embedding_cache.set(embedding_key, embedding, ttl)
    
    # File hash cache
    def get_file_hash_result(self, file_hash: str) -> Optional[Dict[str, Any]]:
        """Get cached file processing result by hash"""
        if not self.enabled:
            return None
        return self.file_hash_cache.get(file_hash)
    
    def cache_file_hash_result(self, file_hash: str, result: Dict[str, Any], ttl: Optional[int] = None) -> None:
        """Cache file processing result by hash"""
        if not self.enabled:
            return
        self.file_hash_cache.set(file_hash, result, ttl)
    
    # Cache management
    def clear_all_caches(self) -> None:
        """Clear all caches"""
        self.document_cache.clear()
        self.search_cache.clear()
        self.embedding_cache.clear()
        self.file_hash_cache.clear()
    
    def get_all_stats(self) -> Dict[str, Any]:
        """Get statistics for all caches"""
        return {
            'enabled': self.enabled,
            'document_cache': self.document_cache.get_stats(),
            'search_cache': self.search_cache.get_stats(),
            'embedding_cache': self.embedding_cache.get_stats(),
            'file_hash_cache': self.file_hash_cache.get_stats()
        }
    
    def toggle_cache(self, enabled: bool) -> None:
        """Enable or disable caching"""
        self.enabled = enabled
        if not enabled:
            self.clear_all_caches()

# Global cache manager instance
cache_manager = CacheManager()

def get_cache_manager() -> CacheManager:
    """Get the global cache manager instance"""
    return cache_manager
