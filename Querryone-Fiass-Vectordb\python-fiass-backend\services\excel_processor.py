"""
Excel File Processor for FAISS Backend System

This module handles Excel file processing for the FAISS backend system,
including Tamil language content detection and proper data structuring.
"""

import os
import re
import json
import pandas as pd
import numpy as np
import uuid
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import faiss
from langchain_huggingface.embeddings import HuggingFaceEmbeddings

# Tamil language detection
def is_tamil_text(text: str) -> bool:
    """Check if text contains Tamil characters"""
    if not text or not isinstance(text, str):
        return False
    # Tamil Unicode range: \u0B80-\u0BFF
    tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
    return bool(tamil_pattern.search(text))

def detect_language_from_excel_data(df: pd.DataFrame) -> str:
    """
    Detect if the Excel data contains Tamil content
    
    Args:
        df: Pandas DataFrame containing Excel data
        
    Returns:
        str: 'Tamil' if Tamil content detected, 'English' otherwise
    """
    # Sample text from all string columns
    sample_texts = []
    
    for column in df.columns:
        if df[column].dtype == 'object':  # String columns
            # Get first few non-null values
            sample_values = df[column].dropna().head(10)
            for value in sample_values:
                if isinstance(value, str) and len(value.strip()) > 0:
                    sample_texts.append(value)
    
    # Check if any sample contains Tamil text
    for text in sample_texts:
        if is_tamil_text(text):
            print(f"🌏 TAMIL CONTENT DETECTED in Excel data: '{text[:50]}...'")
            return 'Tamil'
    
    return 'English'

def chunk_text(text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
    """
    Split text into overlapping chunks for better context preservation.
    
    Args:
        text: Input text to chunk
        chunk_size: Maximum size of each chunk
        overlap: Number of characters to overlap between chunks
        
    Returns:
        List of text chunks
    """
    if not text or len(text) <= chunk_size:
        return [text] if text else []
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        
        # If this is not the last chunk, try to break at a word boundary
        if end < len(text):
            # Look for the last space within the chunk
            last_space = text.rfind(' ', start, end)
            if last_space > start:
                end = last_space
        
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        
        # Move start position with overlap
        start = end - overlap if end < len(text) else end
        
        # Prevent infinite loop
        if start >= len(text):
            break
    
    return chunks

def combine_excel_columns(row: pd.Series, exclude_columns: List[str] = None) -> str:
    """
    Combine all relevant columns from an Excel row into a single text string.
    
    Args:
        row: Pandas Series representing a row
        exclude_columns: List of column names to exclude
        
    Returns:
        Combined text string
    """
    if exclude_columns is None:
        exclude_columns = []
    
    combined_parts = []
    
    for column_name, value in row.items():
        # Skip excluded columns
        if column_name in exclude_columns:
            continue
            
        # Skip null/empty values
        if pd.isna(value) or value == '' or value is None:
            continue
            
        # Convert to string and clean
        text_value = str(value).strip()
        if text_value and text_value.lower() not in ['nan', 'none', 'null']:
            combined_parts.append(f"{column_name}: {text_value}")
    
    return " | ".join(combined_parts)

def process_excel_data(
    excel_data: pd.DataFrame,
    client_email: str,
    filename: str,
    index_name: str,
    embedder: HuggingFaceEmbeddings,
    chunk_size: int = 1000
) -> Tuple[List[np.ndarray], List[Dict[str, Any]]]:
    """
    Process Excel data and create vectors and metadata for FAISS storage.
    Enhanced to match Tamil implementation format with exact metadata structure.

    Args:
        excel_data: Pandas DataFrame containing Excel data
        client_email: Client email identifier (from dropdown)
        filename: Original filename
        index_name: FAISS index name
        embedder: HuggingFace embeddings model
        chunk_size: Size of text chunks

    Returns:
        Tuple of (vectors, metadata) lists
    """
    vectors = []
    metadata = []
    record_date = datetime.now().isoformat()

    print(f"📊 Processing {len(excel_data)} rows from Excel file: {filename}")

    # Detect language
    detected_language = detect_language_from_excel_data(excel_data)
    print(f"🌏 Detected language: {detected_language}")

    for row_idx, row in excel_data.iterrows():
        # Extract text content using Tamil implementation logic
        text = ""

        # Priority columns for text extraction (matching Tamil implementation)
        text_columns = ["news_article", "news_title", "Content", "content", "Text", "text", "Article", "article", "News", "news"]
        for col in text_columns:
            if col in excel_data.columns:
                val = str(row.get(col, "")).strip()
                if val and val.lower() != "nan":
                    text = val
                    break

        # Special handling for news_title + news_article combination
        if "news_title" in excel_data.columns and "news_article" in excel_data.columns:
            title = str(row.get("news_title", "")).strip()
            article = str(row.get("news_article", "")).strip()
            if title.lower() != "nan" and article.lower() != "nan":
                text = f"{title}. {article}"
            elif title.lower() != "nan":
                text = title
            elif article.lower() != "nan":
                text = article

        # If no text found in priority columns, combine all columns
        if not text or text.lower() == "nan":
            text = combine_excel_columns(row)

        if not text or text.lower() == "nan":
            print(f"⚠️ Skipping empty row {row_idx}")
            continue

        # Extract category using Tamil implementation logic
        category = "unknown"
        category_columns = ["news_category", "Sentiment", "sentiment", "Category", "category", "Label", "label"]
        for col in category_columns:
            if col in excel_data.columns:
                val = str(row.get(col, "")).strip()
                if val and val.lower() != "nan":
                    category = val
                    break

        # Extract URL using Tamil implementation logic
        url = "N/A"
        url_columns = ["URL", "url", "Link", "link", "Source", "source"]
        for col in url_columns:
            if col in excel_data.columns:
                url_val = str(row.get(col, "")).strip()
                if url_val and url_val.lower() != "nan":
                    url = url_val
                    break

        # Extract summary using Tamil implementation logic
        summary = "N/A"
        summary_columns = ["Summary", "summary", "Abstract", "abstract", "Description", "description"]
        for col in summary_columns:
            if col in excel_data.columns:
                sum_val = str(row.get(col, "")).strip()
                if sum_val and sum_val.lower() != "nan":
                    summary = sum_val
                    break

        try:
            # Generate embedding for the full text
            vector = embedder.embed_documents([text])[0]
            vector_array = np.array(vector, dtype='float32')
            vectors.append(vector_array)

            # Create metadata in the exact format requested
            chunk_metadata = {
                "chunk_text": text,
                "record_date": record_date,
                "category": category,
                "url": url,
                "summary": summary,
                "vector_id": f"news-{row_idx}-chunk-0",
                "client_email": client_email,
                "file_uploaded": filename
            }

            metadata.append(chunk_metadata)

            if (row_idx + 1) % 50 == 0:
                print(f"📝 Processed {row_idx + 1}/{len(excel_data)} rows...")

        except Exception as e:
            print(f"❌ Error processing row {row_idx}: {e}")
            continue

    print(f"✅ Successfully processed {len(vectors)} chunks from {len(excel_data)} rows")
    return vectors, metadata

def validate_excel_file(file_path: str) -> Tuple[bool, str, Optional[pd.DataFrame]]:
    """
    Validate and read Excel file.
    
    Args:
        file_path: Path to Excel file
        
    Returns:
        Tuple of (is_valid, message, dataframe)
    """
    try:
        # Check file extension
        if not file_path.lower().endswith(('.xlsx', '.xls')):
            return False, "Invalid file format. Only .xlsx and .xls files are supported.", None
        
        # Check if file exists
        if not os.path.exists(file_path):
            return False, "File does not exist.", None
        
        # Try to read the Excel file
        try:
            df = pd.read_excel(file_path)
        except Exception as e:
            return False, f"Failed to read Excel file: {str(e)}", None
        
        # Check if DataFrame is empty
        if df.empty:
            return False, "Excel file is empty.", None
        
        # Check if DataFrame has any columns
        if len(df.columns) == 0:
            return False, "Excel file has no columns.", None
        
        print(f"📊 Excel file validation successful:")
        print(f"   - Rows: {len(df)}")
        print(f"   - Columns: {len(df.columns)}")
        print(f"   - Column names: {list(df.columns)}")
        
        return True, "Excel file is valid.", df
        
    except Exception as e:
        return False, f"Error validating Excel file: {str(e)}", None

def check_duplicate_excel_upload(
    client_email: str,
    filename: str,
    index_name: str,
    existing_metadata: List[Dict[str, Any]]
) -> bool:
    """
    Check if the same Excel file has already been uploaded by the same client.
    Updated to work with new metadata format.

    Args:
        client_email: Client email identifier
        filename: Excel filename
        index_name: FAISS index name
        existing_metadata: Existing metadata from FAISS index

    Returns:
        True if duplicate found, False otherwise
    """
    for item in existing_metadata:
        if (item.get('client_email') == client_email and
            item.get('file_uploaded') == filename):
            return True
    return False
