"""
Language Detection and Multi-Language Support Utilities

This module provides language detection capabilities and utilities for 
multi-language content processing in the FAISS backend system.
"""

import re
from typing import Dict, List, Optional, Tuple
import pandas as pd
import logging

# Configure logging
logger = logging.getLogger(__name__)

# Language detection patterns using Unicode ranges
LANGUAGE_PATTERNS = {
    'Tamil': re.compile(r'[\u0B80-\u0BFF]'),
    'Telugu': re.compile(r'[\u0C00-\u0C7F]'),
    'Kannada': re.compile(r'[\u0C80-\u0CFF]'),
    'Hindi': re.compile(r'[\u0900-\u097F]'),
    'Malayalam': re.compile(r'[\u0D00-\u0D7F]'),
    'Bengali': re.compile(r'[\u0980-\u09FF]'),
    'Gujarati': re.compile(r'[\u0A80-\u0AFF]'),
    'Punjabi': re.compile(r'[\u0A00-\u0A7F]'),
    'Marathi': re.compile(r'[\u0900-\u097F]'),  # Same as Hindi
    'Oriya': re.compile(r'[\u0B00-\u0B7F]'),
}

# Priority languages for South Indian language detection
PRIORITY_LANGUAGES = ['Tamil', 'Telugu', 'Kannada']

# Language code mappings
LANGUAGE_CODE_MAP = {
    'Tamil': 'ta',
    'tamil': 'ta',
    'Telugu': 'te',
    'telugu': 'te',
    'Kannada': 'kn',
    'kannada': 'kn',
    'Hindi': 'hi',
    'hindi': 'hi',
    'English': 'en',
    'english': 'en',
    'Bengali': 'bn',
    'bengali': 'bn',
    'Gujarati': 'gu',
    'gujarati': 'gu',
    'Punjabi': 'pa',
    'punjabi': 'pa',
    'Malayalam': 'ml',
    'malayalam': 'ml',
    'Marathi': 'mr',
    'marathi': 'mr',
    'Oriya': 'or',
    'oriya': 'or',
    # Language codes to themselves
    'ta': 'ta',
    'te': 'te',
    'kn': 'kn',
    'hi': 'hi',
    'en': 'en',
    'bn': 'bn',
    'gu': 'gu',
    'pa': 'pa',
    'ml': 'ml',
    'mr': 'mr',
    'or': 'or'
}

# Language to index mapping
LANGUAGE_INDEX_MAPPING = {
    'Tamil': 'default',
    'Telugu': 'default-telugu',
    'Kannada': 'default-kannada',
    'Hindi': 'default-hindi',
    'Malayalam': 'default-malayalam',
    'Bengali': 'default-bengali',
    'Gujarati': 'default-gujarati',
    'Punjabi': 'default-punjabi',
    'Marathi': 'default-marathi',
    'Oriya': 'default-oriya',
    'English': 'default',
    'default': 'default'
}

def detect_language_from_text(text: str, confidence_threshold: float = 0.1) -> str:
    """
    Detect language from text content using Unicode character patterns.
    
    Args:
        text: Input text to analyze
        confidence_threshold: Minimum ratio of language-specific characters needed
        
    Returns:
        str: Detected language name ('Tamil', 'Telugu', etc.) or 'English' as default
    """
    if not text or not isinstance(text, str):
        return 'English'
    
    text = text.strip()
    if not text:
        return 'English'
    
    # Count characters for each language
    language_scores = {}
    total_chars = len(text)
    
    for language, pattern in LANGUAGE_PATTERNS.items():
        matches = pattern.findall(text)
        if matches:
            score = len(''.join(matches)) / total_chars
            language_scores[language] = score
            print(f"🔍 Language detection - {language}: {score:.3f} ({len(''.join(matches))}/{total_chars} chars)")
    
    # Find language with highest score above threshold
    if language_scores:
        best_language = max(language_scores.items(), key=lambda x: x[1])
        if best_language[1] >= confidence_threshold:
            print(f"✅ Language detected: {best_language[0]} (confidence: {best_language[1]:.3f})")
            return best_language[0]
    
    print(f"🔤 No specific language detected, defaulting to English")
    return 'English'

def detect_language_from_dataframe(df: pd.DataFrame, sample_size: int = 50) -> str:
    """
    Detect language from pandas DataFrame content.
    
    Args:
        df: Pandas DataFrame to analyze
        sample_size: Number of text samples to analyze per column
        
    Returns:
        str: Detected language name
    """
    sample_texts = []
    
    # Collect text samples from string columns
    for column in df.columns:
        if df[column].dtype == 'object':  # String columns
            sample_values = df[column].dropna().head(sample_size)
            for value in sample_values:
                if isinstance(value, str) and len(value.strip()) > 0:
                    sample_texts.append(value)
    
    # Combine samples and detect language
    combined_text = ' '.join(sample_texts[:200])  # Limit to avoid memory issues
    return detect_language_from_text(combined_text)

def get_index_name_for_language(detected_language: str, user_selected_index: Optional[str] = None) -> str:
    """
    Get appropriate FAISS index name based on detected language and user preference.
    
    Args:
        detected_language: Language detected from content
        user_selected_index: User-specified index name (optional)
        
    Returns:
        str: FAISS index name to use
    """
    # If user explicitly selected an index, respect that choice
    if user_selected_index and user_selected_index != 'auto':
        print(f"🎯 Using user-selected index: {user_selected_index}")
        return user_selected_index
    
    # Map detected language to appropriate index
    index_name = LANGUAGE_INDEX_MAPPING.get(detected_language, 'default')
    print(f"🌏 Language '{detected_language}' mapped to index: {index_name}")
    return index_name

def is_language_text(text: str, language: str) -> bool:
    """
    Check if text contains characters from a specific language.
    
    Args:
        text: Text to check
        language: Language name to check for
        
    Returns:
        bool: True if text contains the specified language characters
    """
    if not text or not isinstance(text, str) or language not in LANGUAGE_PATTERNS:
        return False
    
    pattern = LANGUAGE_PATTERNS[language]
    return bool(pattern.search(text))

def get_supported_languages() -> List[str]:
    """
    Get list of supported languages for detection.
    
    Returns:
        List[str]: List of supported language names
    """
    return list(LANGUAGE_PATTERNS.keys()) + ['English']

def get_language_statistics(text: str) -> Dict[str, float]:
    """
    Get detailed statistics about language character distribution in text.
    
    Args:
        text: Text to analyze
        
    Returns:
        Dict[str, float]: Language names mapped to character ratio scores
    """
    if not text or not isinstance(text, str):
        return {}
    
    total_chars = len(text)
    if total_chars == 0:
        return {}
    
    stats = {}
    for language, pattern in LANGUAGE_PATTERNS.items():
        matches = pattern.findall(text)
        if matches:
            char_count = len(''.join(matches))
            ratio = char_count / total_chars
            stats[language] = ratio
    
    return stats


class EnhancedLanguageDetectionService:
    """
    Enhanced language detection service with improved accuracy and confidence scoring.
    Specifically optimized for Tamil, Telugu, and Kannada language detection.
    """

    def __init__(self, confidence_threshold: float = 0.1, min_chars_threshold: int = 5):
        """
        Initialize the enhanced language detection service.

        Args:
            confidence_threshold: Minimum ratio of language-specific characters needed
            min_chars_threshold: Minimum number of language-specific characters needed
        """
        self.confidence_threshold = confidence_threshold
        self.min_chars_threshold = min_chars_threshold
        logger.info(f"Enhanced Language Detection Service initialized with confidence_threshold={confidence_threshold}, min_chars_threshold={min_chars_threshold}")

    def detect_language_with_confidence(self, text: str) -> Tuple[str, float, Dict[str, float]]:
        """
        Detect language with confidence score and detailed statistics.

        Args:
            text: Input text to analyze

        Returns:
            Tuple[str, float, Dict[str, float]]: (detected_language, confidence, all_scores)
        """
        if not text or not isinstance(text, str):
            return 'English', 0.0, {}

        text = text.strip()
        if not text:
            return 'English', 0.0, {}

        # Get detailed language statistics
        language_scores = self._calculate_language_scores(text)

        # Apply priority weighting for South Indian languages
        weighted_scores = self._apply_priority_weighting(language_scores)

        # Find best language with confidence validation
        detected_language, confidence = self._select_best_language(weighted_scores, text)

        logger.debug(f"Language detection result: {detected_language} (confidence: {confidence:.3f})")
        return detected_language, confidence, weighted_scores

    def detect_csv_language(self, df: pd.DataFrame, sample_size: int = 100) -> Tuple[str, float, Dict[str, any]]:
        """
        Detect language from CSV/DataFrame content with enhanced sampling.

        Args:
            df: Pandas DataFrame to analyze
            sample_size: Number of text samples to analyze per column

        Returns:
            Tuple[str, float, Dict]: (detected_language, confidence, metadata)
        """
        sample_texts = []
        column_stats = {}

        # Collect text samples from string columns with better sampling
        for column in df.columns:
            if df[column].dtype == 'object':  # String columns
                # Get diverse samples from the column
                column_values = df[column].dropna()
                if len(column_values) > 0:
                    # Sample from beginning, middle, and end for better representation
                    sample_indices = self._get_diverse_sample_indices(len(column_values), sample_size)
                    column_samples = [str(column_values.iloc[i]) for i in sample_indices
                                    if isinstance(column_values.iloc[i], str) and len(str(column_values.iloc[i]).strip()) > 0]

                    sample_texts.extend(column_samples)
                    column_stats[column] = {
                        'total_values': len(column_values),
                        'samples_taken': len(column_samples),
                        'sample_text_length': sum(len(text) for text in column_samples)
                    }

        # Combine samples and detect language
        combined_text = ' '.join(sample_texts[:500])  # Limit to avoid memory issues
        detected_language, confidence, scores = self.detect_language_with_confidence(combined_text)

        metadata = {
            'total_samples': len(sample_texts),
            'combined_text_length': len(combined_text),
            'column_stats': column_stats,
            'language_scores': scores
        }

        logger.info(f"CSV language detection: {detected_language} (confidence: {confidence:.3f}) from {len(sample_texts)} samples")
        return detected_language, confidence, metadata

    def should_use_direct_retrieval(self, query_language: str, data_language: str) -> bool:
        """
        Determine if direct retrieval should be used (same language) or translation flow.

        Args:
            query_language: Language of the user query
            data_language: Language of the stored data

        Returns:
            bool: True if direct retrieval should be used, False if translation is needed
        """
        # Direct retrieval for same language
        if query_language == data_language:
            logger.info(f"Direct retrieval: Query and data both in {query_language}")
            return True

        # Direct retrieval for priority languages when data is in the same language
        if query_language in PRIORITY_LANGUAGES and data_language == query_language:
            logger.info(f"Direct retrieval: Priority language {query_language} match")
            return True

        logger.info(f"Translation flow: Query in {query_language}, data in {data_language}")
        return False

    def get_language_code(self, language_name: str) -> str:
        """
        Get language code for a language name.

        Args:
            language_name: Full language name

        Returns:
            str: Language code (e.g., 'ta' for Tamil)
        """
        return LANGUAGE_CODE_MAP.get(language_name, 'en')

    def _calculate_language_scores(self, text: str) -> Dict[str, float]:
        """Calculate raw language scores based on character patterns."""
        total_chars = len(text)
        if total_chars == 0:
            return {}

        scores = {}
        for language, pattern in LANGUAGE_PATTERNS.items():
            matches = pattern.findall(text)
            if matches:
                char_count = len(''.join(matches))
                if char_count >= self.min_chars_threshold:
                    ratio = char_count / total_chars
                    scores[language] = ratio
                    logger.debug(f"Language {language}: {char_count}/{total_chars} chars (ratio: {ratio:.3f})")

        return scores

    def _apply_priority_weighting(self, scores: Dict[str, float]) -> Dict[str, float]:
        """Apply priority weighting for South Indian languages."""
        weighted_scores = scores.copy()

        # Boost priority languages slightly to prefer them in close matches
        for language in PRIORITY_LANGUAGES:
            if language in weighted_scores:
                # Small boost (5%) for priority languages
                weighted_scores[language] *= 1.05
                logger.debug(f"Applied priority boost to {language}: {weighted_scores[language]:.3f}")

        return weighted_scores

    def _select_best_language(self, scores: Dict[str, float], text: str) -> Tuple[str, float]:
        """Select the best language based on scores and validation."""
        if not scores:
            return 'English', 0.0

        # Find language with highest score
        best_language, best_score = max(scores.items(), key=lambda x: x[1])

        # Validate confidence threshold
        if best_score >= self.confidence_threshold:
            # Additional validation for very low scores
            if best_score < 0.3:
                # Check if we have enough language-specific characters
                pattern = LANGUAGE_PATTERNS.get(best_language)
                if pattern:
                    matches = pattern.findall(text)
                    char_count = len(''.join(matches))
                    if char_count < self.min_chars_threshold:
                        logger.debug(f"Insufficient characters for {best_language}: {char_count} < {self.min_chars_threshold}")
                        return 'English', 0.0

            return best_language, best_score

        logger.debug(f"Best score {best_score:.3f} below threshold {self.confidence_threshold}")
        return 'English', 0.0

    def _get_diverse_sample_indices(self, total_length: int, sample_size: int) -> List[int]:
        """Get diverse sample indices from beginning, middle, and end."""
        if total_length <= sample_size:
            return list(range(total_length))

        # Distribute samples across the dataset
        indices = []

        # Beginning (30%)
        begin_count = max(1, int(sample_size * 0.3))
        indices.extend(range(min(begin_count, total_length)))

        # Middle (40%)
        middle_count = max(1, int(sample_size * 0.4))
        middle_start = max(begin_count, total_length // 2 - middle_count // 2)
        middle_end = min(total_length, middle_start + middle_count)
        indices.extend(range(middle_start, middle_end))

        # End (30%)
        end_count = sample_size - len(indices)
        if end_count > 0:
            end_start = max(middle_end, total_length - end_count)
            indices.extend(range(end_start, total_length))

        # Remove duplicates and sort
        return sorted(list(set(indices)))


# Global instance for easy access
enhanced_language_detector = EnhancedLanguageDetectionService()
