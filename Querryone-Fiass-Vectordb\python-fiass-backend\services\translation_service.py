"""
Translation Service for FAISS Backend
Provides language detection and translation capabilities for bot responses.
"""

import os
import re
import json
import time
import hashlib
import requests
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

# Import Deep Translator
try:
    from deep_translator import GoogleTranslator
    TRANSLATOR_AVAILABLE = True
    print("✅ Deep Translator library loaded successfully")
except ImportError:
    TRANSLATOR_AVAILABLE = False
    print("⚠️ Deep Translator library not available - using fallback translations")

class TranslationService:
    """
    Service for handling language detection and translation of bot responses.
    Supports multiple translation providers with caching for performance.
    """
    
    def __init__(self):
        self.cache = {}  # In-memory cache for translations
        self.cache_expiry = 3600  # 1 hour cache expiry
        self.supported_languages = {
            'en': 'English',
            'ta': 'Tamil',
            'te': 'Telugu',
            'kn': 'Kannada',
            'hi': 'Hindi',
            'es': 'Spanish',
            'fr': 'French',
            'de': 'German',
            'zh': 'Chinese',
            'ja': 'Japanese',
            'ko': 'Korean',
            'ar': 'Arabic'
        }

        # Configuration for timeouts and retries
        self.api_timeout = 20  # Increased timeout for API calls
        self.max_retries = 3
        self.retry_delay = 1.0  # Delay between retries in seconds

        # Deep Translator doesn't need initialization - it's used directly
        self.translator_available = TRANSLATOR_AVAILABLE
        if self.translator_available:
            print("✅ Deep Translator ready for use")
        else:
            print("⚠️ Deep Translator not available")

        # Initialize error tracking
        self.error_counts = {
            'deep_translator': 0,
            'mymemory': 0,
            'libretranslate': 0,
            'total_requests': 0,
            'successful_requests': 0
        }
        
    def detect_language(self, text: str) -> str:
        """
        Detect the language of the input text.
        
        Args:
            text: Input text to analyze
            
        Returns:
            Language code (e.g., 'en', 'ta', 'hi')
        """
        if not text or not text.strip():
            return 'en'  # Default to English
            
        # Tamil detection using Unicode ranges
        if self._is_tamil_text(text):
            return 'ta'
            
        # Telugu detection using Unicode ranges
        if self._is_telugu_text(text):
            return 'te'
            
        # Kannada detection using Unicode ranges
        if self._is_kannada_text(text):
            return 'kn'
            
        # Hindi detection using Unicode ranges
        if self._is_hindi_text(text):
            return 'hi'
            
        # Arabic detection
        if self._is_arabic_text(text):
            return 'ar'
            
        # Chinese detection
        if self._is_chinese_text(text):
            return 'zh'
            
        # Default to English for other cases
        return 'en'
    
    def _is_tamil_text(self, text: str) -> bool:
        """Check if text contains Tamil characters"""
        tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
        return bool(tamil_pattern.search(text))
    
    def _is_telugu_text(self, text: str) -> bool:
        """Check if text contains Telugu characters"""
        telugu_pattern = re.compile(r'[\u0C00-\u0C7F]')
        return bool(telugu_pattern.search(text))
    
    def _is_kannada_text(self, text: str) -> bool:
        """Check if text contains Kannada characters"""
        kannada_pattern = re.compile(r'[\u0C80-\u0CFF]')
        return bool(kannada_pattern.search(text))
    
    def _is_hindi_text(self, text: str) -> bool:
        """Check if text contains Hindi/Devanagari characters"""
        hindi_pattern = re.compile(r'[\u0900-\u097F]')
        return bool(hindi_pattern.search(text))
    
    def _is_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_pattern = re.compile(r'[\u0600-\u06FF]')
        return bool(arabic_pattern.search(text))
    
    def _is_chinese_text(self, text: str) -> bool:
        """Check if text contains Chinese characters"""
        chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
        return bool(chinese_pattern.search(text))
    
    def _generate_cache_key(self, text: str, source_lang: str, target_lang: str) -> str:
        """Generate a cache key for translation"""
        content = f"{text}|{source_lang}|{target_lang}"
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _is_cache_valid(self, cache_entry: Dict) -> bool:
        """Check if cache entry is still valid"""
        if 'timestamp' not in cache_entry:
            return False
        
        cache_time = datetime.fromisoformat(cache_entry['timestamp'])
        expiry_time = cache_time + timedelta(seconds=self.cache_expiry)
        return datetime.now() < expiry_time
    
    def translate_text(self, text: str, target_lang: str, source_lang: str = None) -> Dict[str, str]:
        """
        Translate text to target language.
        
        Args:
            text: Text to translate
            target_lang: Target language code
            source_lang: Source language code (auto-detect if None)
            
        Returns:
            Dictionary with translation result and metadata
        """
        if not text or not text.strip():
            return {
                'translated_text': text,
                'source_language': 'unknown',
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Auto-detect source language if not provided
        if not source_lang:
            source_lang = self.detect_language(text)
        
        # If source and target are the same, return original text
        if source_lang == target_lang:
            return {
                'translated_text': text,
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': 'none',
                'cached': False
            }
        
        # Check cache first
        cache_key = self._generate_cache_key(text, source_lang, target_lang)
        if cache_key in self.cache and self._is_cache_valid(self.cache[cache_key]):
            cached_result = self.cache[cache_key]
            return {
                'translated_text': cached_result['translated_text'],
                'source_language': source_lang,
                'target_language': target_lang,
                'translation_provider': cached_result.get('provider', 'cached'),
                'cached': True
            }
        
        # Track request
        self.error_counts['total_requests'] += 1

        # Attempt translation using available providers
        translation_result = self._attempt_translation(text, source_lang, target_lang)

        # Track success/failure
        if translation_result['success']:
            self.error_counts['successful_requests'] += 1

        # Cache the result
        if translation_result['success']:
            self.cache[cache_key] = {
                'translated_text': translation_result['translated_text'],
                'provider': translation_result['provider'],
                'timestamp': datetime.now().isoformat()
            }

        return {
            'translated_text': translation_result['translated_text'],
            'source_language': source_lang,
            'target_language': target_lang,
            'translation_provider': translation_result['provider'],
            'cached': False
        }
    
    def _attempt_translation(self, text: str, source_lang: str, target_lang: str) -> Dict:
        """
        Attempt translation using available providers with improved error handling.

        Returns:
            Dictionary with translation result
        """
        # Enhanced logging for debugging
        print(f"🔄 Starting translation attempt: '{text[:30]}...' ({source_lang} -> {target_lang})")

        # Try multiple translation approaches

        # 1. Try Deep Translator first if available
        if self.translator_available and TRANSLATOR_AVAILABLE:
            try:
                print(f"🌐 Attempting Deep Translator: {source_lang} -> {target_lang}")

                # Use Deep Translator (GoogleTranslator) with retry logic
                translator = GoogleTranslator(source=source_lang, target=target_lang)

                # Add retry logic for Deep Translator
                max_retries = 2
                for attempt in range(max_retries):
                    try:
                        # Normalize input text before translation
                        normalized_text = text.normalize('NFC')
                        result = translator.translate(normalized_text)

                        if result and result.strip() and result != text and len(result.strip()) > 0:
                            # Normalize the result to ensure proper Unicode handling
                            normalized_result = result.strip().normalize('NFC')
                            
                            # Enhanced validation for multilingual content
                            if not self._is_translation_valid(text, normalized_result, source_lang, target_lang):
                                print(f"⚠️ Deep Translator result failed validation on attempt {attempt + 1}")
                                if attempt < max_retries - 1:
                                    time.sleep(1)  # Brief delay before retry
                                    continue

                            # Additional check for Unicode corruption
                            if self._has_unicode_corruption(normalized_result):
                                print(f"⚠️ Deep Translator result has Unicode corruption on attempt {attempt + 1}")
                                if attempt < max_retries - 1:
                                    time.sleep(1)
                                    continue

                            print(f"✅ Deep Translator successful: {normalized_result[:50]}...")
                            return {
                                'success': True,
                                'translated_text': normalized_result,
                                'provider': 'deep_translator_google'
                            }
                        else:
                            print(f"⚠️ Deep Translator returned empty or unchanged result on attempt {attempt + 1}")
                            if attempt < max_retries - 1:
                                time.sleep(1)  # Brief delay before retry

                    except Exception as retry_e:
                        print(f"❌ Deep Translator retry {attempt + 1} error: {retry_e}")
                        if attempt < max_retries - 1:
                            time.sleep(1)  # Brief delay before retry

            except Exception as e:
                print(f"❌ Deep Translator error: {e}")

        # 2. Try MyMemory Translation API as backup
        try:
            print(f"🌐 Attempting MyMemory API: {source_lang} -> {target_lang}")
            result = self._translate_with_mymemory(text, source_lang, target_lang)
            if result and result != text:
                print(f"✅ MyMemory API successful: {result[:50]}...")
                return {
                    'success': True,
                    'translated_text': result,
                    'provider': 'mymemory_api'
                }
        except Exception as e:
            print(f"❌ MyMemory API error: {e}")

        # 3. Try LibreTranslate API as another backup
        try:
            print(f"🌐 Attempting LibreTranslate API: {source_lang} -> {target_lang}")
            result = self._translate_with_libretranslate(text, source_lang, target_lang)
            if result and result != text:
                print(f"✅ LibreTranslate API successful: {result[:50]}...")
                return {
                    'success': True,
                    'translated_text': result,
                    'provider': 'libretranslate_api'
                }
        except Exception as e:
            print(f"❌ LibreTranslate API error: {e}")

        # 4. Try the enhanced retry method as a comprehensive fallback
        print(f"🔄 Attempting comprehensive retry translation")
        retry_result = self._translate_with_retry(text, source_lang, target_lang)
        if retry_result:
            print(f"✅ Retry translation successful: {retry_result[:50]}...")
            return {
                'success': True,
                'translated_text': retry_result,
                'provider': 'retry_comprehensive'
            }

        # 5. Fallback to pattern-based translations for common cases
        fallback_translations = self._get_fallback_translations(text, source_lang, target_lang)

        if fallback_translations and fallback_translations != text:
            print(f"🔄 Using fallback translation")
            return {
                'success': True,
                'translated_text': fallback_translations,
                'provider': 'fallback'
            }

        # If no translation available, return original text
        print(f"⚠️ No translation available, returning original text")
        return {
            'success': False,
            'translated_text': text,
            'provider': 'none'
        }
    
    def _get_fallback_translations(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Provide fallback translations for common phrases and responses.
        This is a simplified approach - in production you'd use proper translation APIs.
        """
        
        # Common response patterns and their translations
        translation_patterns = {
            ('en', 'ta'): {
                'Based on the provided information': 'வழங்கப்பட்ட தகவலின் அடிப்படையில்',
                'According to the document': 'ஆவணத்தின் படி',
                'The key points are': 'முக்கிய புள்ளிகள்',
                'In summary': 'சுருக்கமாக',
                'Therefore': 'எனவே',
                'However': 'இருப்பினும்',
                'Additionally': 'கூடுதலாக',
                'Furthermore': 'மேலும்',
                'Reserve Bank of India': 'இந்திய ரிசர்வ் வங்கி',
                'cryptocurrency': 'கிரிப்டோகரன்சி',
                'digital assets': 'டிஜிட்டல் ஆசெட்கள்',
                'regulation': 'ஒழுங்குமுறை',
                'financial institution': 'நிதி நிறுவனம்',
                'ban': 'தடை',
                'Supreme Court': 'உச்ச நீதிமன்றம்',
                'government': 'அரசு',
                'bill': 'சட்டமுன்வடிவு'
            },
            ('ta', 'en'): {
                'வழங்கப்பட்ட தகவலின் அடிப்படையில்': 'Based on the provided information',
                'ஆவணத்தின் படி': 'According to the document',
                'முக்கிய புள்ளிகள்': 'The key points are',
                'சுருக்கமாக': 'In summary',
                'எனவே': 'Therefore',
                'இருப்பினும்': 'However',
                'கூடுதலாக': 'Additionally',
                'மேலும்': 'Furthermore'
            },
            ('en', 'te'): {
                'Based on the provided information': 'అందించిన సమాచారం ఆధారంగా',
                'According to the document': 'పత్రం ప్రకారం',
                'The key points are': 'ముఖ్య అంశాలు',
                'In summary': 'సంక్షేపంలో',
                'Therefore': 'కాబట్టి',
                'However': 'అయితే',
                'Additionally': 'అదనంగా',
                'Furthermore': 'ఇంకా',
                'Reserve Bank of India': 'భారతీయ రిజర్వ్ బ్యాంక్',
                'cryptocurrency': 'క్రిప్టోకరెన్సీ',
                'digital assets': 'డిజిటల్ ఆస్తులు',
                'regulation': 'రెగ్యులేషన్',
                'financial institution': 'ఫైనాన్షియల్ ఇన్స్టిట్యూషన్',
                'ban': 'నిషేధం',
                'Supreme Court': 'సుప్రీం కోర్టు',
                'government': 'ప్రభుత్వం',
                'bill': 'బిల్లు',
                # Additional financial terms
                'mutual funds': 'మ్యూచువల్ ఫండ్స్',
                'investment': 'పెట్టుబడి',
                'strategies': 'వ్యూహాలు',
                'benefits': 'ప్రయోజనాలు',
                'Systematic Investment Plan': 'సిస్టమాటిక్ ఇన్వెస్ట్‌మెంట్ ప్లాన్',
                'SIP': 'SIP',
                'Invest a fixed amount regularly': 'క్రమం తప్పకుండా నిర్ణీత మొత్తాన్ని పెట్టుబడి పెట్టండి',
                'monthly': 'నెలవారీ',
                'quarterly': 'త్రైమాసిక',
                'rupee cost averaging': 'రూపాయి కాస్ట్ యావరేజింగ్',
                'Reduces market timing risk': 'మార్కెట్ టైమింగ్ రిస్క్‌ను తగ్గిస్తుంది',
                'Diversification': 'వైవిధ్యీకరణ',
                'Invest across different categories': 'వివిధ వర్గాలలో పెట్టుబడి పెట్టండి',
                'equity': 'ఈక్విటీ',
                'debt': 'డెట్',
                'hybrid': 'హైబ్రిడ్',
                'minimize risk': 'రిస్క్‌ను తగ్గించండి',
                'Example': 'ఉదాహరణ',
                'Large-cap': 'లార్జ్-క్యాప్',
                'mid-cap': 'మిడ్-క్యాప్',
                'sectoral funds': 'సెక్టోరల్ ఫండ్స్',
                'Long-Term Investing': 'దీర్ఘకాలిక పెట్టుబడి',
                'Staying invested for': 'పెట్టుబడిలో ఉండటం',
                'years': 'సంవత్సరాలు',
                'helps ride out market volatility': 'మార్కెట్ అస్థిరతను అధిగమించడంలో సహాయపడుతుంది',
                'benefit from compounding': 'కాంపౌండింగ్ నుండి ప్రయోజనం పొందండి',
                'Asset Allocation': 'ఆస్తి కేటాయింపు',
                'Adjust equity-debt ratio': 'ఈక్విటీ-డెట్ నిష్పత్తిని సర్దుబాటు చేయండి',
                'based on risk appetite': 'రిస్క్ ఆకలి ఆధారంగా',
                'moderate risk': 'మోడరేట్ రిస్క్',
                'Tax-Efficient Funds': 'పన్ను-సమర్థవంతమైన ఫండ్స్',
                'ELSS': 'ELSS',
                'Equity-Linked Savings Scheme': 'ఈక్విటీ-లింక్డ్ సేవింగ్స్ స్కీమ్',
                'tax deductions': 'పన్ను మినహాయింపులు',
                'Section 80C': 'సెక్షన్ 80C',
                'Benefits of Mutual Funds': 'మ్యూచువల్ ఫండ్స్ యొక్క ప్రయోజనాలు',
                'Professional Management': 'వృత్తిపరమైన నిర్వహణ',
                'Managed by experts': 'నిపుణులచే నిర్వహించబడుతుంది'
            },
            ('te', 'en'): {
                'అందించిన సమాచారం ఆధారంగా': 'Based on the provided information',
                'పత్రం ప్రకారం': 'According to the document',
                'ముఖ్య అంశాలు': 'The key points are',
                'సంక్షేపంలో': 'In summary',
                'కాబట్టి': 'Therefore',
                'అయితే': 'However',
                'అదనంగా': 'Additionally',
                'ఇంకా': 'Furthermore'
            },
            ('en', 'kn'): {
                'Based on the provided information': 'ಒದಗಿಸಿದ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ',
                'According to the document': 'ದಾಖಲೆಯ ಪ್ರಕಾರ',
                'The key points are': 'ಮುಖ್ಯ ಅಂಶಗಳು',
                'In summary': 'ಸಂಕ್ಷೇಪವಾಗಿ',
                'Therefore': 'ಆದ್ದರಿಂದ',
                'However': 'ಆದರೆ',
                'Additionally': 'ಹೆಚ್ಚುವರಿಯಾಗಿ',
                'Furthermore': 'ಇದಲ್ಲದೆ',
                'Reserve Bank of India': 'ಭಾರತೀಯ ರಿಸರ್ವ್ ಬ್ಯಾಂಕ್',
                'cryptocurrency': 'ಕ್ರಿಪ್ಟೋಕರೆನ್ಸಿ',
                'digital assets': 'ಡಿಜಿಟಲ್ ಆಸ್ತಿಗಳು',
                'regulation': 'ನಿಯಂತ್ರಣ',
                'financial institution': 'ಹಣಕಾಸು ಸಂಸ್ಥೆ',
                'ban': 'ನಿಷೇಧ',
                'Supreme Court': 'ಸುಪ್ರೀಂ ಕೋರ್ಟ್',
                'government': 'ಸರ್ಕಾರ',
                'bill': 'ಬಿಲ್ಲು'
            },
            ('kn', 'en'): {
                'ಒದಗಿಸಿದ ಮಾಹಿತಿಯ ಆಧಾರದ ಮೇಲೆ': 'Based on the provided information',
                'ದಾಖಲೆಯ ಪ್ರಕಾರ': 'According to the document',
                'ಮುಖ್ಯ ಅಂಶಗಳು': 'The key points are',
                'ಸಂಕ್ಷೇಪವಾಗಿ': 'In summary',
                'ಆದ್ದರಿಂದ': 'Therefore',
                'ಆದರೆ': 'However',
                'ಹೆಚ್ಚುವರಿಯಾಗಿ': 'Additionally',
                'ಇದಲ್ಲದೆ': 'Furthermore'
            }
        }
        
        pattern_key = (source_lang, target_lang)
        if pattern_key in translation_patterns:
            patterns = translation_patterns[pattern_key]

            # Enhanced pattern matching and replacement
            translated_text = text
            replacements_made = 0

            # Sort patterns by length (longest first) for better matching
            sorted_patterns = sorted(patterns.items(), key=lambda x: len(x[0]), reverse=True)

            for source_phrase, target_phrase in sorted_patterns:
                # Case-insensitive replacement for better matching
                import re
                pattern = re.compile(re.escape(source_phrase), re.IGNORECASE)
                if pattern.search(translated_text):
                    translated_text = pattern.sub(target_phrase, translated_text)
                    replacements_made += 1

            # If any replacements were made, return the translated text
            if replacements_made > 0:
                print(f"✅ Fallback translation made {replacements_made} replacements")
                return translated_text

        return None

    def _translate_with_mymemory(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Translate using MyMemory API (free translation service)
        """
        try:
            url = "https://api.mymemory.translated.net/get"
            params = {
                'q': text,
                'langpair': f"{source_lang}|{target_lang}"
            }

            # Use configurable timeout
            response = requests.get(url, params=params, timeout=self.api_timeout)
            if response.status_code == 200:
                data = response.json()
                if data.get('responseStatus') == 200:
                    translated_text = data.get('responseData', {}).get('translatedText', '')
                    if translated_text and translated_text.strip() and translated_text != text:
                        return translated_text.strip()

        except requests.exceptions.Timeout:
            print(f"MyMemory API timeout error for {source_lang} -> {target_lang}")
            self.error_counts['mymemory'] += 1
        except requests.exceptions.ConnectionError:
            print(f"MyMemory API connection error for {source_lang} -> {target_lang}")
            self.error_counts['mymemory'] += 1
        except Exception as e:
            print(f"MyMemory API error: {e}")
            self.error_counts['mymemory'] += 1

        return None

    def _translate_with_libretranslate(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Translate using LibreTranslate API (free and open source)
        """
        # List of LibreTranslate instances to try
        libretranslate_urls = [
            "https://libretranslate.de/translate",
            "https://translate.argosopentech.com/translate",
            "https://libretranslate.com/translate"
        ]

        for url in libretranslate_urls:
            try:
                data = {
                    'q': text,
                    'source': source_lang,
                    'target': target_lang,
                    'format': 'text'
                }

                headers = {
                    'Content-Type': 'application/json'
                }

                # Use configurable timeout
                response = requests.post(url, json=data, headers=headers, timeout=self.api_timeout)
                if response.status_code == 200:
                    result = response.json()
                    translated_text = result.get('translatedText', '')
                    if translated_text and translated_text.strip() and translated_text != text:
                        print(f"✅ LibreTranslate successful with {url}")
                        return translated_text.strip()

            except requests.exceptions.Timeout:
                print(f"LibreTranslate timeout error with {url}")
                self.error_counts['libretranslate'] += 1
                continue
            except requests.exceptions.ConnectionError:
                print(f"LibreTranslate connection error with {url}")
                self.error_counts['libretranslate'] += 1
                continue
            except Exception as e:
                print(f"LibreTranslate API error with {url}: {e}")
                self.error_counts['libretranslate'] += 1
                continue

        return None

    def _is_translation_valid(self, original_text: str, translated_text: str, source_lang: str, target_lang: str) -> bool:
        """
        Validate if the translation result is reasonable
        """
        if not translated_text or not translated_text.strip():
            return False

        # Check if translation is identical to original (might indicate failure)
        if translated_text.strip() == original_text.strip():
            return False

        # Check if translation is too short compared to original (might indicate truncation)
        if len(translated_text.strip()) < len(original_text.strip()) * 0.3:
            return False

        # Check if translation contains only special characters or numbers
        import re
        if re.match(r'^[^a-zA-Z\u0080-\uFFFF]*$', translated_text.strip()):
            return False

        # Check for obvious translation failures (repeated characters, etc.)
        if len(set(translated_text.strip())) < 3 and len(translated_text.strip()) > 10:
            return False

        return True

    def _has_unicode_corruption(self, text: str) -> bool:
        """
        Check if the text has Unicode corruption patterns
        """
        if not text:
            return False
            
        # Check for common Unicode corruption patterns
        corruption_patterns = [
            # Replacement character (indicates encoding issues)
            '\ufffd',
            # Common corruption patterns for Indian languages
            '??',
            # Broken Unicode sequences
            '\ud800',  # High surrogate without low surrogate
            '\udc00',  # Low surrogate without high surrogate
        ]
        
        for pattern in corruption_patterns:
            if pattern in text:
                try:
                    print(f"⚠️ Unicode corruption detected: {pattern}")
                except UnicodeEncodeError:
                    print(f"⚠️ Unicode corruption detected: [unprintable character]")
                return True
                
        # Check for excessive question marks (often indicates encoding failure)
        question_mark_ratio = text.count('?') / len(text) if len(text) > 0 else 0
        if question_mark_ratio > 0.3:  # More than 30% question marks
            print(f"⚠️ Excessive question marks detected: {question_mark_ratio:.2%}")
            return True
            
        return False

    def _translate_with_retry(self, text: str, source_lang: str, target_lang: str, max_retries: int = 3) -> Optional[str]:
        """
        Translate text with retry logic and multiple providers
        """
        providers = [
            ('deep_translator', self._translate_with_deep_translator),
            ('mymemory', self._translate_with_mymemory),
            ('libretranslate', self._translate_with_libretranslate)
        ]

        for provider_name, provider_func in providers:
            for attempt in range(max_retries):
                try:
                    print(f"🔄 Trying {provider_name} (attempt {attempt + 1}/{max_retries})")
                    result = provider_func(text, source_lang, target_lang)

                    if result and self._is_translation_valid(text, result, source_lang, target_lang):
                        print(f"✅ {provider_name} successful on attempt {attempt + 1}")
                        return result
                    elif result:
                        print(f"⚠️ {provider_name} returned invalid result on attempt {attempt + 1}")
                    else:
                        print(f"⚠️ {provider_name} returned no result on attempt {attempt + 1}")

                except Exception as e:
                    print(f"❌ {provider_name} error on attempt {attempt + 1}: {e}")

                # Brief delay between retries
                if attempt < max_retries - 1:
                    time.sleep(0.5)

        return None

    def _translate_with_deep_translator(self, text: str, source_lang: str, target_lang: str) -> Optional[str]:
        """
        Translate using Deep Translator (separated for retry logic)
        """
        if not (self.translator_available and TRANSLATOR_AVAILABLE):
            return None

        try:
            translator = GoogleTranslator(source=source_lang, target=target_lang)
            result = translator.translate(text)

            if result and result.strip() and result != text:
                return result.strip()

        except Exception as e:
            print(f"Deep Translator specific error: {e}")
            self.error_counts['deep_translator'] += 1

        return None

    def _translate_long_text(self, text: str, source_lang: str, target_lang: str) -> str:
        """
        Enhanced translation for long text with better Unicode preservation
        """
        if len(text) < 500:  # For shorter texts, translate directly
            result = self._attempt_translation(text, source_lang, target_lang)
            return result['translated_text']

        # Enhanced sentence splitting that preserves Unicode punctuation
        import re
        # Split on multiple punctuation marks including Unicode punctuation
        sentences = re.split(r'(?<=[.!?।॥])\s+', text)
        translated_sentences = []

        print(f"🔄 Translating long text with {len(sentences)} sentences")

        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if sentence:
                # Preserve Unicode normalization before translation
                normalized_sentence = sentence.normalize('NFC')
                
                print(f"📝 Translating sentence {i+1}/{len(sentences)}: {normalized_sentence[:50]}...")
                
                result = self._attempt_translation(normalized_sentence, source_lang, target_lang)
                translated_text = result['translated_text']
                
                # Ensure Unicode normalization after translation
                if translated_text:
                    translated_text = translated_text.normalize('NFC')
                
                translated_sentences.append(translated_text)
                
                # Brief pause between sentences to avoid rate limiting
                if i < len(sentences) - 1:
                    time.sleep(0.1)
            else:
                translated_sentences.append(sentence)

        # Join with proper spacing and normalize the final result
        final_text = ' '.join(translated_sentences).normalize('NFC')
        print(f"✅ Long text translation completed: {len(final_text)} characters")
        
        return final_text

    def translate_response_data(self, response_data: Dict, target_lang: str) -> Dict:
        """
        Enhanced translation for response data with better Unicode preservation.
        
        Args:
            response_data: Response data containing text fields to translate
            target_lang: Target language code
            
        Returns:
            Translated response data with preserved Unicode integrity
        """
        translated_data = response_data.copy()
        
        print(f"🌐 Starting response data translation to {target_lang}")
        
        # Translate main AI response using enhanced method for better results
        if 'ai_response' in translated_data and translated_data['ai_response']:
            original_text = translated_data['ai_response']
            
            # Normalize the original text before translation
            normalized_original = original_text.normalize('NFC') if isinstance(original_text, str) else str(original_text)
            
            print(f"📝 Translating AI response: {len(normalized_original)} characters")
            
            # Use the enhanced long text translation method
            translated_text = self._translate_long_text(
                normalized_original,
                'en',  # Assume AI response is in English
                target_lang
            )
            
            # Validate the translation result
            if self._has_unicode_corruption(translated_text):
                print("⚠️ Unicode corruption detected in AI response translation, using fallback")
                # Try fallback translation
                fallback_result = self._get_fallback_translations(normalized_original, 'en', target_lang)
                if fallback_result and not self._has_unicode_corruption(fallback_result):
                    translated_text = fallback_result
                else:
                    # If all else fails, return original text
                    translated_text = normalized_original
                    print("⚠️ Using original text due to translation issues")
            
            translated_data['ai_response'] = translated_text
            translated_data['translation_metadata'] = {
                'ai_response': {
                    'translated_text': translated_text,
                    'source_language': 'en',
                    'target_language': target_lang,
                    'translation_provider': 'enhanced_unicode_safe',
                    'cached': False,
                    'unicode_safe': not self._has_unicode_corruption(translated_text)
                }
            }
        
        # Translate related questions with Unicode safety
        if 'related_questions' in translated_data and translated_data['related_questions']:
            translated_questions = []
            question_translations = []
            
            print(f"📝 Translating {len(translated_data['related_questions'])} related questions")
            
            for i, question in enumerate(translated_data['related_questions']):
                # Normalize question before translation
                normalized_question = question.normalize('NFC') if isinstance(question, str) else str(question)
                
                translation_result = self.translate_text(normalized_question, target_lang)
                translated_question = translation_result['translated_text']
                
                # Check for Unicode corruption in question translation
                if self._has_unicode_corruption(translated_question):
                    print(f"⚠️ Unicode corruption in question {i+1}, using fallback")
                    fallback_result = self._get_fallback_translations(normalized_question, 'en', target_lang)
                    if fallback_result and not self._has_unicode_corruption(fallback_result):
                        translated_question = fallback_result
                        translation_result['translated_text'] = translated_question
                        translation_result['translation_provider'] = 'fallback_unicode_safe'
                    else:
                        translated_question = normalized_question
                        translation_result['translated_text'] = translated_question
                        translation_result['translation_provider'] = 'original_text'
                
                translated_questions.append(translated_question)
                translation_result['unicode_safe'] = not self._has_unicode_corruption(translated_question)
                question_translations.append(translation_result)
            
            translated_data['related_questions'] = translated_questions
            if 'translation_metadata' not in translated_data:
                translated_data['translation_metadata'] = {}
            translated_data['translation_metadata']['related_questions'] = question_translations
        
        # Add enhanced language metadata
        translated_data['response_language'] = target_lang
        translated_data['translation_timestamp'] = datetime.now().isoformat()
        translated_data['unicode_normalized'] = True
        
        print(f"✅ Response data translation completed for {target_lang}")
        
        return translated_data
    
    def get_cache_stats(self) -> Dict:
        """Get translation cache and error statistics"""
        valid_entries = 0
        expired_entries = 0

        for cache_entry in self.cache.values():
            if self._is_cache_valid(cache_entry):
                valid_entries += 1
            else:
                expired_entries += 1

        # Calculate success rate
        total_requests = self.error_counts['total_requests']
        success_rate = (self.error_counts['successful_requests'] / total_requests * 100) if total_requests > 0 else 0

        return {
            'cache_info': {
                'total_entries': len(self.cache),
                'valid_entries': valid_entries,
                'expired_entries': expired_entries,
                'cache_expiry_seconds': self.cache_expiry
            },
            'translation_stats': {
                'total_requests': total_requests,
                'successful_requests': self.error_counts['successful_requests'],
                'success_rate_percent': round(success_rate, 2),
                'error_counts': {
                    'deep_translator': self.error_counts['deep_translator'],
                    'mymemory': self.error_counts['mymemory'],
                    'libretranslate': self.error_counts['libretranslate']
                }
            },
            'configuration': {
                'api_timeout': self.api_timeout,
                'max_retries': self.max_retries,
                'retry_delay': self.retry_delay,
                'translator_available': self.translator_available
            },
            'supported_languages': self.supported_languages
        }
    
    def clear_cache(self):
        """Clear the translation cache"""
        self.cache.clear()
        print("🧹 Translation cache cleared")

    def reset_error_stats(self):
        """Reset error tracking statistics"""
        self.error_counts = {
            'deep_translator': 0,
            'mymemory': 0,
            'libretranslate': 0,
            'total_requests': 0,
            'successful_requests': 0
        }
        print("📊 Error statistics reset")

    def get_health_status(self) -> Dict:
        """Get the health status of translation services"""
        total_requests = self.error_counts['total_requests']
        if total_requests == 0:
            return {
                'status': 'unknown',
                'message': 'No translation requests made yet',
                'recommendations': ['Make some translation requests to assess health']
            }

        success_rate = (self.error_counts['successful_requests'] / total_requests) * 100

        if success_rate >= 80:
            status = 'healthy'
            message = f'Translation services are working well ({success_rate:.1f}% success rate)'
            recommendations = []
        elif success_rate >= 50:
            status = 'degraded'
            message = f'Translation services are experiencing some issues ({success_rate:.1f}% success rate)'
            recommendations = [
                'Check network connectivity',
                'Consider increasing timeout values',
                'Monitor specific provider errors'
            ]
        else:
            status = 'unhealthy'
            message = f'Translation services are having significant problems ({success_rate:.1f}% success rate)'
            recommendations = [
                'Check internet connection',
                'Verify translation service availability',
                'Consider using fallback translations only',
                'Check firewall/proxy settings'
            ]

        return {
            'status': status,
            'message': message,
            'success_rate': success_rate,
            'total_requests': total_requests,
            'recommendations': recommendations,
            'provider_errors': self.error_counts
        }

# Global translation service instance
translation_service = TranslationService()
