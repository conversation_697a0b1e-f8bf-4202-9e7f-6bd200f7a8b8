#!/usr/bin/env python3
"""
Hybrid YouTube processor that tries <PERSON>hisper first, falls back to YouTube Transcript API

This processor handles the case where FFmpeg is not available by:
1. First trying FFmpeg-based audio extraction (preferred)
2. Falling back to direct audio download without conversion
3. Using various audio formats that <PERSON>his<PERSON> can handle directly
4. Finally falling back to YouTube Transcript API if <PERSON>hisper fails

Note: For best results, install FFmpeg, but the processor will work without it.
"""

import os
import json
import datetime
import hashlib
import re
import tempfile
import faiss
import numpy as np
from sentence_transformers import SentenceTransformer
from dotenv import load_dotenv

# Import language detection utilities
from language_utils import (
    detect_language_from_text,
    get_index_name_for_language,
    get_language_statistics
)

# Load environment variables
load_dotenv()

# Try importing Faster Whisper and yt-dlp
try:
    from faster_whisper import WhisperModel
    import yt_dlp
    WHISPER_AVAILABLE = True
    print("✅ Faster Whisper and yt-dlp available")
except ImportError:
    WHISPER_AVAILABLE = False
    print("⚠️ Faster Whisper/yt-dlp not available, will use YouTube Transcript API fallback")

# Try importing YouTube Transcript API as fallback
try:
    from youtube_transcript_api import YouTubeTranscriptApi
    TRANSCRIPT_API_AVAILABLE = True
    print("✅ YouTube Transcript API available")
except ImportError:
    TRANSCRIPT_API_AVAILABLE = False
    print("⚠️ YouTube Transcript API not available")

# Configuration
FAISS_DATA_DIR = os.getenv("FAISS_DATA_DIR", "faiss_data")
OUTPUT_DIR = os.path.join(os.path.dirname(os.path.dirname(__file__)), FAISS_DATA_DIR)
YOUTUBE_DIR = os.path.join(OUTPUT_DIR, "youtube")
EMBED_MODEL = "all-MiniLM-L6-v2"
NLIST = 10  # Number of clusters for IVF

# Ensure directories exist
os.makedirs(YOUTUBE_DIR, exist_ok=True)

# Initialize embedding model
embedder = SentenceTransformer(EMBED_MODEL)

def extract_video_id(url):
    """Extract YouTube video ID from various URL formats"""
    patterns = [
        r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)',
        r'youtube\.com\/watch\?.*v=([^&\n?#]+)'
    ]
    
    for pattern in patterns:
        match = re.search(pattern, url)
        if match:
            return match.group(1)
    return None

def get_video_info(video_id):
    """Get basic video information"""
    try:
        return {
            "video_id": video_id,
            "title": f"YouTube Video {video_id}",
            "url": f"https://www.youtube.com/watch?v={video_id}"
        }
    except Exception as e:
        print(f"Error getting video info: {e}")
        return None

# Whisper-based transcription functions
def download_youtube_audio(video_id, output_dir):
    """Download audio from YouTube video using yt-dlp with FFmpeg fallback handling"""
    if not WHISPER_AVAILABLE:
        return None

    try:
        url = f"https://www.youtube.com/watch?v={video_id}"

        # First try with FFmpeg postprocessing (preferred method)
        ydl_opts_ffmpeg = {
            'format': 'bestaudio/best',
            'outtmpl': os.path.join(output_dir, f'{video_id}.%(ext)s'),
            'postprocessors': [{
                'key': 'FFmpegExtractAudio',
                'preferredcodec': 'wav',
                'preferredquality': '192',
            }],
            'quiet': True,
            'no_warnings': True,
        }

        try:
            print("🔧 Trying audio extraction with FFmpeg...")
            with yt_dlp.YoutubeDL(ydl_opts_ffmpeg) as ydl:
                ydl.download([url])

            # Find the downloaded audio file
            audio_file = os.path.join(output_dir, f'{video_id}.wav')
            if os.path.exists(audio_file):
                print("✅ Audio extracted with FFmpeg")
                return audio_file

        except Exception as ffmpeg_error:
            print(f"⚠️ FFmpeg extraction failed: {ffmpeg_error}")
            print("🔄 Trying direct audio download without conversion...")

            # Fallback: Download best audio format directly without conversion
            ydl_opts_direct = {
                'format': 'bestaudio[ext=m4a]/bestaudio[ext=webm]/bestaudio/best',
                'outtmpl': os.path.join(output_dir, f'{video_id}.%(ext)s'),
                'quiet': True,
                'no_warnings': True,
            }

            try:
                with yt_dlp.YoutubeDL(ydl_opts_direct) as ydl:
                    ydl.download([url])

                # Find the downloaded audio file with various extensions
                for ext in ['m4a', 'webm', 'mp4', 'mp3', 'wav']:
                    audio_file = os.path.join(output_dir, f'{video_id}.{ext}')
                    if os.path.exists(audio_file):
                        print(f"✅ Audio downloaded directly as {ext}")
                        return audio_file

            except Exception as direct_error:
                print(f"❌ Direct download also failed: {direct_error}")
                return None

        # Try other possible extensions from first attempt
        for ext in ['mp3', 'm4a', 'webm', 'mp4']:
            alt_file = os.path.join(output_dir, f'{video_id}.{ext}')
            if os.path.exists(alt_file):
                print(f"✅ Found audio file: {ext}")
                return alt_file

        return None

    except Exception as e:
        print(f"Error downloading audio for {video_id}: {e}")
        return None

def transcribe_with_whisper(audio_file, model_size="base"):
    """Transcribe audio file using Faster Whisper with improved error handling"""
    if not WHISPER_AVAILABLE:
        print("❌ Faster Whisper not available for transcription")
        return None, None

    try:
        print(f"🎤 Loading Faster Whisper model ({model_size})...")
        model = WhisperModel(model_size, device="cpu", compute_type="int8")

        print(f"🎵 Transcribing audio: {os.path.basename(audio_file)}")
        print(f"📁 Audio file size: {os.path.getsize(audio_file)} bytes")

        # Check if audio file exists and is not empty
        if not os.path.exists(audio_file):
            print(f"❌ Audio file not found: {audio_file}")
            return None, None

        if os.path.getsize(audio_file) == 0:
            print(f"❌ Audio file is empty: {audio_file}")
            return None, None

        segments, info = model.transcribe(audio_file, beam_size=5)

        # Convert segments to list and extract text
        segments_list = list(segments)
        transcript = " ".join([segment.text for segment in segments_list])

        print(f"✅ Transcription completed successfully")
        print(f"🌐 Detected language: {info.language}")
        print(f"📝 Transcript length: {len(transcript)} characters")

        # Create result format compatible with original code
        result = {
            "text": transcript,
            "language": info.language,
            "segments": [{"text": seg.text, "start": seg.start, "end": seg.end} for seg in segments_list]
        }

        return transcript, result

    except Exception as e:
        print(f"❌ Error transcribing with Faster Whisper: {e}")
        print("💡 Possible solutions:")
        print("   - Check if the audio file format is supported by Whisper")
        print("   - Try with a different audio file")
        print("   - Ensure sufficient disk space and memory")
        return None, None

def get_transcript_whisper(video_id):
    """Get transcript using Whisper"""
    if not WHISPER_AVAILABLE:
        return None, None
        
    try:
        # Create temporary directory for audio download
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📥 Downloading audio for video {video_id}...")
            
            # Download audio
            audio_file = download_youtube_audio(video_id, temp_dir)
            if not audio_file:
                print("❌ Failed to download audio")
                return None, None
            
            print(f"✅ Audio downloaded: {os.path.basename(audio_file)}")
            
            # Transcribe with Whisper
            transcript, whisper_result = transcribe_with_whisper(audio_file)
            if not transcript:
                print("❌ Failed to transcribe audio")
                return None, None
            
            print(f"✅ Whisper transcription completed: {len(transcript)} characters")
            
            # Create segments list similar to YouTube Transcript API format
            segments = []
            if whisper_result and "segments" in whisper_result:
                for segment in whisper_result["segments"]:
                    segments.append({
                        'text': segment.get('text', ''),
                        'start': segment.get('start', 0),
                        'duration': segment.get('end', 0) - segment.get('start', 0)
                    })
            
            return transcript, segments
            
    except Exception as e:
        print(f"Error getting Whisper transcript for {video_id}: {e}")
        return None, None

# YouTube Transcript API fallback
def get_transcript_api(video_id):
    """Get transcript using YouTube Transcript API with language fallback"""
    if not TRANSCRIPT_API_AVAILABLE:
        return None, None

    try:
        # Try to get transcript in multiple languages
        languages_to_try = ['en', 'hi', 'es', 'fr', 'de', 'ja', 'ko', 'zh', 'ar']

        for lang in languages_to_try:
            try:
                print(f"🌐 Trying language: {lang}")
                transcript_list = YouTubeTranscriptApi.get_transcript(video_id, languages=[lang])
                full_transcript = " ".join([item['text'] for item in transcript_list])
                print(f"✅ YouTube API transcription completed ({lang}): {len(full_transcript)} characters")
                return full_transcript, transcript_list
            except Exception as lang_error:
                print(f"⚠️ Language {lang} failed: {str(lang_error)[:100]}...")
                continue

        # If specific languages fail, try getting any available transcript
        print("🔄 Trying any available language...")
        transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
        full_transcript = " ".join([item['text'] for item in transcript_list])
        print(f"✅ YouTube API transcription completed (auto): {len(full_transcript)} characters")
        return full_transcript, transcript_list

    except Exception as e:
        print(f"Error getting API transcript for {video_id}: {str(e)[:200]}...")
        return None, None

def get_youtube_transcript(video_id):
    """Get transcript using Whisper first, fallback to YouTube API"""
    print(f"🎥 Getting transcript for video {video_id}")
    
    # Try Whisper first if available
    if WHISPER_AVAILABLE:
        print("🎤 Trying Whisper transcription...")
        transcript, segments = get_transcript_whisper(video_id)
        if transcript:
            return transcript, segments
        print("⚠️ Whisper failed, trying YouTube API fallback...")
    
    # Fallback to YouTube Transcript API
    if TRANSCRIPT_API_AVAILABLE:
        print("📝 Trying YouTube Transcript API...")
        transcript, segments = get_transcript_api(video_id)
        if transcript:
            return transcript, segments
        print("⚠️ YouTube API also failed")
    
    print("❌ All transcription methods failed")
    return None, None

def chunk_text(text: str, size: int = 500):
    """Split text into chunks"""
    chunks, start = [], 0
    L = len(text)
    while start < L:
        end = min(start + size, L)
        if end < L:
            while end > start and not text[end].isspace():
                end -= 1
            if end == start:
                end = min(start + size, L)
        chunk = text[start:end].strip()
        if chunk:
            chunks.append(chunk)
        start = end
    return chunks

def process_youtube_url(youtube_url, index_name="default", user_selected_language=None):
    """Process a YouTube URL and add to selected FAISS index with multi-language support"""
    print(f"🎥 Processing YouTube URL: {youtube_url}")
    print(f"🎯 Target index: {index_name}")
    if user_selected_language:
        print(f"🌐 User selected language: {user_selected_language}")

    # Extract video ID
    video_id = extract_video_id(youtube_url)
    if not video_id:
        print("❌ Invalid YouTube URL")
        return False

    # Get video info
    video_info = get_video_info(video_id)
    if not video_info:
        print("❌ Could not get video information")
        return False

    # Get transcript
    transcript, segments = get_youtube_transcript(video_id)
    if not transcript:
        print("❌ Could not get transcript")
        return False

    print(f"✅ Got transcript with {len(transcript)} characters")

    # Detect language from transcript content
    detected_language = detect_language_from_text(transcript)
    print(f"🔍 Detected language: {detected_language}")

    # Get language statistics for detailed analysis
    lang_stats = get_language_statistics(transcript)
    if lang_stats:
        print("📊 Language distribution:")
        for lang, ratio in sorted(lang_stats.items(), key=lambda x: x[1], reverse=True):
            print(f"   {lang}: {ratio:.3f} ({ratio*100:.1f}%)")

    # Determine final index name based on language detection and user preference
    if index_name == "default" or index_name == "auto":
        # Auto-detect index based on content language
        final_index_name = get_index_name_for_language(detected_language, user_selected_language)
    else:
        # Use user-specified index
        final_index_name = index_name
        print(f"🎯 Using user-specified index: {final_index_name}")

    print(f"📁 Final target index: {final_index_name}")

    # Load or create the target FAISS index
    try:
        # Import FAISS index management functions
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(__file__)))
        from full_code import load_faiss_index, create_faiss_index

        # Try to load existing index using final index name
        faiss_index, existing_metadata, success = load_faiss_index(final_index_name)

        if not success or faiss_index is None:
            print(f"📁 Creating new FAISS index: {final_index_name}")
            # Create new index if it doesn't exist
            dummy_embedding = embedder.encode(["hello world"])
            dim = len(dummy_embedding[0])
            result = create_faiss_index(final_index_name, dimension=dim, embed_model=EMBED_MODEL)
            if not result.get("success"):
                print(f"❌ Failed to create index: {result.get('error')}")
                return False

            # Load the newly created index
            faiss_index, existing_metadata, success = load_faiss_index(final_index_name)
            if not success:
                print(f"❌ Failed to load newly created index")
                return False

        print(f"✅ Loaded FAISS index: {final_index_name} with {len(existing_metadata)} existing entries")

    except Exception as e:
        print(f"❌ Error loading FAISS index: {e}")
        return False

    # Process transcript chunks
    chunks = chunk_text(transcript)
    record_date = datetime.datetime.now().isoformat()
    new_vectors = []
    new_metadata = []

    for chunk_idx, chunk in enumerate(chunks):
        # Create embedding for this chunk
        vec_list = embedder.encode([chunk])
        vec = np.array(vec_list[0], dtype="float32")
        new_vectors.append(vec)

        new_metadata.append({
            "chunk_text": chunk,
            "record_date": record_date,
            "category": "youtube",
            "url": youtube_url,
            "video_id": video_id,
            "title": video_info.get("title", "Unknown"),
            "vector_id": f"youtube-{video_id}-chunk-{chunk_idx}",
            "source_type": "youtube",
            "transcription_method": "whisper" if WHISPER_AVAILABLE else "youtube_api",
            "upload_source": "youtube_upload",
            "detected_language": detected_language,
            "index_name": final_index_name,
            "language_stats": lang_stats,
            "user_selected_language": user_selected_language
        })

        print(f"📝 Embedded chunk {chunk_idx + 1}/{len(chunks)}: {chunk[:60]}...")

    if not new_vectors:
        print("❌ No content to index")
        return False

    # Stack & normalize for cosine similarity
    xb = np.vstack(new_vectors)
    faiss.normalize_L2(xb)

    # Add vectors to the existing index
    faiss_index.add(xb)

    # Combine existing and new metadata
    combined_metadata = existing_metadata + new_metadata

    # Save updated index and metadata
    index_dir = os.path.join(OUTPUT_DIR, final_index_name)
    os.makedirs(index_dir, exist_ok=True)

    faiss_file_path = os.path.join(index_dir, f"{final_index_name}.faiss")
    metadata_file_path = os.path.join(index_dir, f"{final_index_name}.json")

    # Save FAISS index
    faiss.write_index(faiss_index, faiss_file_path)
    print(f"🧠 Updated FAISS index saved to {faiss_file_path}")

    # Save metadata
    with open(metadata_file_path, "w", encoding="utf-8") as f:
        json.dump(combined_metadata, f, ensure_ascii=False, indent=2)
    print(f"🗃️ Updated metadata saved to {metadata_file_path}")

    print(f"✅ Finished: Added {len(new_vectors)} chunks to index '{final_index_name}' (total: {faiss_index.ntotal})")
    print(f"🌐 Content language: {detected_language}")
    print(f"📁 Stored in index: {final_index_name}")
    return True

def main():
    print("🎤 Hybrid YouTube Transcription Processor")
    print("=" * 50)
    print(f"🎤 Whisper available: {'✅' if WHISPER_AVAILABLE else '❌'}")
    print(f"📝 YouTube API available: {'✅' if TRANSCRIPT_API_AVAILABLE else '❌'}")

    if WHISPER_AVAILABLE:
        print("💡 FFmpeg Status:")
        print("   - If FFmpeg is installed: Best audio quality extraction")
        print("   - If FFmpeg is missing: Will use direct audio download")
        print("   - Both methods work with Whisper transcription")

    if not WHISPER_AVAILABLE and not TRANSCRIPT_API_AVAILABLE:
        print("❌ No transcription methods available!")
        print("Install either:")
        print("  pip install faster-whisper yt-dlp")
        print("  OR")
        print("  pip install youtube-transcript-api")
        return

    # Test with a YouTube URL
    test_url = input("\nEnter YouTube URL: ").strip()
    if test_url:
        success = process_youtube_url(test_url)
        if success:
            print("🎉 YouTube video processed successfully!")
        else:
            print("❌ Failed to process YouTube video")
            if WHISPER_AVAILABLE:
                print("💡 If Whisper failed, the system will automatically try YouTube Transcript API")
            print("💡 Check the error messages above for specific issues")

if __name__ == "__main__":
    main()
