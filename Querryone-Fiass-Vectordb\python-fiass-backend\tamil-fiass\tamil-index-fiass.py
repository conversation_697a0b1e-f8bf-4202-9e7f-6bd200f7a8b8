import os
import json
import datetime
import faiss
import numpy as np
import pandas as pd
import gc
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

UPLOAD_FOLDER = 'uploads'
OUTPUT_DIR = 'faiss_indexes'
EMBED_MODEL = "all-MiniLM-L6-v2"
NLIST = 100
BATCH_SIZE = 100
CHUNK_SIZE = 1000

os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER

# Initialize embedder
embedder = HuggingFaceEmbeddings(model_name=EMBED_MODEL)
dummy_embedding = embedder.embed_documents(["hello world"])
dim = len(dummy_embedding[0])
quantizer = faiss.IndexFlatIP(dim)
index = faiss.IndexIVFFlat(quantizer, dim, NLIST, faiss.METRIC_INNER_PRODUCT)
index_trained = False
train_vectors = []
metadata_store = []

def normalize_and_train_index_tamil(vectors):
    global index_trained
    if not index.is_trained and not index_trained:
        print("🎯 Training FAISS IVF index...")
        train_sample = np.vstack(vectors)[:min(10000, len(vectors))].astype('float32')
        faiss.normalize_L2(train_sample)
        index.train(train_sample)
        index_trained = True

def process_batch_tamil(batch_vectors, batch_metadata):
    if not batch_vectors:
        return
    xb = np.vstack(batch_vectors).astype('float32')
    faiss.normalize_L2(xb)
    normalize_and_train_index_tamil(train_vectors)
    index.add(xb)
    metadata_store.extend(batch_metadata)

@app.route("/upload", methods=["POST"])
def upload_file():
    global metadata_store, index_trained, train_vectors, index

    # Reset FAISS state on each upload
    index = faiss.IndexIVFFlat(faiss.IndexFlatIP(dim), dim, NLIST, faiss.METRIC_INNER_PRODUCT)
    index_trained = False
    train_vectors = []
    metadata_store = []

    client_email = request.form.get("client_email")
    if not client_email:
        return jsonify({"error": "client_email is required"}), 400

    file = request.files.get("file")
    if not file or not file.filename.endswith(".xlsx"):
        return jsonify({"error": "Excel .xlsx file is required"}), 400

    filename = secure_filename(file.filename)
    file_path = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(file_path)

    try:
        df = pd.read_excel(file_path, engine="openpyxl")
    except Exception as e:
        return jsonify({"error": str(e)}), 500

    total_rows = len(df)
    for start_idx in range(0, total_rows, CHUNK_SIZE):
        end_idx = min(start_idx + CHUNK_SIZE, total_rows)
        chunk = df.iloc[start_idx:end_idx]
        batch_vectors = []
        batch_metadata = []

        for row_idx, row in chunk.iterrows():
            text = ""
            for col in ["news_article", "news_title", "Content", "content", "Text", "text", "Article", "article", "News", "news"]:
                if col in chunk.columns:
                    val = str(row.get(col, "")).strip()
                    if val and val.lower() != "nan":
                        text = val
                        break

            if "news_title" in chunk.columns and "news_article" in chunk.columns:
                title = str(row.get("news_title", "")).strip()
                article = str(row.get("news_article", "")).strip()
                if title.lower() != "nan" and article.lower() != "nan":
                    text = f"{title}. {article}"
                elif title.lower() != "nan":
                    text = title
                elif article.lower() != "nan":
                    text = article

            if not text or text.lower() == "nan":
                continue

            category = "unknown"
            for col in ["news_category", "Sentiment", "sentiment", "Category", "category", "Label", "label"]:
                if col in chunk.columns:
                    val = str(row.get(col, "")).strip()
                    if val and val.lower() != "nan":
                        category = val
                        break

            record_date = datetime.datetime.now().isoformat()
            vec_list = embedder.embed_documents([text])[0]
            vec = np.array(vec_list, dtype="float32")
            batch_vectors.append(vec)
            train_vectors.append(vec)

            url = "N/A"
            for col in ["URL", "url", "Link", "link", "Source", "source"]:
                if col in chunk.columns:
                    url_val = str(row.get(col, "")).strip()
                    if url_val and url_val.lower() != "nan":
                        url = url_val
                        break

            summary = "N/A"
            for col in ["Summary", "summary", "Abstract", "abstract", "Description", "description"]:
                if col in chunk.columns:
                    sum_val = str(row.get(col, "")).strip()
                    if sum_val and sum_val.lower() != "nan":
                        summary = sum_val
                        break

            batch_metadata.append({
                "full_text": text,
                "record_date": record_date,
                "category": category,
                "url": url,
                "summary": summary,
                "vector_id": f"news-{start_idx + row_idx}",
                "client_email": client_email,
                "file_uploaded": filename
            })

            if len(batch_vectors) >= BATCH_SIZE:
                process_batch_tamil(batch_vectors, batch_metadata)
                batch_vectors.clear()
                batch_metadata.clear()
                gc.collect()

        process_batch_tamil(batch_vectors, batch_metadata)
        gc.collect()

    if index.ntotal == 0:
        return jsonify({"error": "No vectors were added. Check your content columns."}), 400

    index_path = os.path.join(OUTPUT_DIR, f"{filename}_index.faiss")
    metadata_path = os.path.join(OUTPUT_DIR, f"{filename}_metadata.json")

    faiss.write_index(index, index_path)
    with open(metadata_path, "w", encoding="utf-8") as f:
        json.dump(metadata_store, f, ensure_ascii=False, indent=2)

    return jsonify({
        "message": "Index and metadata created successfully",
        "index_file": index_path,
        "metadata_file": metadata_path,
        "total_vectors": index.ntotal
    })

if __name__ == "__main__":
    app.run(debug=True)
