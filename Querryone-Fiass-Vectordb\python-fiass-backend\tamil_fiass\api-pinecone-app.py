from flask import Flask, request, jsonify
from flask_cors import CORS
import faiss
import json
import numpy as np
from langchain_huggingface.embeddings import HuggingFaceEmbeddings
from openai import OpenAI
from dotenv import load_dotenv
import os
import re
import datetime

# Load environment variables
load_dotenv()

# Configuration
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY")
INDEX_PATH = r"python-fiass\news_index.faiss"
METADATA_PATH = r"python-fiass\news_metadata.json"

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Load FAISS index and metadata
try:
    faiss_index = faiss.read_index(INDEX_PATH)
    with open(METADATA_PATH, "r", encoding="utf-8") as f:
        metadata_store = json.load(f)
    print(f"✅ Loaded FAISS index with {faiss_index.ntotal} vectors")
except Exception as e:
    print(f"❌ Error loading FAISS index: {e}")
    faiss_index = None
    metadata_store = []

# Use multilingual embedding model (supports Tamil)
embedder = HuggingFaceEmbeddings(model_name="sentence-transformers/all-MiniLM-L12-v2")

deepseek_client = OpenAI(api_key=DEEPSEEK_API_KEY, base_url="https://api.deepseek.com")

def retrieve_from_faiss(query, k=5):
    if faiss_index is None or not metadata_store:
        return []

    query_vector = embedder.embed_documents([query])[0]
    query_embedding = np.array([query_vector]).astype("float32")
    faiss.normalize_L2(query_embedding)

    distances, indices = faiss_index.search(query_embedding, k)
    results = []

    for rank, idx in enumerate(indices[0]):
        if 0 <= idx < len(metadata_store):
            meta = metadata_store[idx]
            match = type('Match', (), {
                'score': float(distances[0][rank]),
                'metadata': {
                    'chunk_text': meta.get("chunk_text", ""),
                    'record_date': meta.get("record_date", "Unknown"),
                    'category': meta.get("category", "N/A"),
                    'url': meta.get("url", "N/A"),
                    'summary': meta.get("summary", "N/A")
                }
            })()
            results.append(match)
    return results

def generate_response(query, context_docs):
    context = "\n\n".join([doc.metadata.get('chunk_text', '') for doc in context_docs])
    messages = [
        {"role": "system", "content": "You are a helpful assistant that answers only in Tamil."},
        {"role": "user", "content": f"""கீழ்காணும் தகவல்களின் அடிப்படையில் இந்தக் கேள்விக்கு தமிழில் பதிலளிக்கவும்:

தகவல்:
{context}

கேள்வி: {query}
"""}
    ]
    response = deepseek_client.chat.completions.create(
        model="deepseek-chat",
        messages=messages,
        stream=False
    )
    return response.choices[0].message.content

def generate_related_questions(query, answer):
    prompt = f"""
கீழே உள்ள கேள்வி மற்றும் பதிலின் அடிப்படையில் தமிழில் 5 தொடர்புடைய தொடர்ந்த கேள்விகளை உருவாக்கவும்.

கேள்வி: {query}
பதில்: {answer}

தொடர்ந்த கேள்விகள்:
1.
"""
    messages = [{"role": "user", "content": prompt}]
    try:
        response = deepseek_client.chat.completions.create(
            model="deepseek-chat",
            messages=messages
        )
        raw_text = response.choices[0].message.content
        lines = raw_text.strip().split('\n')
        questions = [
            re.sub(r"^\d+\.\s*", "", line).strip()
            for line in lines if re.search(r'\?', line)
        ]
        return questions[:5]
    except Exception as e:
        return [f"(தொடர்ந்த கேள்விகளை உருவாக்க முடியவில்லை: {str(e)})"]

def extract_sentences(text):
    return re.split(r'(?<=[.!?])\s+', text.strip())

def enrich_ai_response_with_urls(ai_response):
    sentences = extract_sentences(ai_response)
    enriched = []

    for sentence in sentences:
        if not sentence.strip():
            continue
        matches = retrieve_from_faiss(sentence, k=1)
        if matches:
            top_match = matches[0].metadata
            enriched.append({
                "sentence": sentence,
                "url": top_match.get("url", "N/A"),
                "summary": top_match.get("summary", "N/A")
            })
        else:
            enriched.append({
                "sentence": sentence,
                "url": "Not found",
                "summary": "No summary found"
            })
    return enriched

@app.route('/financial_query', methods=['POST'])
def handle_query():
    data = request.get_json()
    query = str(data.get("query", "")).strip()

    if not query:
        return jsonify({"error": "Query is required."}), 400

    matches = retrieve_from_faiss(query)
    retrieved_docs = []
    for i, match in enumerate(matches):
        metadata = match.metadata
        score = round(match.score * 100, 2)
        retrieved_docs.append({
            "rank": i + 1,
            "score": f"{score}%",
            "date": metadata.get('record_date', 'Unknown'),
            "category": metadata.get('category', 'N/A'),
            "text": metadata.get('chunk_text', 'No text')
        })

    ai_response = generate_response(query, matches)
    enriched_sentences = enrich_ai_response_with_urls(ai_response)
    related_questions = generate_related_questions(query, ai_response)

    return jsonify({
        "query": query,
        "retrieved_documents": retrieved_docs,
        "ai_response": ai_response,
        "sentence_analysis": enriched_sentences,
        "related_questions": related_questions
    })

if __name__ == '__main__':
    app.run(debug=True)
