#!/usr/bin/env python3
"""
Test script to verify direct Tamil processing without translation
"""

import requests
import json
import os

def test_direct_tamil_processing():
    """Test Tamil query with Tamil index for direct processing"""

    url = f"{os.getenv('BACKEND_URL', 'http://localhost:5010')}/financial_query"

    # Test data - Tamil query with Tamil index (should trigger direct processing)
    test_data = {
        "query": "பழைய செயலக இராணுவ தோட்டத்தில் தீ விபத்து",
        "index_name": "tamil",
        "client_email": "<EMAIL>"
    }
    
    print("🧪 Testing Direct Tamil Processing")
    print(f"📝 Query: {test_data['query']}")
    print(f"📊 Index: {test_data['index_name']}")
    print(f"👤 Client: {test_data['client_email']}")
    print("-" * 50)
    
    try:
        response = requests.post(url, json=test_data, headers={'Content-Type': 'application/json'})
        
        if response.status_code == 200:
            result = response.json()
            
            print("✅ Request successful!")
            print(f"🔍 Original Query: {result.get('query', 'N/A')}")
            print(f"🌐 Translated Query: {result.get('translated_query', 'N/A')}")
            print(f"📈 Query Translated: {result.get('query_translated', 'N/A')}")
            print(f"🎯 Direct Tamil Processing: {result.get('direct_tamil_processing', 'N/A')}")
            print(f"📚 Index Used: {result.get('index_used', 'N/A')}")
            print(f"🌍 Data Language: {result.get('data_language', 'N/A')}")
            
            # Check if translation was applied
            translation_applied = result.get('translation_applied', False)
            print(f"🔄 Translation Applied: {translation_applied}")
            
            # Check cross-language processing
            cross_lang = result.get('cross_language_processing', {})
            print(f"🌏 Cross-Language Processing Applied: {cross_lang.get('applied', 'N/A')}")
            print(f"🌏 Cross-Language Reason: {cross_lang.get('reason', 'N/A')}")
            
            # Show AI response snippet
            ai_response = result.get('ai_response', '')
            print(f"🤖 AI Response (first 100 chars): {ai_response[:100]}...")
            
            # Check if response is in Tamil
            import re
            tamil_pattern = re.compile(r'[\u0B80-\u0BFF]')
            has_tamil = bool(tamil_pattern.search(ai_response))
            print(f"🔤 Response contains Tamil: {has_tamil}")
            
            return True
            
        else:
            print(f"❌ Request failed with status {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        return False

if __name__ == "__main__":
    test_direct_tamil_processing()
