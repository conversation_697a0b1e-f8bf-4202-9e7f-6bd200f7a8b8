#!/usr/bin/env python3
"""
Quick test script to verify Tamil routing functionality.
"""

import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_language_detection():
    """Test language detection with various queries."""
    print("🧪 Testing Language Detection")
    print("=" * 50)
    
    try:
        from services.language_utils import enhanced_language_detector
        
        test_queries = [
            ("மின்சாரம் வழங்க வேண்டும்", "Tamil"),
            ("Import of wheat with poisoning", "English"),
            ("விவசாய கடன் தேவை", "Tamil"),
            ("విద్యుత్ సరఫరా అవసరం", "Telugu"),
            ("ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ", "Kannada"),
        ]
        
        for query, expected in test_queries:
            language, confidence, scores = enhanced_language_detector.detect_language_with_confidence(query)
            status = "✅" if language == expected else "❌"
            print(f"{status} '{query[:30]}...' -> {language} (confidence: {confidence:.3f}, expected: {expected})")
            
            if scores:
                top_scores = sorted(scores.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"    Top scores: {top_scores}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Enhanced language detector not available: {e}")
        return False

def test_tamil_routing():
    """Test Tamil routing logic."""
    print("\n🌏 Testing Tamil Routing Logic")
    print("=" * 50)
    
    try:
        from services.tamil_query_router import tamil_router
        
        test_queries = [
            ("மின்சாரம் வழங்க வேண்டும்", True),
            ("Import of wheat with poisoning", False),
            ("விவசாய கடன் தேவை", True),
            ("I need electricity supply", False),
        ]
        
        for query, expected in test_queries:
            should_route = tamil_router.should_route_to_tamil_api(query)
            status = "✅" if should_route == expected else "❌"
            print(f"{status} '{query[:30]}...' -> Route: {should_route} (expected: {expected})")
        
        return True
        
    except ImportError as e:
        print(f"❌ Tamil router not available: {e}")
        return False

def test_tamil_api_health():
    """Test Tamil API health."""
    print("\n🏥 Testing Tamil API Health")
    print("=" * 50)
    
    try:
        from services.tamil_query_router import tamil_router
        
        health = tamil_router.check_tamil_api_health()
        if health.get('healthy'):
            print(f"✅ Tamil API is healthy (status: {health.get('status_code')})")
        else:
            print(f"❌ Tamil API is not healthy: {health.get('error')}")
        
        return health.get('healthy', False)
        
    except ImportError as e:
        print(f"❌ Tamil router not available: {e}")
        return False

def test_cross_language_logic():
    """Test cross-language processing logic."""
    print("\n🌐 Testing Cross-Language Logic")
    print("=" * 50)
    
    try:
        from services.cross_language_processor import cross_language_processor
        
        test_cases = [
            ("Tamil", "English", None, True),   # Should translate
            ("English", "Tamil", None, True),   # Should translate  
            ("Tamil", "Tamil", None, False),    # Should not translate
            ("English", "English", None, False), # Should not translate
            ("Tamil", "English", "Tamil", True), # User wants Tamil response
        ]
        
        for query_lang, data_lang, user_lang, expected in test_cases:
            should_translate = cross_language_processor.should_use_translation_flow(
                query_lang, data_lang, user_lang
            )
            status = "✅" if should_translate == expected else "❌"
            print(f"{status} Query:{query_lang} Data:{data_lang} User:{user_lang} -> Translate:{should_translate} (expected:{expected})")
        
        return True
        
    except ImportError as e:
        print(f"❌ Cross-language processor not available: {e}")
        return False

def main():
    """Main test function."""
    print("🧪 TAMIL ROUTING SYSTEM TEST")
    print("=" * 70)
    
    results = []
    
    # Test language detection
    results.append(("Language Detection", test_language_detection()))
    
    # Test Tamil routing
    results.append(("Tamil Routing", test_tamil_routing()))
    
    # Test Tamil API health
    results.append(("Tamil API Health", test_tamil_api_health()))
    
    # Test cross-language logic
    results.append(("Cross-Language Logic", test_cross_language_logic()))
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Tamil routing system is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the implementation.")
    
    # Specific guidance based on the logs
    print("\n💡 TROUBLESHOOTING NOTES:")
    print("- If you're testing with 'Import of wheat with poisoning', this is English text")
    print("- Tamil routing only works with actual Tamil text like 'மின்சாரம் வழங்க வேண்டும்'")
    print("- Make sure the Tamil FAISS API service is running on localhost:5000")
    print("- Check that the DeepSeek API key is set in environment variables")

if __name__ == '__main__':
    main()
