"""
Test suite for language detection and routing functionality.

This module tests the enhanced language detection, Tamil query routing,
and cross-language translation flows.
"""

import unittest
import sys
import os
import json
from unittest.mock import Mock, patch, MagicMock

# Add the parent directory to the path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestLanguageDetection(unittest.TestCase):
    """Test cases for enhanced language detection service."""
    
    def setUp(self):
        """Set up test fixtures."""
        try:
            from services.language_utils import enhanced_language_detector
            self.detector = enhanced_language_detector
        except ImportError:
            self.skipTest("Enhanced language detection service not available")
    
    def test_tamil_text_detection(self):
        """Test detection of Tamil text."""
        tamil_text = "மின்சாரம் வழங்க வேண்டியதன் அவசியத்தை புரிந்துகொள்கிறேன்"
        language, confidence, scores = self.detector.detect_language_with_confidence(tamil_text)
        
        self.assertEqual(language, 'Tamil')
        self.assertGreater(confidence, 0.1)
        self.assertIn('Tamil', scores)
    
    def test_telugu_text_detection(self):
        """Test detection of Telugu text."""
        telugu_text = "విద్యుత్ సరఫరా అవసరాలను అర్థం చేసుకుంటున్నాను"
        language, confidence, scores = self.detector.detect_language_with_confidence(telugu_text)
        
        self.assertEqual(language, 'Telugu')
        self.assertGreater(confidence, 0.1)
        self.assertIn('Telugu', scores)
    
    def test_kannada_text_detection(self):
        """Test detection of Kannada text."""
        kannada_text = "ವಿದ್ಯುತ್ ಪೂರೈಕೆಯ ಅವಶ್ಯಕತೆಯನ್ನು ಅರ್ಥಮಾಡಿಕೊಳ್ಳುತ್ತೇನೆ"
        language, confidence, scores = self.detector.detect_language_with_confidence(kannada_text)
        
        self.assertEqual(language, 'Kannada')
        self.assertGreater(confidence, 0.1)
        self.assertIn('Kannada', scores)
    
    def test_english_text_detection(self):
        """Test detection of English text."""
        english_text = "I understand the need for electricity supply"
        language, confidence, scores = self.detector.detect_language_with_confidence(english_text)
        
        self.assertEqual(language, 'English')
    
    def test_mixed_language_text(self):
        """Test detection with mixed language text."""
        mixed_text = "Hello மின்சாரம் world"
        language, confidence, scores = self.detector.detect_language_with_confidence(mixed_text)
        
        # Should detect Tamil due to priority weighting
        self.assertIn(language, ['Tamil', 'English'])
    
    def test_empty_text(self):
        """Test detection with empty text."""
        language, confidence, scores = self.detector.detect_language_with_confidence("")
        
        self.assertEqual(language, 'English')
        self.assertEqual(confidence, 0.0)


class TestTamilQueryRouting(unittest.TestCase):
    """Test cases for Tamil query routing service."""
    
    def setUp(self):
        """Set up test fixtures."""
        try:
            from services.tamil_query_router import tamil_router
            self.router = tamil_router
        except ImportError:
            self.skipTest("Tamil query router service not available")
    
    def test_should_route_tamil_query(self):
        """Test routing decision for Tamil queries."""
        tamil_query = "மின்சாரம் வழங்க வேண்டும்"
        should_route = self.router.should_route_to_tamil_api(tamil_query)
        
        # Should route Tamil queries to Tamil API
        self.assertTrue(should_route)
    
    def test_should_not_route_english_query(self):
        """Test routing decision for English queries."""
        english_query = "I need electricity supply"
        should_route = self.router.should_route_to_tamil_api(english_query)
        
        # Should not route English queries to Tamil API
        self.assertFalse(should_route)
    
    @patch('requests.post')
    def test_route_tamil_query_success(self, mock_post):
        """Test successful Tamil query routing."""
        # Mock successful API response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'query': 'மின்சாரம் வழங்க வேண்டும்',
            'ai_response': 'மின்சாரம் வழங்குவதற்கான வழிகள்',
            'retrieved_documents': []
        }
        mock_post.return_value = mock_response
        
        tamil_query = "மின்சாரம் வழங்க வேண்டும்"
        result = self.router.route_tamil_query(tamil_query)
        
        self.assertNotIn('error', result)
        self.assertIn('routing_info', result)
        self.assertTrue(result['routing_info']['routed_to_tamil_api'])
    
    @patch('requests.post')
    def test_route_tamil_query_failure(self, mock_post):
        """Test failed Tamil query routing."""
        # Mock failed API response
        mock_response = Mock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_post.return_value = mock_response
        
        tamil_query = "மின்சாரம் வழங்க வேண்டும்"
        result = self.router.route_tamil_query(tamil_query)
        
        self.assertIn('error', result)
        self.assertIn('routing_info', result)
        self.assertTrue(result['routing_info']['routed_to_tamil_api'])


class TestCrossLanguageProcessor(unittest.TestCase):
    """Test cases for cross-language processing service."""
    
    def setUp(self):
        """Set up test fixtures."""
        try:
            from services.cross_language_processor import cross_language_processor
            self.processor = cross_language_processor
        except ImportError:
            self.skipTest("Cross-language processor service not available")
    
    def test_should_use_translation_flow_different_languages(self):
        """Test translation flow decision for different languages."""
        should_translate = self.processor.should_use_translation_flow(
            query_language='Tamil',
            data_language='English',
            user_requested_language=None
        )
        
        self.assertTrue(should_translate)
    
    def test_should_not_use_translation_flow_same_languages(self):
        """Test translation flow decision for same languages."""
        should_translate = self.processor.should_use_translation_flow(
            query_language='Tamil',
            data_language='Tamil',
            user_requested_language=None
        )
        
        self.assertFalse(should_translate)
    
    def test_detect_data_language_from_metadata(self):
        """Test data language detection from metadata."""
        metadata_list = [
            {'chunk_text': 'மின்சாரம் வழங்க வேண்டும்'},
            {'chunk_text': 'மின்சார சப்ளை அவசியம்'},
            {'chunk_text': 'மின்சாரம் தேவை'}
        ]
        
        detected_language = self.processor.detect_data_language_from_metadata(metadata_list)
        
        self.assertEqual(detected_language, 'Tamil')
    
    def test_get_translation_service_status(self):
        """Test translation service status check."""
        status = self.processor.get_translation_service_status()
        
        self.assertIn('translation_service_available', status)
        self.assertIn('supported_languages', status)


class TestLanguageRoutingIntegration(unittest.TestCase):
    """Integration tests for the complete language routing system."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.test_queries = {
            'tamil': "மின்சாரம் வழங்க வேண்டும்",
            'telugu': "విద్యుత్ సరఫరా అవసరం",
            'kannada': "ವಿದ್ಯುತ್ ಪೂರೈಕೆ ಅವಶ್ಯಕ",
            'english': "I need electricity supply"
        }
    
    def test_end_to_end_tamil_routing(self):
        """Test end-to-end Tamil query routing."""
        try:
            from services.language_utils import enhanced_language_detector
            from services.tamil_query_router import tamil_router
            
            tamil_query = self.test_queries['tamil']
            
            # Step 1: Language detection
            language, confidence, _ = enhanced_language_detector.detect_language_with_confidence(tamil_query)
            self.assertEqual(language, 'Tamil')
            
            # Step 2: Routing decision
            should_route = tamil_router.should_route_to_tamil_api(tamil_query)
            self.assertTrue(should_route)
            
        except ImportError:
            self.skipTest("Required services not available")
    
    def test_cross_language_flow_decision(self):
        """Test cross-language processing flow decision."""
        try:
            from services.cross_language_processor import cross_language_processor
            
            # Test different language combinations
            test_cases = [
                ('Tamil', 'English', True),  # Should translate
                ('English', 'Tamil', True),  # Should translate
                ('Tamil', 'Tamil', False),   # Should not translate
                ('English', 'English', False)  # Should not translate
            ]
            
            for query_lang, data_lang, expected in test_cases:
                with self.subTest(query_lang=query_lang, data_lang=data_lang):
                    should_translate = cross_language_processor.should_use_translation_flow(
                        query_lang, data_lang
                    )
                    self.assertEqual(should_translate, expected)
                    
        except ImportError:
            self.skipTest("Cross-language processor not available")


if __name__ == '__main__':
    # Create test suite
    loader = unittest.TestLoader()
    test_suite = unittest.TestSuite()

    # Add test cases
    test_suite.addTest(loader.loadTestsFromTestCase(TestLanguageDetection))
    test_suite.addTest(loader.loadTestsFromTestCase(TestTamilQueryRouting))
    test_suite.addTest(loader.loadTestsFromTestCase(TestCrossLanguageProcessor))
    test_suite.addTest(loader.loadTestsFromTestCase(TestLanguageRoutingIntegration))

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Test Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*50}")
