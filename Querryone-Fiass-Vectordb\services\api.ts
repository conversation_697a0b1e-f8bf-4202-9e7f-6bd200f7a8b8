import axios from 'axios';

// Create an axios instance with default config
// Configure base URL based on environment
const baseURL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010';

const api = axios.create({
  baseURL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API functions
export const fetchNews = async (category: string = 'all') => {
  try {
    const response = await api.get('/api/news', {
      params: { category }
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching news:', error);
    throw error;
  }
};

// FAISS-specific API functions
export const listEmbeddingModels = async () => {
  try {
    const response = await api.get('/api/list-embedding-models');
    return response.data;
  } catch (error) {
    console.error('Error fetching embedding models:', error);
    throw error;
  }
};

export const listFaissCategories = async () => {
  try {
    const response = await api.post('/api/list-categories');
    return response.data;
  } catch (error) {
    console.error('Error fetching FAISS categories:', error);
    throw error;
  }
};

export const checkFaissIndex = async (indexName: string, embedModel?: string) => {
  try {
    const response = await api.post('/api/check-index', {
      index_name: indexName,
      embed_model: embedModel
    });
    return response.data;
  } catch (error) {
    console.error('Error checking FAISS index:', error);
    throw error;
  }
};

/**
 * Get current user's email from session storage
 */
const getCurrentUserEmail = (): string | null => {
  try {
    if (typeof window === 'undefined') return null;

    // Try multiple sources for user email
    const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');
    if (directEmail) return directEmail;

    // Try from user session data
    const userSession = sessionStorage.getItem('resultUser');
    if (userSession) {
      const userData = JSON.parse(userSession);
      return userData.email || userData.username || null;
    }

    return null;
  } catch (error) {
    console.error('Error getting current user email:', error);
    return null;
  }
};

export const queryFaissIndex = async (query: string, indexName: string, k: number = 5, userEmail?: string) => {
  try {
    // Get user email if not provided
    const emailToUse = userEmail || getCurrentUserEmail();

    const requestBody: any = {
      query,
      index_name: indexName,
      k
    };

    // Add user email for access validation if available
    if (emailToUse) {
      requestBody.user_email = emailToUse;
    }

    const response = await api.post('/api/query-faiss', requestBody);
    return response.data;
  } catch (error) {
    console.error('Error querying FAISS index:', error);
    throw error;
  }
};

export const deleteFaissIndex = async (indexName: string) => {
  try {
    const response = await api.post('/api/delete-faiss-index', {
      index_name: indexName
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting FAISS index:', error);
    throw error;
  }
};

// Data Management API functions
export const getIndexData = async (indexName: string, limit: number = 1000, offset: number = 0) => {
  try {
    const response = await api.post('/api/get-index-data', {
      index_name: indexName,
      limit,
      offset
    });
    return response.data;
  } catch (error) {
    console.error('Error fetching index data:', error);
    throw error;
  }
};

export const deleteIndexRows = async (indexName: string, rowIds: string[]) => {
  try {
    const response = await api.delete('/api/delete-index-rows', {
      data: {
        index_name: indexName,
        row_ids: rowIds
      }
    });
    return response.data;
  } catch (error) {
    console.error('Error deleting index rows:', error);
    throw error;
  }
};

export default api;
