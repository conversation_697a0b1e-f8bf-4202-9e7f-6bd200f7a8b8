/**
 * Service for handling file downloads
 */

/**
 * Download a sample CSV file
 * @param {string} fileName - Name of the sample file to download
 * @returns {Promise<boolean>} - Promise that resolves to true if download was successful
 */
export const downloadSampleFile = async (fileName: string = 'querry.csv'): Promise<boolean> => {
  try {
    // First check if the file exists by making a HEAD request
    try {
      // Use the public directory path
      const checkResponse = await fetch(`/${fileName}`, { method: 'HEAD' });
      if (!checkResponse.ok) {
        console.error(`Sample file ${fileName} not found. Status: ${checkResponse.status}`);
        return false;
      }
    } catch (checkError) {
      console.error('Error checking if sample file exists:', checkError);
      return false;
    }

    // Create a URL to the file in the public directory
    const fileUrl = `/${fileName}`;

    // Create a link element
    const link = document.createElement('a');
    link.href = fileUrl;
    link.download = fileName;

    // Append to the document
    document.body.appendChild(link);

    // Trigger the download
    link.click();

    // Clean up
    document.body.removeChild(link);

    return true;
  } catch (error) {
    console.error('Error downloading sample file:', error);
    return false;
  }
};
