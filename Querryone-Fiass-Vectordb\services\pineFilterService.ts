/**
 * PINE Collection Email-Based Filtering Service
 * 
 * This service provides frontend functionality for email-based filtering of PINE collection data
 * to ensure users only see their own data based on email authentication.
 */

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_BACKEND_URL || process.env.BACKEND_URL || 'http://localhost:5010';

// Types
export interface PineRecord {
  id: number;
  index_name: string;
  email: string;
  upload_date: string;
  category: string;
}

export interface UserIndicesResponse {
  success: boolean;
  message: string;
  indices: string[];
  count: number;
  user_email: string;
  error?: string;
}

export interface UserAccessResponse {
  success: boolean;
  has_access: boolean;
  message: string;
  user_email: string;
  index_name: string;
  error?: string;
}

export interface UserRecordsResponse {
  success: boolean;
  message: string;
  records: PineRecord[];
  count: number;
  user_email: string;
  error?: string;
}

/**
 * Service class for PINE collection email-based filtering
 */
export class PineFilterService {
  
  /**
   * Get the current user's email from session storage
   */
  private static getCurrentUserEmail(): string | null {
    try {
      if (typeof window === 'undefined') return null;
      
      // Try multiple sources for user email
      const directEmail = localStorage.getItem('user_email') || sessionStorage.getItem('user_email');
      if (directEmail) return directEmail;
      
      // Try from user session data
      const userSession = sessionStorage.getItem('resultUser');
      if (userSession) {
        const userData = JSON.parse(userSession);
        return userData.email || userData.username || null;
      }
      
      return null;
    } catch (error) {
      console.error('Error getting current user email:', error);
      return null;
    }
  }

  /**
   * Validate email format
   */
  private static validateEmail(email: string): boolean {
    if (!email || !email.trim()) return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  }

  /**
   * Get all PINE collection indices for the current user
   */
  static async getUserIndices(userEmail?: string): Promise<UserIndicesResponse> {
    try {
      const email = userEmail || this.getCurrentUserEmail();
      
      if (!email) {
        return {
          success: false,
          message: 'User email not found. Please log in again.',
          indices: [],
          count: 0,
          user_email: '',
          error: 'No user email available'
        };
      }

      if (!this.validateEmail(email)) {
        return {
          success: false,
          message: 'Invalid email format',
          indices: [],
          count: 0,
          user_email: email,
          error: 'Invalid email format'
        };
      }

      const response = await fetch(`${API_BASE_URL}/api/user-indices`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data: UserIndicesResponse = await response.json();
      
      console.log(`Retrieved ${data.count} indices for user ${email}:`, data.indices);
      return data;

    } catch (error) {
      console.error('Error fetching user indices:', error);
      return {
        success: false,
        message: `Failed to fetch user indices: ${error instanceof Error ? error.message : 'Unknown error'}`,
        indices: [],
        count: 0,
        user_email: userEmail || '',
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Validate if the current user has access to a specific index
   */
  static async validateUserAccess(indexName: string, userEmail?: string): Promise<UserAccessResponse> {
    try {
      const email = userEmail || this.getCurrentUserEmail();
      
      if (!email) {
        return {
          success: false,
          has_access: false,
          message: 'User email not found. Please log in again.',
          user_email: '',
          index_name: indexName,
          error: 'No user email available'
        };
      }

      if (!this.validateEmail(email)) {
        return {
          success: false,
          has_access: false,
          message: 'Invalid email format',
          user_email: email,
          index_name: indexName,
          error: 'Invalid email format'
        };
      }

      if (!indexName || !indexName.trim()) {
        return {
          success: false,
          has_access: false,
          message: 'Index name is required',
          user_email: email,
          index_name: indexName,
          error: 'Index name is required'
        };
      }

      const response = await fetch(`${API_BASE_URL}/api/validate-user-access`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email, 
          index_name: indexName.trim() 
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data: UserAccessResponse = await response.json();
      
      console.log(`Access validation for user ${email} to index ${indexName}:`, data.has_access);
      return data;

    } catch (error) {
      console.error('Error validating user access:', error);
      return {
        success: false,
        has_access: false,
        message: `Failed to validate access: ${error instanceof Error ? error.message : 'Unknown error'}`,
        user_email: userEmail || '',
        index_name: indexName,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Filter a list of indices to only include those accessible by the current user
   */
  static async filterIndicesByUser(indices: string[], userEmail?: string): Promise<string[]> {
    try {
      if (!indices || indices.length === 0) {
        return [];
      }

      const email = userEmail || this.getCurrentUserEmail();
      if (!email) {
        console.warn('No user email found for filtering indices');
        return [];
      }

      // Get user's accessible indices
      const userIndicesResponse = await this.getUserIndices(email);
      if (!userIndicesResponse.success) {
        console.error('Failed to get user indices for filtering:', userIndicesResponse.error);
        return [];
      }

      // Filter the provided indices to only include user's accessible ones
      const filteredIndices = indices.filter(index => 
        userIndicesResponse.indices.includes(index)
      );

      console.log(`Filtered ${indices.length} indices to ${filteredIndices.length} for user ${email}`);
      return filteredIndices;

    } catch (error) {
      console.error('Error filtering indices by user:', error);
      return [];
    }
  }

  /**
   * Check if the current user has access to any indices
   */
  static async hasUserIndices(userEmail?: string): Promise<boolean> {
    try {
      const userIndicesResponse = await this.getUserIndices(userEmail);
      return userIndicesResponse.success && userIndicesResponse.count > 0;
    } catch (error) {
      console.error('Error checking if user has indices:', error);
      return false;
    }
  }

  /**
   * Get a safe index name for the user (first available index or 'default')
   */
  static async getSafeIndexForUser(userEmail?: string): Promise<string> {
    try {
      const userIndicesResponse = await this.getUserIndices(userEmail);
      
      if (userIndicesResponse.success && userIndicesResponse.indices.length > 0) {
        // Return the first available index
        return userIndicesResponse.indices[0];
      }
      
      // Fallback to 'default'
      return 'default';
      
    } catch (error) {
      console.error('Error getting safe index for user:', error);
      return 'default';
    }
  }
}

// Export convenience functions for backward compatibility
export const getUserPineIndices = (userEmail?: string) => PineFilterService.getUserIndices(userEmail);
export const validateUserPineAccess = (indexName: string, userEmail?: string) => PineFilterService.validateUserAccess(indexName, userEmail);
export const filterPineIndicesByUser = (indices: string[], userEmail?: string) => PineFilterService.filterIndicesByUser(indices, userEmail);
