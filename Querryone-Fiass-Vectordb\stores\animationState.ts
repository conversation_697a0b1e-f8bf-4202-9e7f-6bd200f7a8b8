// stores/animationState.ts
import { create } from "zustand";
import { persist } from "zustand/middleware";

// Animation status enum
export enum AnimationStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  SKIPPED = 'skipped'
}

// Message animation state
interface MessageAnimationState {
  status: AnimationStatus;
  progress: number; // 0-1
  lastUpdated: number; // timestamp
}

interface AnimationState {
  // Map of message IDs to their animation state
  animatedMessages: Record<string, MessageAnimationState>;

  // Current message being animated
  currentlyAnimating: string | null;

  // Queue of messages waiting to be animated
  animationQueue: string[];

  // Add a message to the animation queue
  queueAnimation: (messageId: string) => void;

  // Start animating a message
  startAnimation: (messageId: string) => void;

  // Update animation progress
  updateProgress: (messageId: string, progress: number) => void;

  // Mark a message as completed
  completeAnimation: (messageId: string, skipped?: boolean) => void;

  // Check if a message has been animated
  getAnimationState: (messageId: string) => MessageAnimationState | null;

  // Check if a message has been animated
  hasBeenAnimated: (messageId: string) => boolean;

  // Get the next message to animate
  getNextMessageToAnimate: () => string | null;

  // Reset the animation state (useful for testing)
  resetAnimationState: () => void;
}

// Default animation state for a new message
const createDefaultAnimationState = (): MessageAnimationState => ({
  status: AnimationStatus.PENDING,
  progress: 0,
  lastUpdated: Date.now()
});

export const useAnimationState = create<AnimationState>()(
  persist(
    (set, get) => ({
      animatedMessages: {},
      currentlyAnimating: null,
      animationQueue: [],

      queueAnimation: (messageId: string) => {
        set((state) => {
          // If message is already in queue or has been animated, don't add it again
          if (
            state.animationQueue.includes(messageId) ||
            state.animatedMessages[messageId]?.status === AnimationStatus.COMPLETED ||
            state.animatedMessages[messageId]?.status === AnimationStatus.SKIPPED
          ) {
            return state;
          }

          // Add message to animation state if it doesn't exist
          const updatedAnimatedMessages = { ...state.animatedMessages };
          if (!updatedAnimatedMessages[messageId]) {
            updatedAnimatedMessages[messageId] = createDefaultAnimationState();
          }

          return {
            animatedMessages: updatedAnimatedMessages,
            animationQueue: [...state.animationQueue, messageId]
          };
        });
      },

      startAnimation: (messageId: string) => {
        set((state) => {
          // Update the message state
          const updatedAnimatedMessages = { ...state.animatedMessages };
          updatedAnimatedMessages[messageId] = {
            status: AnimationStatus.IN_PROGRESS,
            progress: 0,
            lastUpdated: Date.now()
          };

          // Remove from queue if it's there
          const updatedQueue = state.animationQueue.filter(id => id !== messageId);

          return {
            animatedMessages: updatedAnimatedMessages,
            currentlyAnimating: messageId,
            animationQueue: updatedQueue
          };
        });
      },

      updateProgress: (messageId: string, progress: number) => {
        set((state) => {
          // Only update if the message exists and is in progress
          if (
            !state.animatedMessages[messageId] ||
            state.animatedMessages[messageId].status !== AnimationStatus.IN_PROGRESS
          ) {
            return state;
          }

          const updatedAnimatedMessages = { ...state.animatedMessages };
          updatedAnimatedMessages[messageId] = {
            ...updatedAnimatedMessages[messageId],
            progress,
            lastUpdated: Date.now()
          };

          return { animatedMessages: updatedAnimatedMessages };
        });
      },

      completeAnimation: (messageId: string, skipped = false) => {
        set((state) => {
          // Only update if the message exists
          if (!state.animatedMessages[messageId]) {
            return state;
          }

          const updatedAnimatedMessages = { ...state.animatedMessages };
          updatedAnimatedMessages[messageId] = {
            status: skipped ? AnimationStatus.SKIPPED : AnimationStatus.COMPLETED,
            progress: 1,
            lastUpdated: Date.now()
          };

          // Clear currently animating if this was the current message
          const updatedCurrentlyAnimating =
            state.currentlyAnimating === messageId ? null : state.currentlyAnimating;

          return {
            animatedMessages: updatedAnimatedMessages,
            currentlyAnimating: updatedCurrentlyAnimating
          };
        });
      },

      getAnimationState: (messageId: string) => {
        return get().animatedMessages[messageId] || null;
      },

      hasBeenAnimated: (messageId: string) => {
        const state = get().animatedMessages[messageId];
        return state?.status === AnimationStatus.COMPLETED ||
               state?.status === AnimationStatus.SKIPPED;
      },

      getNextMessageToAnimate: () => {
        const state = get();

        // If already animating something, don't start a new one
        if (state.currentlyAnimating) {
          return null;
        }

        // Return the first message in the queue
        return state.animationQueue.length > 0 ? state.animationQueue[0] : null;
      },

      resetAnimationState: () => {
        set({
          animatedMessages: {},
          currentlyAnimating: null,
          animationQueue: []
        });
      }
    }),
    {
      name: "animation-state",
      // Only store completed animations in localStorage
      partialize: (state) => {
        const completedAnimations: Record<string, MessageAnimationState> = {};

        Object.entries(state.animatedMessages).forEach(([messageId, animState]) => {
          if (
            animState.status === AnimationStatus.COMPLETED ||
            animState.status === AnimationStatus.SKIPPED
          ) {
            completedAnimations[messageId] = animState;
          }
        });

        return {
          animatedMessages: completedAnimations,
          currentlyAnimating: null,
          animationQueue: []
        };
      }
    }
  )
);
