/* styles/typingAnimation.css */
.typing-animation-professional {
  position: relative;
}

.typing-animation-professional .inline-code {
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  padding: 0.2em 0.4em;
  font-family: monospace;
  font-size: 0.9em;
}

.typing-animation-professional .block-code {
  display: block;
  background-color: rgba(0, 0, 0, 0.05);
  border-radius: 5px;
  padding: 1em;
  margin: 1em 0;
  font-family: monospace;
  font-size: 0.9em;
  white-space: pre-wrap;
  overflow-x: auto;
}

/* Cursor animation */
@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0; }
}

.typing-animation-professional .cursor {
  display: inline-block;
  width: 0.5em;
  height: 1.2em;
  background-color: currentColor;
  margin-left: 2px;
  animation: blink 1s infinite;
  vertical-align: middle;
}

/* Skip button styling */
.typing-animation-professional button {
  transition: all 0.2s ease;
  opacity: 0.7;
}

.typing-animation-professional button:hover {
  opacity: 1;
  transform: translateX(2px);
}

/* Animation effects */
@keyframes fade-in {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slide-up {
  from { transform: translateY(10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

.typing-animation-professional {
  animation: fade-in 0.3s ease-out;
}

.typing-animation-professional button {
  animation: slide-up 0.5s ease-out forwards;
}
