#!/usr/bin/env python3
"""
Backend Connection Test Script
Tests if the backend server is running on the configured port and responds correctly.
"""

import requests
import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get backend URL from environment or use default
BACKEND_URL = os.getenv("BACKEND_SERVER_URL", "http://localhost:5010")

def test_backend_health():
    """Test if the backend health endpoint is accessible"""
    try:
        print(f"🧪 Testing backend health at: {BACKEND_URL}")
        response = requests.get(f"{BACKEND_URL}/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend health check passed!")
            print(f"   Server Status: {data.get('data', {}).get('server_status', 'Unknown')}")
            print(f"   Default Embed Model: {data.get('data', {}).get('default_embed_model', 'Unknown')}")
            
            # Check service availability
            upload_services = data.get('data', {}).get('upload_services', {})
            active_services = sum(1 for status in upload_services.values() if status)
            total_services = len(upload_services)
            print(f"   Active Services: {active_services}/{total_services}")
            
            return True
        else:
            print(f"❌ Backend health check failed: HTTP {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Cannot connect to backend at {BACKEND_URL}")
        print("   Make sure the backend server is running:")
        print("   cd python-fiass-backend && python full_code.py")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Backend request timed out at {BACKEND_URL}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_cors_configuration():
    """Test CORS configuration"""
    try:
        print(f"\n🧪 Testing CORS configuration...")
        headers = {
            'Origin': 'http://localhost:3000',
            'Content-Type': 'application/json'
        }
        
        # Test OPTIONS request (preflight)
        response = requests.options(f"{BACKEND_URL}/api/health", headers=headers, timeout=5)
        
        if response.status_code in [200, 204]:
            cors_origin = response.headers.get('Access-Control-Allow-Origin')
            cors_methods = response.headers.get('Access-Control-Allow-Methods')
            print("✅ CORS preflight check passed!")
            print(f"   Allowed Origin: {cors_origin}")
            print(f"   Allowed Methods: {cors_methods}")
            return True
        else:
            print(f"❌ CORS preflight failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ CORS test error: {e}")
        return False

def test_translation_endpoint():
    """Test translation endpoint"""
    try:
        print(f"\n🧪 Testing translation endpoint...")
        data = {
            'text': 'Hello, how are you?',
            'source_lang': 'en',
            'target_lang': 'ta'
        }
        
        response = requests.post(f"{BACKEND_URL}/api/translate", json=data, timeout=10)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                translated_text = result.get('data', {}).get('translated_text', '')
                print("✅ Translation endpoint working!")
                print(f"   Original: {data['text']}")
                print(f"   Translated: {translated_text}")
                return True
            else:
                print(f"❌ Translation failed: {result.get('error', 'Unknown error')}")
                return False
        else:
            print(f"❌ Translation endpoint failed: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Translation test error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Backend Connection Test Suite")
    print("=" * 50)
    print(f"Backend URL: {BACKEND_URL}")
    print("=" * 50)
    
    tests = [
        ("Health Check", test_backend_health),
        ("CORS Configuration", test_cors_configuration),
        ("Translation Endpoint", test_translation_endpoint)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        if test_func():
            passed += 1
        print()  # Add spacing between tests
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Backend is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the backend configuration.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
