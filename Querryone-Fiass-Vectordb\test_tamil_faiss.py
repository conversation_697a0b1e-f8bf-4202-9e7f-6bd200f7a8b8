#!/usr/bin/env python3
"""
Test script to verify Tamil FAISS functionality
"""

import requests
import json

def test_multilingual_financial_query():
    """Test the multilingual financial query endpoint"""
    
    # Test data
    test_queries = [
        {
            "query": "What is mutual fund?",
            "language": "English"
        },
        {
            "query": "மியூச்சுவல் ஃபண்ட் என்றால் என்ன?",
            "language": "Tamil"
        },
        {
            "query": "How to invest in stock market?",
            "language": "English"
        }
    ]
    
    # Backend URL
    backend_url = "http://localhost:5010/api/multilingual_financial_query"
    
    print("🧪 Testing Multilingual Financial Query Endpoint")
    print("=" * 60)
    
    for i, test_case in enumerate(test_queries, 1):
        print(f"\n📝 Test Case {i}: {test_case['language']}")
        print(f"Query: {test_case['query']}")
        print("-" * 40)
        
        try:
            # Make request
            response = requests.post(backend_url, json={
                "query": test_case["query"],
                "client_email": "<EMAIL>",
                "api_key": "",
                "index_name": "financial"
            }, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get("success"):
                    print("✅ Success!")
                    print(f"AI Response: {data.get('ai_response', 'No response')[:100]}...")
                    print(f"Related Questions: {len(data.get('related_questions', []))}")
                    print(f"Retrieved Documents: {len(data.get('retrieved_documents', []))}")
                    
                    # Check translation metadata
                    translation_meta = data.get('translation_metadata', {})
                    print(f"Detected Language: {translation_meta.get('detected_language', 'Unknown')}")
                    print(f"Translation Successful: {translation_meta.get('query_translation_successful', False)}")
                    
                else:
                    print("❌ Request failed:")
                    print(f"Error: {data.get('error', 'Unknown error')}")
                    
            else:
                print(f"❌ HTTP Error {response.status_code}")
                try:
                    error_data = response.json()
                    print(f"Error: {error_data.get('error', 'Unknown error')}")
                except:
                    print(f"Response: {response.text}")
                    
        except requests.exceptions.ConnectionError:
            print("❌ Connection Error: Backend server not running")
            print("Please start the backend server with: python full_code.py")
            break
        except requests.exceptions.Timeout:
            print("❌ Timeout Error: Request took too long")
        except Exception as e:
            print(f"❌ Unexpected Error: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed")

def test_health_check():
    """Test the health check endpoint"""
    
    print("\n🏥 Testing Health Check Endpoint")
    print("-" * 40)
    
    try:
        response = requests.get("http://localhost:5010/api/health", timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Backend server is healthy")
            print(f"Server Status: {data.get('data', {}).get('server_status', 'Unknown')}")
            print(f"FAISS Data Dir: {data.get('data', {}).get('faiss_data_dir', 'Unknown')}")
            print(f"Default Model: {data.get('data', {}).get('default_embed_model', 'Unknown')}")
            
            # Check upload services
            upload_services = data.get('data', {}).get('upload_services', {})
            active_services = sum(1 for status in upload_services.values() if status)
            total_services = len(upload_services)
            print(f"Upload Services: {active_services}/{total_services} active")
            
        else:
            print(f"❌ Health check failed with status {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Connection Error: Backend server not running")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Tamil FAISS Backend Test Suite")
    print("=" * 60)
    
    # First check if backend is running
    if test_health_check():
        # Run the main tests
        test_multilingual_financial_query()
    else:
        print("\n💡 To start the backend server:")
        print("   cd python-fiass-backend")
        print("   python full_code.py")
