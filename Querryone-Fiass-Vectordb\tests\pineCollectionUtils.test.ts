/**
 * Test file for PINE collection utilities
 * This file contains tests to verify the new user handling functionality
 */

import {
  createPineCollectionEntry,
  fetchPineConfig,
  DEFAULT_PINE_CONFIG,
  handleUserPineConfiguration
} from '../utils/pineCollectionUtils';

// Mock fetch for testing
global.fetch = jest.fn();

describe('PINE Collection Utils', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true,
    });
  });

  describe('createPineCollectionEntry', () => {
    it('should create a new PINE collection entry successfully', async () => {
      const mockResponse = {
        ok: true,
        json: async () => ({ success: true })
      };
      
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await createPineCollectionEntry('<EMAIL>');

      expect(result).toEqual({
        client: '<EMAIL>',
        api_key: DEFAULT_PINE_CONFIG.api_key,
        index_name: DEFAULT_PINE_CONFIG.index_name
      });

      expect(fetch).toHaveBeenCalledWith(
        'https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE',
        expect.objectContaining({
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'xxxid': 'PINE'
          },
          body: JSON.stringify({
            client: '<EMAIL>',
            api_key: DEFAULT_PINE_CONFIG.api_key,
            index_name: DEFAULT_PINE_CONFIG.index_name
          })
        })
      );
    });

    it('should return null when API call fails', async () => {
      const mockResponse = {
        ok: false,
        status: 500
      };
      
      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await createPineCollectionEntry('<EMAIL>');

      expect(result).toBeNull();
    });
  });

  describe('fetchPineConfig', () => {
    it('should return existing config when user exists in PINE collection', async () => {
      const existingConfig = {
        client: '<EMAIL>',
        api_key: 'existing_api_key',
        index_name: 'existing_index'
      };

      const mockResponse = {
        ok: true,
        json: async () => ({
          statusCode: 200,
          source: [JSON.stringify(existingConfig)]
        })
      };

      (fetch as jest.Mock).mockResolvedValue(mockResponse);

      const result = await fetchPineConfig('<EMAIL>');

      expect(result).toEqual({
        api_key: 'existing_api_key',
        index_name: 'existing_index'
      });
    });

    it('should return default config when user does not exist in PINE collection', async () => {
      // Check if user exists (returns empty)
      const mockCheckResponse = {
        ok: true,
        json: async () => ({
          statusCode: 200,
          source: []
        })
      };

      (fetch as jest.Mock).mockResolvedValueOnce(mockCheckResponse);

      const result = await fetchPineConfig('<EMAIL>');

      expect(result).toEqual(DEFAULT_PINE_CONFIG);

      // Should have made only one API call (no creation)
      expect(fetch).toHaveBeenCalledTimes(1);
    });

    it('should return default config when no email provided', async () => {
      const result = await fetchPineConfig('');

      expect(result).toEqual(DEFAULT_PINE_CONFIG);
      expect(fetch).not.toHaveBeenCalled();
    });
  });

  describe('handleUserPineConfiguration', () => {
    it('should use existing localStorage config when available', async () => {
      const existingConfig = {
        api_key: 'stored_api_key',
        index_name: 'stored_index'
      };

      (window.localStorage.getItem as jest.Mock)
        .mockReturnValueOnce('stored_api_key')
        .mockReturnValueOnce('stored_index');

      const result = await handleUserPineConfiguration('<EMAIL>');

      expect(result).toEqual(existingConfig);
      expect(fetch).not.toHaveBeenCalled();
    });

    it('should fetch and store config when not in localStorage', async () => {
      // No existing localStorage data
      (window.localStorage.getItem as jest.Mock).mockReturnValue(null);

      const mockResponse = {
        ok: true,
        json: async () => ({
          statusCode: 200,
          source: []
        })
      };

      (fetch as jest.Mock).mockResolvedValueOnce(mockResponse);

      const result = await handleUserPineConfiguration('<EMAIL>');

      expect(result).toEqual(DEFAULT_PINE_CONFIG);

      // Should have stored in localStorage
      expect(window.localStorage.setItem).toHaveBeenCalledWith('pinecone_api_key', DEFAULT_PINE_CONFIG.api_key);
      expect(window.localStorage.setItem).toHaveBeenCalledWith('pinecone_index_name', DEFAULT_PINE_CONFIG.index_name);
    });
  });
});

// Integration test example
describe('Integration Test - New User Flow', () => {
  it('should handle complete new user flow', async () => {
    // Mock localStorage as empty
    (window.localStorage.getItem as jest.Mock).mockReturnValue(null);

    // Mock API responses
    const mockCheckResponse = {
      ok: true,
      json: async () => ({
        statusCode: 200,
        source: [] // No existing user
      })
    };

    const mockCreateResponse = {
      ok: true,
      json: async () => ({ success: true })
    };
    
    (fetch as jest.Mock)
      .mockResolvedValueOnce(mockCheckResponse)
      .mockResolvedValueOnce(mockCreateResponse);

    // Test the complete flow
    const result = await handleUserPineConfiguration('<EMAIL>');

    // Verify the result
    expect(result).toEqual({
      api_key: DEFAULT_PINE_CONFIG.api_key,
      index_name: DEFAULT_PINE_CONFIG.index_name
    });

    // Verify API calls were made
    expect(fetch).toHaveBeenCalledTimes(2);
    
    // Verify first call was to check existing user
    expect(fetch).toHaveBeenNthCalledWith(1, 
      expect.stringContaining('retrievecollection?ColName=PINE&filtercount=1&f1_field=client_S&f1_op=eq&f1_value=newuser%40example.com'),
      expect.objectContaining({ method: 'GET' })
    );

    // Verify second call was to create new entry
    expect(fetch).toHaveBeenNthCalledWith(2,
      'https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE',
      expect.objectContaining({ method: 'POST' })
    );

    // Verify localStorage was updated
    expect(window.localStorage.setItem).toHaveBeenCalledWith('pinecone_api_key', DEFAULT_PINE_CONFIG.api_key);
    expect(window.localStorage.setItem).toHaveBeenCalledWith('pinecone_index_name', DEFAULT_PINE_CONFIG.index_name);
  });
});
