#!/usr/bin/env python3
"""
Quick translation test
"""

import requests
import time

def test_direct_translation():
    """Test direct translation API"""
    print("🧪 Testing direct translation...")
    
    try:
        # Test MyMemory API directly
        url = "https://api.mymemory.translated.net/get"
        params = {
            'q': 'Hello, how are you?',
            'langpair': 'en|te'
        }
        
        response = requests.get(url, params=params, timeout=10)
        if response.status_code == 200:
            data = response.json()
            if data.get('responseStatus') == 200:
                translated_text = data.get('responseData', {}).get('translatedText', '')
                print(f"✅ Direct translation working: 'Hello, how are you?' -> '{translated_text}'")
                return True
            else:
                print(f"❌ Translation API error: {data}")
        else:
            print(f"❌ Translation API HTTP error: {response.status_code}")
    except Exception as e:
        print(f"❌ Translation API exception: {e}")
    
    return False

def test_backend_health():
    """Test if backend is running"""
    print("🧪 Testing backend health...")
    
    try:
        response = requests.get("http://localhost:5010/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
            return True
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Backend not accessible: {e}")
    
    return False

if __name__ == "__main__":
    print("🚀 Quick Translation Test")
    print("=" * 30)
    
    # Test direct translation
    direct_works = test_direct_translation()
    time.sleep(1)
    
    # Test backend
    backend_works = test_backend_health()
    
    print("\n" + "=" * 30)
    print("📊 Results:")
    print(f"Direct Translation: {'✅ Working' if direct_works else '❌ Failed'}")
    print(f"Backend Health: {'✅ Working' if backend_works else '❌ Failed'}")
    
    if direct_works:
        print("\n🎉 Translation API is working! Your app should be able to translate.")
    else:
        print("\n❌ Translation API is not working. Check network connectivity.")
