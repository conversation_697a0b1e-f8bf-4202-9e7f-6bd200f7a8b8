import React, { <PERSON>E<PERSON>, useState, useRef, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { v4 as uuidv4 } from "uuid";
import {
  PiArrowUp,
  PiMicrophone,
  PiPaperclip,
  PiLightbulb,
  PiSparkle,
  PiGlobe,
  PiStop,
  PiWaveform,
  PiPencilSimple,
  PiCheck,
  PiSpinner
} from "react-icons/pi";
import { useChat<PERSON>and<PERSON> } from "@/stores/chatList";
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';
import { TranslationService } from "@/components/chatComponents/services/TranslationService";

// API configuration
const API_CONFIG = {
  // Production endpoint (previously used)
  PROD_ENDPOINT: "http://localhost:5010/financial_query",
  // Development endpoint for suggest.py (running locally)
  DEV_ENDPOINT: "http://localhost:5010/financial_query",
  // Use DEV_ENDPOINT for local development, PROD_ENDPOINT for production
  ACTIVE_ENDPOINT: "http://localhost:5010/financial_query",
  // PINE collection endpoint
  PINE_COLLECTION_ENDPOINT: "https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE"
};

interface ChatBoxProps {
  onLanguageChange?: (language: string) => void;
}

function ChatBox({ onLanguageChange }: ChatBoxProps = {}) {
  const [inputText, setInputText] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  // Add language dropup state for responsive design
  const [showLanguageMenu, setShowLanguageMenu] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("English");
  const [isListening, setIsListening] = useState(false);
  const [speaking, setSpeaking] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  // Track recent words for word count
  const [recentWords, setRecentWords] = useState<string[]>([]);
  // Add state for transcript editing
  const [isEditingTranscript, setIsEditingTranscript] = useState(false);
  const [editedTranscript, setEditedTranscript] = useState("");
  // Add state for language validation
  const [languageError, setLanguageError] = useState<string | null>(null);
  // Add state to track if language buttons should be disabled
  const [languageButtonsDisabled, setLanguageButtonsDisabled] = useState(false);
  // Add state for user email from localStorage
  const [userEmail, setUserEmail] = useState<string | null>(null);
  // Add state for Pinecone indexes
  const [pineconeIndexes, setPineconeIndexes] = useState<string[]>(['querry', 'finan', 'financialnews', 'mannit']);
  const [selectedIndex, setSelectedIndex] = useState<string>('');
  const [apiEnvironment, setApiEnvironment] = useState<'development' | 'production'>('production');
  // Add state for index selector dropdown visibility
  const [showIndexSelector, setShowIndexSelector] = useState<boolean>(false);
  // Add state for index selection confirmation
  const [showIndexConfirmation, setShowIndexConfirmation] = useState<boolean>(false);
  // Reference for the index selector dropdown
  const indexSelectorRef = useRef<HTMLDivElement>(null);
  const suggestionsRef = useRef<HTMLDivElement>(null);
  // Add language menu ref for responsive design
  const languageMenuRef = useRef<HTMLDivElement>(null);
  const transcriptRef = useRef<HTMLDivElement>(null);
  const editableTranscriptRef = useRef<HTMLTextAreaElement>(null);
  const router = useRouter();
  const path = usePathname();
  const { userQuery, handleSubmit, addMessage, setUserQuery, isLoading, setIsLoading } = useChatHandler();

  // Speech detection timer
  const speakingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // Translation state for performance optimization
  const [isTranslating, setIsTranslating] = useState(false);
  const translationTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Available languages for voice input with their language codes - English, Tamil, Telugu, and Kannada
  const languages = [
    { name: "English", code: "en-US", color: "blue" },
    { name: "Tamil", code: "ta-IN", color: "purple" },
    { name: "Telugu", code: "te-IN", color: "green" },
    { name: "Kannada", code: "kn-IN", color: "orange" }
  ];

  // Get the language code for the selected language
  const getLanguageCode = () => {
    const language = languages.find(lang => lang.name === selectedLanguage);
    return language ? language.code : "en-US"; // Default to English if not found
  };

  // Initialize with default values for server-side rendering
  const [transcript, setTranscript] = useState("");
  const [listening, setListening] = useState(false);
  const [browserSupportsSpeechRecognition, setBrowserSupportsSpeechRecognition] = useState(false);
  const [isMicrophoneAvailable, setIsMicrophoneAvailable] = useState(false);

  // Speech recognition setup - only run on client side
  const {
    transcript: clientTranscript,
    listening: clientListening,
    resetTranscript,
    browserSupportsSpeechRecognition: clientBrowserSupport,
    isMicrophoneAvailable: clientMicrophoneAvailable,
    // We don't need these transcripts as we're using the combined transcript
    // interimTranscript,
    // finalTranscript
  } = typeof window !== 'undefined' ? useSpeechRecognition({
    clearTranscriptOnListen: false,
    transcribing: true,
    commands: [
      {
        command: '*',
        callback: (command) => {
          console.log('Voice command detected:', command);
        }
      }
    ]
  }) : {
    transcript: "",
    listening: false,
    resetTranscript: () => {},
    browserSupportsSpeechRecognition: false,
    isMicrophoneAvailable: false
  };

  // Handle click outside to close dropdowns
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // Close suggestions dropdown if clicked outside
      if (suggestionsRef.current && !suggestionsRef.current.contains(event.target as Node)) {
        setShowSuggestions(false);
      }

      // Close language menu if clicked outside
      if (languageMenuRef.current && !languageMenuRef.current.contains(event.target as Node)) {
        setShowLanguageMenu(false);
      }

      // Close index selector if clicked outside
      if (indexSelectorRef.current && !indexSelectorRef.current.contains(event.target as Node)) {
        setShowIndexSelector(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Auto-scroll transcript to bottom when it gets too long
  useEffect(() => {
    if (transcriptRef.current && transcript) {
      transcriptRef.current.scrollTop = transcriptRef.current.scrollHeight;
    }
  }, [transcript]);

  // Update word count when transcript changes
  useEffect(() => {
    if (transcript) {
      const words = transcript.trim().split(/\s+/).filter(word => word !== "");
      setWordCount(words.length);

      // Track most recent words for animation
      if (words.length > 0) {
        const lastWord = words[words.length - 1];
        if (lastWord && lastWord.length > 0) {
          setRecentWords((prev: string[]) => {
            const newWords = [...prev, lastWord];
            return newWords.slice(-5); // Keep only the last 5 words
          });
        }
      }

      // Set speaking state when new words are detected
      if (isListening) {
        setSpeaking(true);

        // Clear previous timer if it exists
        if (speakingTimerRef.current) {
          clearTimeout(speakingTimerRef.current);
        }

        // Set a timer to detect when speaking has paused
        speakingTimerRef.current = setTimeout(() => {
          setSpeaking(false);
        }, 1500); // 1.5 seconds of silence is considered a pause
      }
    }
  }, [transcript, isListening]);

  // Update input text when transcript changes
  useEffect(() => {
    // Always update with the latest transcript when listening
    if (listening || isListening) {
      setInputText(transcript);
      setUserQuery(transcript);

      // Also update the edited transcript to match the current transcript
      // This ensures that when we switch to edit mode, we have the latest transcript
      setEditedTranscript(transcript);

      console.log("Speech recognized:", transcript);

      if (transcript && !isListening) {
        console.log("Got transcript but isListening was false. Fixing state...");
        setIsListening(true);
      }
    }
  }, [transcript, listening, isListening, setUserQuery]);

  // Separate effect to handle listening state changes
  useEffect(() => {
    if (isListening) {
      if (inputText !== "") {
        setInputText("");
        setUserQuery("");
      }
    } else {
      console.log("Stopped listening, transcript:", transcript);

      if (transcript && transcript.trim() !== "") {
        setInputText(transcript);
        setUserQuery(transcript);
      } else if (typeof window !== 'undefined') {
        resetTranscript();
      }

      // Reset speaking state and clear any timers
      setSpeaking(false);
      if (speakingTimerRef.current) {
        clearTimeout(speakingTimerRef.current);
        speakingTimerRef.current = null;
      }

      // No longer need to close language dropdown
      // setShowLanguageDropdown(false);
    }

    console.log("Listening state changed:", isListening);
  }, [isListening, transcript, inputText]);

  // Update listening state when speech recognition status changes
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip on server-side

    console.log("Speech recognition listening state changed:", listening);

    if (isListening !== listening) {
      setIsListening(listening);
    }

    if (!listening && isListening) {
      console.warn("Speech recognition stopped unexpectedly. Checking if we should restart...");

      // Only attempt to restart if we're still supposed to be listening
      // This prevents infinite restart loops
      const restartTimeout = setTimeout(() => {
        if (isListening && typeof window !== 'undefined') {
          console.log("Attempting to restart speech recognition...");

          // Try to restart speech recognition
          const languageCode = getLanguageCode();
          SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });
        }
      }, 1000);

      return () => clearTimeout(restartTimeout);
    }
  }, [listening, isListening]);

  // Effect to handle language changes
  useEffect(() => {
    if (typeof window === 'undefined') return; // Skip on server-side

    console.log(`Language changed to: ${selectedLanguage}`);
    // This effect can be used to update any UI elements when language changes
    // The placeholder text and suggestions are already handled by the render logic

    // If we're listening, we need to restart with the new language
    if (isListening) {
      const restartWithNewLanguage = async () => {
        try {
          await SpeechRecognition.stopListening();
          await new Promise(resolve => setTimeout(resolve, 300));

          const languageCode = getLanguageCode();
          await SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });

          console.log(`Restarted speech recognition with language: ${selectedLanguage} (${languageCode})`);
        } catch (error) {
          console.error("Error restarting speech recognition with new language:", error);
        }
      };

      restartWithNewLanguage();
    }
  }, [selectedLanguage]);

  // Effect to hide index confirmation after 5 seconds
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (showIndexConfirmation) {
      timer = setTimeout(() => {
        setShowIndexConfirmation(false);
      }, 5000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [showIndexConfirmation]);

  // Sample recommended suggestions
  const englishSuggestions = [
    "How does the new tax regime (post-2023) compare with the old tax regime in terms of benefits for salaried individuals?",
    "How has the rise of UPI (Unified Payments Interface) transformed retail banking and cashless transactions in India?",
    "What factors should a retail investor in India consider before investing in IPOs?",
    "How effective has the Pradhan Mantri Jan Dhan Yojana (PMJDY) been in achieving financial inclusion in rural India?",
    "How are fintech startups like Zerodha and Groww changing the investment landscape for young Indians?"
  ];

  // Tamil financial suggestions
  const tamilSuggestions = [
    "இந்தியாவில் டிஜிட்டல் வாலட் மற்றும் மொபைல் பேமெண்ட் பயன்பாடுகள் நிதி சேவைகளை அணுகுவதை எவ்வாறு மாற்றியுள்ளன?",
    "நீண்ட கால ஓய்வூதிய திட்டங்களில் முதலீடு செய்வதற்கான சிறந்த வழிகள் என்ன மற்றும் அவற்றின் வரி நன்மைகள் என்ன?",
    "சிறு மற்றும் நடுத்தர தொழில்களுக்கு (SMEs) இந்தியாவில் கிடைக்கும் நிதி ஆதரவு திட்டங்கள் என்னென்ன?",
    "பங்குச் சந்தை முதலீட்டிற்கும் தங்கம் மற்றும் நிலம் போன்ற பாரம்பரிய முதலீடுகளுக்கும் இடையே உள்ள முக்கிய வேறுபாடுகள் என்ன?",
    "இந்தியாவில் கிரிப்டோகரன்சி மற்றும் டிஜிட்டல் சொத்துக்களுக்கான தற்போதைய ஒழுங்குமுறை நிலைப்பாடு என்ன?"
  ];

  // Telugu financial suggestions
  const teluguSuggestions = [
    "భారతదేశంలో మ్యూచువల్ ఫండ్స్ లో పెట్టుబడి పెట్టడానికి ఉత్తమ వ్యూహాలు ఏమిటి మరియు వాటి ప్రయోజనాలు ఏమిటి?",
    "వ్యక్తిగత ఆర్థిక ప్రణాళిక కోసం డిజిటల్ టూల్స్ మరియు యాప్‌లు ఎలా ఉపయోగపడతాయి?",
    "భారతదేశంలో స్టార్టప్‌లకు వెంచర్ క్యాపిటల్ మరియు ఏంజెల్ ఇన్వెస్టర్‌ల నుండి నిధులు సేకరించడం ఎలా?",
    "రియల్ ఎస్టేట్ పెట్టుబడులకు REIT (రియల్ ఎస్టేట్ ఇన్వెస్ట్‌మెంట్ ట్రస్ట్‌లు) ఎలా ప్రత్యామ్నాయంగా పనిచేస్తాయి?",
    "భారతదేశంలో ఫినాన్షియల్ లిటరసీని మెరుగుపరచడానికి ప్రభుత్వం మరియు ప్రైవేట్ రంగం తీసుకుంటున్న చర్యలు ఏమిటి?"
  ];

  // Kannada financial suggestions
  const kannadaSuggestions = [
    "ಭಾರತದಲ್ಲಿ ಸಣ್ಣ ಉಳಿತಾಯ ಯೋಜನೆಗಳು ಮತ್ತು ಸರ್ಕಾರಿ ಬಾಂಡ್‌ಗಳಲ್ಲಿ ಹೂಡಿಕೆ ಮಾಡುವುದರ ಪ್ರಯೋಜನಗಳು ಯಾವುವು?",
    "ಹಣದುಬ್ಬರದ ಸಮಯದಲ್ಲಿ ಹಣಕಾಸು ಸ್ಥಿರತೆಯನ್ನು ಕಾಪಾಡಿಕೊಳ್ಳಲು ಉತ್ತಮ ಆರ್ಥಿಕ ತಂತ್ರಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಸ್ವಯಂ ಉದ್ಯೋಗಿಗಳು ಮತ್ತು ಫ್ರೀಲ್ಯಾನ್ಸರ್‌ಗಳಿಗೆ ಲಭ್ಯವಿರುವ ತೆರಿಗೆ ಯೋಜನೆ ಮತ್ತು ಹಣಕಾಸು ಸಾಧನಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಹೊಸ ಪೆನ್ಷನ್ ಯೋಜನೆ (NPS) ಮತ್ತು ಅಟಲ್ ಪೆನ್ಷನ್ ಯೋಜನೆ (APY) ನಡುವಿನ ವ್ಯತ್ಯಾಸಗಳು ಯಾವುವು?",
    "ಭಾರತದಲ್ಲಿ ಮಹಿಳಾ ಉದ್ಯಮಿಗಳಿಗೆ ಲಭ್ಯವಿರುವ ವಿಶೇಷ ಹಣಕಾಸು ಯೋಜನೆಗಳು ಮತ್ತು ಸಾಲ ಕಾರ್ಯಕ್ರಮಗಳು ಯಾವುವು?"
  ];

  // Get suggestions based on selected language
  const getSuggestionsByLanguage = () => {
    switch(selectedLanguage) {
      case "Tamil":
        return tamilSuggestions;
      case "Telugu":
        return teluguSuggestions;
      case "Kannada":
        return kannadaSuggestions;
      default:
        return englishSuggestions;
    }
  };

  const recommendedSuggestions = getSuggestionsByLanguage();

  // Extract chat ID from URL
  const chatIdUrl = path && path.includes("/chat/") ? path.split("/chat/")[1] : "";

  // Handle selecting a suggestion
  const handleSelectSuggestion = (suggestion: string) => {
    setInputText(suggestion);
    setUserQuery(suggestion);
    setShowSuggestions(false);
  };

  // Handle selecting a related question
  const handleSelectQuestion = (question: string) => {
    console.log("Selected related question:", question);
    setInputText(question);
    setUserQuery(question);
  };

  // Handle selecting a language for voice input and UI
  const handleSelectLanguage = async (e: React.MouseEvent, language: string) => {
    // Prevent the event from bubbling up and triggering form submission
    e.preventDefault();
    e.stopPropagation();

    setSelectedLanguage(language);
    // Close language menu when a language is selected
    setShowLanguageMenu(false);

    // Clear any language error when the user changes the language
    if (languageError) {
      setLanguageError(null);
    }

    // Update placeholder and UI based on language
    console.log(`Language set to: ${language}`);

    // Notify parent component about language change if callback is provided
    if (onLanguageChange) {
      onLanguageChange(language);
    }

    // If suggestions are showing, close and reopen to refresh the content
    if (showSuggestions) {
      setShowSuggestions(false);
      setTimeout(() => setShowSuggestions(true), 100);
    }

    // If currently listening, restart with new language
    if (isListening && typeof window !== 'undefined') {
      try {
        await SpeechRecognition.stopListening();
        console.log("Speech recognition stopped for language change");

        await new Promise(resolve => setTimeout(resolve, 300));
        await startListening();
      } catch (error) {
        console.error("Error changing speech recognition language:", error);
      }
    }
  };

  // Toggle voice recognition
  const toggleListening = async () => {
    // Skip on server-side
    if (typeof window === 'undefined') {
      console.warn("Attempted to toggle listening on server-side");
      return;
    }

    if (isListening) {
      try {
        await SpeechRecognition.stopListening();
        setIsListening(false); // Explicitly set to false to ensure state is updated

        // Don't reset transcript when stopping, so it can be edited
        // resetTranscript();

        console.log("Speech recognition stopped successfully");
      } catch (error) {
        console.error("Error stopping speech recognition:", error);
      }
    } else {
      await startListening();
    }
  };

  // Toggle between edit and view modes for the transcript
  const toggleTranscriptEditMode = () => {
    if (isEditingTranscript) {
      // Save the edited transcript
      if (editedTranscript.trim() !== "") {
        setInputText(editedTranscript);
        setUserQuery(editedTranscript);
      }
      setIsEditingTranscript(false);
    } else {
      // Enter edit mode
      setIsEditingTranscript(true);

      // Focus the editable textarea after a short delay to ensure it's rendered
      setTimeout(() => {
        if (editableTranscriptRef.current) {
          editableTranscriptRef.current.focus();
        }
      }, 50);
    }
  };

  // Handle changes to the editable transcript
  const handleTranscriptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setEditedTranscript(e.target.value);

    // Update word count
    const words = e.target.value.trim().split(/\s+/).filter(word => word !== "");
    setWordCount(words.length);

    // Clear any language error when the user edits the transcript
    if (languageError) {
      setLanguageError(null);
    }
  };

  // Start listening with the selected language
  const startListening = async () => {
    // Skip on server-side
    if (typeof window === 'undefined') {
      console.warn("Attempted to start listening on server-side");
      return;
    }

    // Only reset transcript if we're starting fresh
    // If we have edited transcript, keep it
    if (!editedTranscript || editedTranscript.trim() === "") {
      resetTranscript();
      setInputText("");
      setUserQuery("");
      setWordCount(0);
      setRecentWords([]);
    }

    const languageCode = getLanguageCode();
    console.log(`Starting speech recognition in ${selectedLanguage} (${languageCode})`);

    try {
      setIsListening(true);

      await new Promise(resolve => setTimeout(resolve, 100));

      if (!browserSupportsSpeechRecognition) {
        console.error("Browser doesn't support speech recognition");
        alert("Your browser doesn't support speech recognition. Please try using Chrome.");
        setIsListening(false);
        return;
      }

      if (!isMicrophoneAvailable) {
        console.error("Microphone is not available");
        alert("Microphone is not available. Please check your microphone permissions.");
        setIsListening(false);
        return;
      }

      await SpeechRecognition.startListening({
        continuous: true,
        language: languageCode,
        interimResults: true
      });

      console.log("Speech recognition started successfully");

      setTimeout(() => {
        if (!listening && typeof window !== 'undefined') {
          console.warn("Speech recognition may not have started properly. Trying again...");
          SpeechRecognition.startListening({
            continuous: true,
            language: languageCode,
            interimResults: true
          });
        }
      }, 500);

    } catch (error) {
      console.error("Error starting speech recognition:", error);
      setIsListening(false);
      alert("There was an error starting speech recognition. Please try again.");
    }
  };

  // Function to detect if text is likely Tamil
  const isTamilText = (text: string): boolean => {
    // Tamil Unicode range: \u0B80-\u0BFF
    const tamilRegex = /[\u0B80-\u0BFF]/;
    return tamilRegex.test(text);
  };

  // Function to detect if text is likely Telugu
  const isTeluguText = (text: string): boolean => {
    // Telugu Unicode range: \u0C00-\u0C7F
    const teluguRegex = /[\u0C00-\u0C7F]/;
    return teluguRegex.test(text);
  };

  // Function to detect if text is likely Kannada
  const isKannadaText = (text: string): boolean => {
    // Kannada Unicode range: \u0C80-\u0CFF
    const kannadaRegex = /[\u0C80-\u0CFF]/;
    return kannadaRegex.test(text);
  };

  // Function to validate if text matches the selected language
  const validateLanguageMatch = (text: string, language: string): boolean => {
    if (!text || text.trim() === "") return true; // Empty text is valid for any language

    // First, remove continuous capital English words (acronyms, proper nouns, etc.)
    // These should be preserved in any language and not affect validation
    const textWithoutCapitalWords = text.replace(/\b[A-Z]{2,}\b/g, '');

    // Check if the remaining text contains characters from different languages
    const hasTamilChars = isTamilText(textWithoutCapitalWords);
    const hasTeluguChars = isTeluguText(textWithoutCapitalWords);
    const hasKannadaChars = isKannadaText(textWithoutCapitalWords);

    // Count how many different language scripts are present
    const scriptCount = (hasTamilChars ? 1 : 0) + (hasTeluguChars ? 1 : 0) + (hasKannadaChars ? 1 : 0);

    // If there are multiple scripts, it's likely a mismatch
    if (scriptCount > 1) {
      return false;
    }

    // English can contain any characters, so we only validate non-English languages
    if (language === "English") {
      // For English, we consider it valid if it doesn't contain Tamil, Telugu, or Kannada characters
      return !(hasTamilChars || hasTeluguChars || hasKannadaChars);
    } else if (language === "Tamil") {
      // For Tamil, it should contain Tamil characters
      return hasTamilChars || textWithoutCapitalWords.trim() === '';
    } else if (language === "Telugu") {
      // For Telugu, it should contain Telugu characters
      return hasTeluguChars || textWithoutCapitalWords.trim() === '';
    } else if (language === "Kannada") {
      // For Kannada, it should contain Kannada characters
      return hasKannadaChars || textWithoutCapitalWords.trim() === '';
    }

    return true; // Default case
  };

  // Function to create new PINE collection entry for new user
  const createPineCollectionEntry = async (userEmail: string) => {
    try {
      console.log(`Creating new PINE collection entry for user: ${userEmail}`);

      // Default values for new users
      const defaultConfig = {
        client: userEmail,
        api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
        index_name: "financialnews"
      };

      const response = await fetch(`https://dev-commonmannit.mannit.co/mannit/eCreateCol?colname=PINE`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "xxxid": "PINE"
        },
        body: JSON.stringify(defaultConfig)
      });

      if (!response.ok) {
        console.error("Failed to create PINE collection entry");
        return null;
      }

      console.log("Successfully created PINE collection entry for new user:", userEmail);
      return defaultConfig;
    } catch (error) {
      console.error("Error creating PINE collection entry:", error);
      return null;
    }
  };

  // Function to fetch PINE collection data for the current user
  // Falls back to default configuration (financialnews index) when:
  // - No user email is found
  // - API requests fail
  // - No PINE data exists for user
  // - Any errors occur
const fetchPineCollection = async () => {
  try {
    const userEmail = localStorage.getItem("user_email");
    console.log("local mail", userEmail);
    if (!userEmail) {
      console.warn("No user email found in localStorage - using default configuration");
      // Set default configuration
      const defaultConfig = {
        api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
        index_name: "financialnews"
      };
      localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
      localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
      return defaultConfig;
    }

    // Use filtered API endpoint to get ALL entries for this user (remove filtercount=1 to get all matches)
    const filterUrl = `https://dev-commonmannit.mannit.co/mannit/retrievecollection?ColName=PINE&f1_field=client&f1_op=eq&f1_value=${encodeURIComponent(userEmail.trim())}`;

    const response = await fetch(filterUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'xxxid': 'PINE'
      }
    });

    if (!response.ok) {
      console.warn(`Failed to fetch PINE collection: ${response.status} - using default configuration`);
      // Set default configuration
      const defaultConfig = {
        api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
        index_name: "financialnews"
      };
      localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
      localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
      return defaultConfig;
    }

    const data = await response.json();
    console.log("PINE collection response:", data);

    // Check if user exists in PINE collection and extract all their indexes
    if (data.statusCode === 200 && data.source && data.source.length > 0) {
      // Parse each item in the source array (they are JSON strings)
      const pineData = data.source.map((item: string) => JSON.parse(item));

      // Extract all indexes and API keys for this user
      const userIndexes: string[] = [];
      const userApiKeys: string[] = [];
      let firstUserEntry: any | null = null;

      pineData.forEach((item: any) => {
        if (item.client && item.client.trim().toLowerCase() === userEmail.trim().toLowerCase()) {
          if (!firstUserEntry) firstUserEntry = item; // Keep first entry for return
          if (item.index_name && !userIndexes.includes(item.index_name)) {
            userIndexes.push(item.index_name);
          }
          if (item.api_key && !userApiKeys.includes(item.api_key)) {
            userApiKeys.push(item.api_key);
          }
        }
      });

      if (userIndexes.length > 0 && userApiKeys.length > 0) {
        // User exists in PINE collection - store all their configurations
        localStorage.setItem("pinecone_api_key", userApiKeys[0]); // Store first API key as default
        localStorage.setItem("pinecone_index_name", userIndexes[0]); // Store first index as default
        localStorage.setItem('pineconeApiKeys', JSON.stringify(userApiKeys)); // Store all API keys
        localStorage.setItem('userPineconeIndexes', JSON.stringify(userIndexes)); // Store all indexes
        console.log("Found existing PINE data for user:", userEmail);
        console.log("User indexes:", userIndexes);
        console.log("User API keys count:", userApiKeys.length);
        return firstUserEntry;
      } else {
        // User doesn't exist in PINE collection - create new entry with defaults
        console.log("No PINE data found for user:", userEmail, "- creating new entry");

        const newConfig = await createPineCollectionEntry(userEmail);

        if (newConfig) {
          // Store the new configuration in localStorage
          localStorage.setItem("pinecone_api_key", newConfig.api_key);
          localStorage.setItem("pinecone_index_name", newConfig.index_name);
          console.log("Created and stored new PINE configuration for user:", userEmail);
          return newConfig;
        } else {
          // Fallback to default values if creation fails
          const defaultConfig = {
            api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
            index_name: "financialnews"
          };
          localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
          localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
          console.log("Using fallback default PINE configuration for user:", userEmail);
          return defaultConfig;
        }
      }
    } else {
      // No PINE data found - create new entry with defaults
      console.log("No PINE data found for user:", userEmail, "- creating new entry");

      const newConfig = await createPineCollectionEntry(userEmail);

      if (newConfig) {
        // Store the new configuration in localStorage
        localStorage.setItem("pinecone_api_key", newConfig.api_key);
        localStorage.setItem("pinecone_index_name", newConfig.index_name);
        console.log("Created and stored new PINE configuration for user:", userEmail);
        return newConfig;
      } else {
        // Fallback to default values if creation fails
        const defaultConfig = {
          api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
          index_name: "financialnews"
        };
        localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
        localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
        console.log("Using fallback default PINE configuration for user:", userEmail);
        return defaultConfig;
      }
    }
  } catch (error) {
    console.warn("Error fetching PINE collection - using default configuration:", error);
    // Fallback to default values on error
    const defaultConfig = {
      api_key: "pcsk_3pBLRt_49J48GS74JYL6yrCZRKMaERk4vNsF6FZgr3L7b7fZ6nqw8c9XUfzQhTwrXyWaua",
      index_name: "financialnews"
    };
    localStorage.setItem("pinecone_api_key", defaultConfig.api_key);
    localStorage.setItem("pinecone_index_name", defaultConfig.index_name);
    return defaultConfig;
  }
};


  // Fast translation function with timeout and parallel processing
  const translateText = async (text: string, sourceLang: string, targetLang: string): Promise<string> => {
    console.log(`🚀 Fast translating from ${sourceLang} to ${targetLang}: ${text.substring(0, 50)}${text.length > 50 ? '...' : ''}`);

    // If source and target languages are the same, return original text immediately
    if (sourceLang === targetLang) {
      console.log("⚠️ Source and target languages are the same, returning original text");
      return text;
    }

    try {
      // Set translation state
      setIsTranslating(true);

      // Use the TranslationService with a timeout for faster responses
      const translationPromise = TranslationService.translateWithCapitalWordsPreservation(text, sourceLang, targetLang);

      // Add timeout to prevent long waits
      const timeoutPromise = new Promise<string>((_, reject) => {
        translationTimeoutRef.current = setTimeout(() => {
          reject(new Error('Translation timeout - using original text'));
        }, 3000); // 3 second timeout for faster responses
      });

      const result = await Promise.race([translationPromise, timeoutPromise]);

      // Clear timeout if translation completed
      if (translationTimeoutRef.current) {
        clearTimeout(translationTimeoutRef.current);
        translationTimeoutRef.current = null;
      }

      console.log(`✅ Fast translation completed: ${result.substring(0, 50)}${result.length > 50 ? '...' : ''}`);
      return result;

    } catch (error) {
      console.warn(`⚠️ Translation failed or timed out, using original text:`, error);
      // Clear timeout on error
      if (translationTimeoutRef.current) {
        clearTimeout(translationTimeoutRef.current);
        translationTimeoutRef.current = null;
      }
      return text; // Return original text if translation fails or times out
    } finally {
      setIsTranslating(false);
    }
  };

  const handleSendMessage = async (e: FormEvent) => {
    e.preventDefault();

    // If we're in edit mode, use the edited transcript
    // Otherwise, use the transcript if listening, or the input text
    const textToSend = isEditingTranscript ? editedTranscript : (isListening ? transcript : inputText);

    if (!textToSend || !textToSend.trim()) {
      return;
    }

    // Validate that the input text matches the selected language
    if (!validateLanguageMatch(textToSend, selectedLanguage)) {
      setLanguageError("Please select the proper language or type in the currently selected language.");
      return;
    }

    // Clear any previous language errors
    setLanguageError(null);

    // If we're in edit mode, exit edit mode
    if (isEditingTranscript) {
      setIsEditingTranscript(false);
    }

    if (isListening && typeof window !== 'undefined') {
      SpeechRecognition.stopListening();
      setIsListening(false); // Explicitly set to false
      setSpeaking(false); // Reset speaking state
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Close any open dropdowns/popups
    setShowLanguageMenu(false);
    setShowSuggestions(false);

    // Disable language buttons during query processing
    setLanguageButtonsDisabled(true);

    setIsLoading(true);

    const currentChatId = chatIdUrl || uuidv4();

    if (!chatIdUrl) {
      router.push(`/chat/${currentChatId}`);
    }

    const queryText = textToSend.trim();

    // Determine the language of the query
    const isTamil = isTamilText(queryText) || selectedLanguage === "Tamil";
    const isTelugu = isTeluguText(queryText) || selectedLanguage === "Telugu";
    const isKannada = isKannadaText(queryText) || selectedLanguage === "Kannada";

    // Store the original query for display purposes (commented out as it's not currently used)
    // const originalQuery = queryText;
    let translatedQuery = queryText;
    let needsTranslation = false;

    // Use the financial_query endpoint for all languages
    // For Tamil, Telugu, and Kannada, we'll translate the query and response
    const apiEndpoint = API_CONFIG.ACTIVE_ENDPOINT;

    // Extract continuous capital English words that should not be translated
    const capitalWordsMatches = queryText.match(/\b[A-Z]{2,}\b/g) || [];
    const capitalWords = capitalWordsMatches.map((word: string) => ({
      word,
      placeholder: `__CAPITAL_WORD_${Math.random().toString(36).substring(2, 10)}__`
    }));

    // Create a version of the query with placeholders for capital words
    let queryWithPlaceholders = queryText;
    capitalWords.forEach((item: { word: string, placeholder: string }) => {
      queryWithPlaceholders = queryWithPlaceholders.replace(item.word, item.placeholder);
    });

    // Fast parallel translation for non-English queries
    if (isTamil || isTelugu || isKannada) {
      needsTranslation = true;

      try {
        const sourceLang = isTamil ? "ta" : isTelugu ? "te" : "kn";
        const langName = isTamil ? "Tamil" : isTelugu ? "Telugu" : "Kannada";

        console.log(`🚀 Fast translating ${langName} query to English: "${queryWithPlaceholders.substring(0, 50)}..."`);

        // Use the optimized translation function
        translatedQuery = await translateText(queryWithPlaceholders, sourceLang, "en");

        // After translation, restore the capital words
        capitalWords.forEach(item => {
          translatedQuery = translatedQuery.replace(item.placeholder, item.word);
        });

        console.log(`✅ Query translation completed in ${langName}`);
      } catch (error) {
        console.error(`❌ Error translating ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} query:`, error);
        // Use original query if translation fails
        translatedQuery = queryText;
      }
    }

    console.log(`Query detected as ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : isKannada ? 'Kannada' : 'English'}, using endpoint: ${apiEndpoint}`);

    setInputText("");
    setUserQuery("");
    resetTranscript();
    setWordCount(0);
    setRecentWords([]);

    const userMessageTimestamp = new Date().toISOString();
    addMessage({
      isUser: true,
      text: queryText,
      timestamp: userMessageTimestamp
    }, currentChatId);

    const loadingMessageTimestamp = new Date().toISOString();
    const loadingMessageId = `loading-${currentChatId}-${Date.now()}`;

    addMessage({
      isUser: false,
      text: "__LOADING__",
      timestamp: loadingMessageTimestamp,
      messageId: loadingMessageId
    }, currentChatId);

    try {
      // Use the translated query for Telugu, otherwise use the original query
      const queryToSend = needsTranslation ? translatedQuery : queryText;

      // Get Pinecone API key and index name from localStorage if available
      const pineconeApiKey = typeof window !== 'undefined' ? localStorage.getItem('pinecone_api_key') : null;
      const pineconeIndexName = typeof window !== 'undefined' ? localStorage.getItem('pinecone_index_name') : null;

      // Prepare request body with all available data
      const requestBody: any = { query: queryToSend };

      // Add client email if available
      if (userEmail) {
        requestBody.client_email = userEmail;
        console.log(`Including user email in request: ${userEmail}`);
      }

      // Get available API keys from localStorage
      const storedApiKeys = typeof window !== 'undefined' ? localStorage.getItem('pineconeApiKeys') : null;
      let availableApiKeys: string[] = [];

      if (storedApiKeys) {
        try {
          availableApiKeys = JSON.parse(storedApiKeys);
          console.log(`📋 Found ${availableApiKeys.length} API keys in localStorage`);
        } catch (error) {
          console.error('❌ Error parsing stored API keys:', error);
        }
      }

      // Determine which index to use - REMOVE DEFAULT FALLBACK
      const indexToUse = selectedIndex || (typeof window !== 'undefined' ? localStorage.getItem('selectedPineconeIndex') : null);
      console.log(`🎯 Selected index from UI: ${selectedIndex}`);
      console.log(`💾 Stored index from localStorage: ${typeof window !== 'undefined' ? localStorage.getItem('selectedPineconeIndex') : null}`);
      console.log(`📌 Final index to use: ${indexToUse}`);

      // Find the correct API key for the selected index
      let apiKeyToUse = null;

      if (indexToUse && availableApiKeys.length > 0) {
        // Use the first available API key (each API key can access multiple indexes)
        apiKeyToUse = availableApiKeys[0];
        console.log(`🔑 Using API key for index ${indexToUse}: ${apiKeyToUse.substring(0, 10)}...`);
      } else if (pineconeApiKey && pineconeIndexName && !selectedIndex) {
        // Only use localStorage configuration if no index was specifically selected from UI
        apiKeyToUse = pineconeApiKey;
        console.log(`💾 Using localStorage API key for index ${pineconeIndexName}: ${apiKeyToUse.substring(0, 10)}...`);
      }

      // First try to use selected index if available
      if (indexToUse && apiKeyToUse) {
        requestBody.api_key = apiKeyToUse;
        requestBody.index_name = indexToUse;
        console.log(`✅ SENDING REQUEST WITH - Selected Index: "${indexToUse}", API Key: ${apiKeyToUse.substring(0, 10)}...`);
      }
      // If no index selected, fall back to default configuration
      else {
        const defaultConfig = await fetchPineCollection();
        if (defaultConfig.api_key && defaultConfig.index_name) {
          requestBody.api_key = defaultConfig.api_key;
          requestBody.index_name = defaultConfig.index_name;
          console.log(`🔁 FALLING BACK TO DEFAULT CONFIG - Index: "${defaultConfig.index_name}", API Key: ${defaultConfig.api_key.substring(0, 10)}...`);
        } else {
          console.error(`❌ MISSING CONFIGURATION - No index selected and default configuration unavailable`);
          throw new Error(`Missing Pinecone configuration. Please select an index or ensure default configuration is available.`);
        }
      }

      const response = await fetch(apiEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestBody),
      });

      if (!response.ok) {
        // Check if the error is related to the index
        if (response.status === 404) {
          const errorData = await response.json();
          if (errorData.error && errorData.error.includes("No matching documents found or index not available")) {
            throw new Error(`The selected index "${selectedIndex}" is not available or contains no relevant data. Please try another index.`);
          }
        }
        throw new Error(`API response error: ${response.status}`);
      }

      const data = await response.json();
      console.log("API Response received:", data);

      // Explicitly check for related_questions
      if (data.related_questions) {
        console.log("Found related_questions in API response:", data.related_questions);
      } else {
        console.warn("No related_questions found in API response");
      }

      // Check if the response includes a list of Pinecone indexes
      if (data.pinecone_indexes && Array.isArray(data.pinecone_indexes) && data.pinecone_indexes.length > 0) {
        console.log("Received Pinecone indexes from API:", data.pinecone_indexes);
        // Update the dropdown options with the available indexes
        setPineconeIndexes(data.pinecone_indexes);
      }

      let aiResponse = data.ai_response;
      console.log("Original AI Response:", aiResponse);

      // Fast parallel translation for non-English responses
      if ((isTamil || isTelugu || isKannada) && aiResponse) {
        try {
          const targetLang = isTamil ? "ta" : isTelugu ? "te" : "kn";
          const langName = isTamil ? "Tamil" : isTelugu ? "Telugu" : "Kannada";

          console.log(`🚀 Fast translating response from English to ${langName}: "${aiResponse.substring(0, 50)}..."`);

          // Use the optimized TranslationService for faster translation
          aiResponse = await TranslationService.translateWithCapitalWordsPreservation(aiResponse, "en", targetLang);

          console.log(`✅ Response translation completed to ${langName}`);
        } catch (error) {
          console.error(`❌ Error translating response to ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'}:`, error);
          // Keep original response if translation fails
        }
      }

      console.log("AI Response to display:", aiResponse);

      if (data.sentence_analysis && Array.isArray(data.sentence_analysis)) {
        console.log("Sentence analysis data:", data.sentence_analysis);

        // For Tamil, Telugu, or Kannada, we might want to translate the sentence analysis too
        if (isTamil || isTelugu || isKannada) {
          // In a real app, you would translate each sentence and summary
          // This is just a placeholder for the actual implementation
          console.log(`Would translate sentence analysis for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);

          // Example of how you might translate each item in a real implementation:
          // for (let i = 0; i < data.sentence_analysis.length; i++) {
          //   const item = data.sentence_analysis[i];
          //   if (item.summary) {
          //     item.summary = await translateText(item.summary, "en", isTamil ? "ta" : "te");
          //   }
          //   if (item.sentence) {
          //     item.sentence = await translateText(item.sentence, "en", isTamil ? "ta" : "te");
          //   }
          // }
        }

        data.sentence_analysis.forEach((item: { sentence: string; url: string; summary?: string }, index: number) => {
          const sentence = item.sentence;
          const url = item.url;
          const summary = item.summary || "";
          console.log(`Sentence ${index + 1}: ${sentence}`);
          console.log(`URL ${index + 1}: ${url}`);
          console.log(`Summary ${index + 1}: ${summary}`);
        });
      } else {
        console.log("No sentence_analysis data available.");
      }

      // Log related questions if available
      if (data.related_questions && Array.isArray(data.related_questions)) {
        console.log("Related questions data:", data.related_questions);

        // For Tamil, Telugu, or Kannada, we might want to translate the related questions too
        if (isTamil || isTelugu || isKannada) {
          console.log(`Would translate related questions for ${isTamil ? 'Tamil' : isTelugu ? 'Telugu' : 'Kannada'} here`);
        }

        data.related_questions.forEach((question: string, index: number) => {
          console.log(`Related Question ${index + 1}: ${question}`);
        });
      } else {
        console.log("No related_questions data available.");
      }

      if (aiResponse === undefined || aiResponse === null) {
        console.warn("API returned null/undefined ai_response");
        handleSubmit(queryText, currentChatId, "Sorry, I couldn't process your request properly.");
        return;
      }

      // Create a properly structured response object with all fields
      const responseObject = {
        ai_response: aiResponse,
        sentence_analysis: data.sentence_analysis || [],
        related_questions: data.related_questions || []
      };

      // Explicitly log the related_questions in the response object
      console.log("Related questions in response object:", responseObject.related_questions);
      console.log("Sending structured response to handleSubmit:", responseObject);
      handleSubmit(queryText, currentChatId, responseObject);
    } catch (error) {
      console.error("Error fetching AI response:", error);

      // Provide a more specific error message if it's related to the Pinecone index
      let errorMessage = "I'm sorry, I couldn't process your request at the moment. Please try again later.";

      if (error instanceof Error) {
        const errorText = error.message;

        // Check if the error is related to the Pinecone index
        if (errorText.includes("index") || errorText.includes("Index")) {
          errorMessage = errorText;
          console.log("Index-related error detected:", errorText);
        }
      }

      handleSubmit(queryText, currentChatId, errorMessage);
    } finally {
      setIsLoading(false);
      // Re-enable language buttons once the response is received or if there's an error
      setLanguageButtonsDisabled(false);
    }
  };

  // Initialize client-side values after mount to prevent hydration errors
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Initialize selectedIndex from localStorage
      const savedIndex = localStorage.getItem('selectedPineconeIndex');
      if (savedIndex) {
        setSelectedIndex(savedIndex);
      }

      // Update state with client-side values
      setTranscript(clientTranscript);
      setListening(clientListening);
      setBrowserSupportsSpeechRecognition(clientBrowserSupport);
      setIsMicrophoneAvailable(clientMicrophoneAvailable);

      console.log("SpeechRecognition API check:", {
        webkitSpeechRecognition: 'webkitSpeechRecognition' in window,
        SpeechRecognition: 'SpeechRecognition' in window,
        mozSpeechRecognition: 'mozSpeechRecognition' in window,
        msSpeechRecognition: 'msSpeechRecognition' in window,
        clientBrowserSupport
      });

      // Fetch PINE collection data when component mounts
      const fetchAndLogPineData = async () => {
        console.log("Fetching PINE collection data...");
        const pineData = await fetchPineCollection();
        if (pineData) {
          console.log("PINE collection data fetched successfully:", pineData);
        } else {
          console.log("No PINE collection data available for this user - using default configuration");
        }
      };

      fetchAndLogPineData();
    }
  }, [clientBrowserSupport, clientMicrophoneAvailable, clientListening, clientTranscript]);

  // Get user email from localStorage and set up API environment
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Get the logged-in user's email from localStorage
      const email = localStorage.getItem('user_email');
      setUserEmail(email);

      // Check if we should use the development environment
      const useDevEnv = localStorage.getItem('use_dev_environment');
      if (useDevEnv === 'true') {
        console.log('Using development API endpoint:', API_CONFIG.DEV_ENDPOINT);
        API_CONFIG.ACTIVE_ENDPOINT = API_CONFIG.DEV_ENDPOINT;
        setApiEnvironment('development');
      } else {
        console.log('Using production API endpoint:', API_CONFIG.PROD_ENDPOINT);
        API_CONFIG.ACTIVE_ENDPOINT = API_CONFIG.PROD_ENDPOINT;
        setApiEnvironment('production');
      }
    }
  }, []);

  // Check for browser support and microphone availability
  useEffect(() => {
    if (!browserSupportsSpeechRecognition && typeof window !== 'undefined') {
      console.warn("Browser doesn't support speech recognition.");
    }

    if (!isMicrophoneAvailable && typeof window !== 'undefined') {
      console.warn("Microphone is not available.");
    }

    // Cleanup function to ensure microphone is stopped when component unmounts
    return () => {
      if (isListening && typeof window !== 'undefined') {
        console.log("Component unmounting, stopping speech recognition");
        SpeechRecognition.stopListening();
      }

      // Clear any timers
      if (speakingTimerRef.current) {
        clearTimeout(speakingTimerRef.current);
        speakingTimerRef.current = null;
      }
    };
  }, [browserSupportsSpeechRecognition, isMicrophoneAvailable, isListening]);

  // Function to log speech recognition state for debugging - uncomment the debug button below to use
  // const logSpeechRecognitionState = () => {
  //   console.group("Speech Recognition Debug Info");
  //   console.log("Browser supports speech recognition:", browserSupportsSpeechRecognition);
  //   console.log("Microphone available:", isMicrophoneAvailable);
  //   console.log("Current listening state (from hook):", listening);
  //   console.log("Current isListening state (component):", isListening);
  //   console.log("Current transcript:", transcript);
  //   console.log("Selected language:", selectedLanguage);
  //   console.log("Language code:", getLanguageCode());
  //   console.groupEnd();
  // };

  // Generate sound wave animation elements
  const renderSoundWave = () => {
    // Use different colors based on selected language
    let waveColor = "bg-red-500"; // Default color

    if (selectedLanguage === "Tamil") {
      waveColor = "bg-purple-500";
    } else if (selectedLanguage === "Telugu") {
      waveColor = "bg-green-500";
    } else if (selectedLanguage === "Kannada") {
      waveColor = "bg-orange-500";
    }

    return (
      <div className="flex items-center gap-[2px] h-5">
        {Array.from({ length: 5 }).map((_, i) => {
          const isActive = speaking || (i === 2); // Middle bar always active when not speaking
          const delay = i * 0.1;
          const duration = isActive ? (0.5 + (i * 0.1)) : 1; // Use deterministic duration
          const height = isActive ?
            (i % 2 === 0 ? 10 + (i * 2) : 6 + (i * 1.5)) : // Use deterministic height based on index
            (i === 2 ? 6 : 4);

          return (
            <div
              key={`wave-${i}`} // Use stable key
              className={`w-1 ${waveColor} rounded-full transition-all`}
              style={{
                height: `${height}px`,
                animationName: isActive ? 'soundWavePulse' : 'none',
                animationDuration: `${duration}s`,
                animationIterationCount: 'infinite',
                animationDirection: 'alternate',
                animationDelay: `${delay}s`,
                opacity: isActive ? 1 : 0.5
              }}
            ></div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="w-full max-w-[1070px] mx-auto px-2 sm:px-4 md:px-6">
      <style jsx global>{`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(-5px); }
          to { opacity: 1; transform: translateY(0); }
        }
        .animate-fadeIn {
          animation: fadeIn 0.3s ease-out forwards;
        }
      `}</style>
      {/* Pinecone Index Selector */}


      {isListening && (
        <div className={`mb-4 p-3 rounded-lg flex items-center justify-between shadow-sm relative z-40
          ${selectedLanguage === "Tamil"
            ? "bg-gradient-to-r from-purple-50 to-purple-50/70 border border-purple-200"
            : selectedLanguage === "Telugu"
              ? "bg-gradient-to-r from-green-50 to-green-50/70 border border-green-200"
              : selectedLanguage === "Kannada"
                ? "bg-gradient-to-r from-orange-50 to-orange-50/70 border border-orange-200"
                : "bg-gradient-to-r from-red-50 to-red-50/70 border border-red-200"}`}>
          <div className="flex items-center gap-3">
            <div className="flex items-center bg-white p-1 rounded-full shadow-sm">
              {renderSoundWave()}
            </div>
            <div>
              <span className={`text-sm font-medium ${
                selectedLanguage === "Tamil"
                  ? "text-purple-700"
                  : selectedLanguage === "Telugu"
                    ? "text-green-700"
                    : selectedLanguage === "Kannada"
                      ? "text-orange-700"
                      : "text-red-700"
              }`}>
                {selectedLanguage === "Tamil"
                  ? "குரல் பதிவு செயலில் உள்ளது"
                  : selectedLanguage === "Telugu"
                    ? "వాయిస్ రికార్డింగ్ యాక్టివ్"
                    : selectedLanguage === "Kannada"
                      ? "ಧ್ವನಿ ರೆಕಾರ್ಡಿಂಗ್ ಸಕ್ರಿಯವಾಗಿದೆ"
                      : "Voice recording active in"} <strong>{selectedLanguage}</strong>
              </span>
              {wordCount > 0 && (
                <div className={`text-xs mt-0.5 ${
                  selectedLanguage === "Tamil"
                    ? "text-purple-500/80"
                    : selectedLanguage === "Telugu"
                      ? "text-green-500/80"
                      : selectedLanguage === "Kannada"
                        ? "text-orange-500/80"
                        : "text-red-500/80"
                }`}>
                  {selectedLanguage === "Tamil"
                    ? `இதுவரை ${wordCount} சொற்கள் பதிவு செய்யப்பட்டுள்ளன`
                    : selectedLanguage === "Telugu"
                      ? `ఇప్పటివరకు ${wordCount} పదాలు క్యాప్చర్ చేయబడ్డాయి`
                      : selectedLanguage === "Kannada"
                        ? `ಇಲ್ಲಿಯವರೆಗೆ ${wordCount} ಪದಗಳನ್ನು ಸೆರೆಹಿಡಿಯಲಾಗಿದೆ`
                        : `Captured ${wordCount} ${wordCount === 1 ? 'word' : 'words'} so far`}
                </div>
              )}
            </div>
          </div>
          <button
            onClick={() => {
              toggleListening();
              // Additional cleanup to ensure UI is reset
              setIsListening(false);
              resetTranscript();
              setSpeaking(false);
            }}
            className={`text-xs px-3 py-1.5 bg-white rounded-full transition-colors shadow-sm hover:shadow flex items-center gap-1.5
              ${selectedLanguage === "Tamil"
                ? "border border-purple-300 text-purple-600 hover:bg-purple-100"
                : selectedLanguage === "Telugu"
                  ? "border border-green-300 text-green-600 hover:bg-green-100"
                  : selectedLanguage === "Kannada"
                    ? "border border-orange-300 text-orange-600 hover:bg-orange-100"
                    : "border border-red-300 text-red-600 hover:bg-red-100"}`}
          >
            <PiStop className={
              selectedLanguage === "Tamil"
                ? "text-purple-600"
                : selectedLanguage === "Telugu"
                  ? "text-green-600"
                  : selectedLanguage === "Kannada"
                    ? "text-orange-600"
                    : "text-red-600"
            } />
            <span>{
              selectedLanguage === "Tamil"
                ? "பதிவை நிறுத்து"
                : selectedLanguage === "Telugu"
                  ? "రికార్డింగ్ ఆపివేయి"
                  : selectedLanguage === "Kannada"
                    ? "ರೆಕಾರ್ಡಿಂಗ್ ನಿಲ್ಲಿಸಿ"
                    : "Stop Recording"
            }</span>
          </button>
        </div>
      )}
      {/* Index Indicator - Shows which index is currently selected */}
      <div className="w-full flex justify-end mb-2">
        <div className="px-2 py-0.5 bg-blue-50 dark:bg-blue-900/20 rounded-md text-xs font-medium text-gray-600 dark:text-gray-400 border border-blue-200 dark:border-blue-800/50 shadow-sm flex items-center gap-1">
          <span>Category:</span>
          <span className="text-blue-600 dark:text-blue-400 font-semibold">
            {selectedIndex || "Financial News"}
          </span>
        </div>
      </div>

      <form
        onSubmit={handleSendMessage}
        className="w-full bg-primaryColor/5 p-2 sm:p-3 lg:p-4 rounded-xl border border-primaryColor/20"
      >
        {/* Language validation error message */}
        {languageError && (
          <div className="mb-3 p-3 bg-red-50 border border-red-200 rounded-lg text-red-600 text-sm flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {languageError}
          </div>
        )}
        <div className="w-full bg-white rounded-lg max-lg:text-sm block dark:bg-n0 relative z-30">
          {isListening || transcript ? (
            <div className="w-full p-4 min-h-[56px] max-h-[200px] overflow-y-auto relative flex flex-col">
              {isEditingTranscript ? (
                // Editable textarea for transcript
                <div className="flex-grow relative">
                  <textarea
                    ref={editableTranscriptRef}
                    className="w-full h-full min-h-[80px] p-2 border border-primaryColor/30 rounded-md focus:outline-none focus:ring-2 focus:ring-primaryColor/50 text-gray-800 resize-none"
                    value={editedTranscript}
                    onChange={handleTranscriptChange}
                    placeholder="Edit your transcribed text here..."
                  />
                  <button
                    onClick={toggleTranscriptEditMode}
                    className={`absolute top-2 right-2 p-1.5 rounded-full transition-colors
                      ${selectedLanguage === "Tamil"
                        ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                        : selectedLanguage === "Telugu"
                          ? 'bg-green-100 text-green-600 hover:bg-green-200'
                          : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                      }`}
                    title="Save edits"
                  >
                    <PiCheck className="text-lg" />
                  </button>
                </div>
              ) : (
                // View mode for transcript
                <div
                  ref={transcriptRef}
                  className="flex-grow text-gray-800 dark:text-white break-words relative"
                >
                  {transcript && transcript.trim() !== "" ? (
                    // Show transcript with animated cursor
                    <div className="relative">
                      <p className="m-0 leading-relaxed">
                        {transcript ? transcript : ""}
                        {speaking && (
                          <span className="inline-block w-1 h-4 bg-primaryColor ml-1"
                            style={{
                              animationName: 'pulse',
                              animationDuration: '1s',
                              animationIterationCount: 'infinite',
                              animationDirection: 'alternate'
                            }}
                          ></span>
                        )}
                      </p>

                      {/* Action buttons - only show when not actively listening */}
                      {!isListening && transcript && transcript.trim() !== "" && (
                        <div className="absolute top-0 right-0 flex gap-1">
                          <button
                            onClick={() => {
                              setTranscript("");
                              setInputText("");
                              setUserQuery("");
                              resetTranscript();
                              setWordCount(0);
                              setRecentWords([]);
                            }}
                            className={`p-1.5 rounded-full transition-colors
                              ${selectedLanguage === "Tamil"
                                ? 'bg-red-100 text-red-600 hover:bg-red-200'
                                : selectedLanguage === "Telugu"
                                  ? 'bg-red-100 text-red-600 hover:bg-red-200'
                                  : 'bg-red-100 text-red-600 hover:bg-red-200'
                              }`}
                            title="Clear transcript"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                          </button>
                          <button
                            onClick={toggleTranscriptEditMode}
                            className={`p-1.5 rounded-full transition-colors
                              ${selectedLanguage === "Tamil"
                                ? 'bg-purple-100 text-purple-600 hover:bg-purple-200'
                                : selectedLanguage === "Telugu"
                                  ? 'bg-green-100 text-green-600 hover:bg-green-200'
                                  : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                              }`}
                            title="Edit transcript"
                          >
                            <PiPencilSimple className="text-lg" />
                          </button>
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-gray-400 italic">
                      <span>
                        {selectedLanguage === "Tamil"
                          ? "தமிழில் கேட்கிறது... இப்போது பேசவும்"
                          : selectedLanguage === "Telugu"
                            ? "తెలుగులో వింటున్నాము... ఇప్పుడు మాట్లాడండి"
                            : selectedLanguage === "Kannada"
                              ? "ಕನ್ನಡದಲ್ಲಿ ಆಲಿಸುತ್ತಿದ್ದೇವೆ... ಈಗ ಮಾತನಾಡಿ"
                              : `Listening in ${selectedLanguage}... Speak now`}
                      </span>
                      <div className="flex space-x-1 ml-2">
                        {[0, 1, 2].map((i) => (
                          <div
                            key={`dot-${i}`} /* Use stable key */
                            className={`h-2 w-2 rounded-full ${
                              selectedLanguage === "Tamil"
                                ? "bg-purple-500"
                                : selectedLanguage === "Telugu"
                                  ? "bg-green-500"
                                  : selectedLanguage === "Kannada"
                                    ? "bg-orange-500"
                                    : "bg-red-500"
                            }`}
                            style={{
                              animationName: 'pulse',
                              animationDuration: '1s',
                              animationIterationCount: 'infinite',
                              animationDirection: 'alternate',
                              animationDelay: `${i * 0.2}s`
                            }}
                          ></div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Speaking indicator */}
              {(transcript && transcript.trim() !== "") && (
                <div className="mt-2 flex items-center justify-between text-xs text-gray-500 border-t pt-2 border-gray-100">
                  <div className="flex items-center gap-2">
                    {isEditingTranscript ? (
                      <span className="text-blue-500 flex items-center gap-1">
                        <PiPencilSimple />
                        {selectedLanguage === "Tamil"
                          ? "திருத்துகிறது"
                          : selectedLanguage === "Telugu"
                            ? "సవరిస్తోంది"
                            : selectedLanguage === "Kannada"
                              ? "ಸಂಪಾದಿಸುತ್ತಿದೆ"
                              : "Editing"}
                      </span>
                    ) : speaking ? (
                      <span className="text-green-500 flex items-center gap-1">
                        <PiWaveform className="animate-pulse" />
                        {selectedLanguage === "Tamil"
                          ? "செயலில்"
                          : selectedLanguage === "Telugu"
                            ? "యాక్టివ్"
                            : selectedLanguage === "Kannada"
                              ? "ಸಕ್ರಿಯ"
                              : "Active"}
                      </span>
                    ) : (
                      <span className="text-gray-400 flex items-center gap-1">
                        <PiWaveform />
                        {selectedLanguage === "Tamil"
                          ? "இடைநிறுத்தப்பட்டது"
                          : selectedLanguage === "Telugu"
                            ? "నిలిపివేయబడింది"
                            : selectedLanguage === "Kannada"
                              ? "ವಿರಾಮಗೊಳಿಸಲಾಗಿದೆ"
                              : "Paused"}
                      </span>
                    )}
                  </div>
                  <span className="bg-gray-100 px-2 py-1 rounded-full text-xs">
                    {selectedLanguage === "Tamil"
                      ? `${wordCount} சொற்கள்`
                      : selectedLanguage === "Telugu"
                        ? `${wordCount} పదాలు`
                        : selectedLanguage === "Kannada"
                          ? `${wordCount} ಪದಗಳು`
                          : `${wordCount} words`}
                  </span>
                </div>
              )}
            </div>
          ) : (
            <input
              className="w-full outline-none p-4 bg-transparent relative z-30"
              placeholder={
                selectedLanguage === "Tamil"
                  ? "நிதி தொடர்பான கேள்வியை தமிழில் கேளுங்கள்..."
                  : selectedLanguage === "Telugu"
                    ? "ఒక ఆర్థిక విషయంపై నేను ఒక ప్రశ్నను అడగదలుచుకున్నాను..."
                    : selectedLanguage === "Kannada"
                      ? "ಹಣಕಾಸು ವಿಷಯದ ಬಗ್ಗೆ ಪ್ರಶ್ನೆಯನ್ನು ಕೇಳಿ..."
                      : "ask question based financial topic.."
              }
              value={inputText}
              onChange={(e) => {
                setUserQuery(e.target.value);
                setInputText(e.target.value);
                // Clear any language error when the user starts typing
                if (languageError) {
                  setLanguageError(null);
                }
              }}
              disabled={isLoading}
              onClick={() => {
                // Close any open dropdowns when clicking in the input field
                // No longer need to close language dropdown since we're using horizontal buttons
                // setShowLanguageDropdown(false);
                setShowSuggestions(false);
              }}
            />
          )}
        </div>

        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-0 pt-3 sm:pt-4">
          <div className="flex justify-start items-center gap-3 relative">
            <button
              type="button"
              onClick={() => setShowSuggestions(!showSuggestions)}
              className={`bg-white px-3 py-2 rounded-lg flex items-center gap-2 border
                ${showSuggestions ? 'border-primaryColor bg-primaryColor/5' : 'border-primaryColor/20'}
                hover:bg-primaryColor/5 hover:border-primaryColor transition-all shadow-sm hover:shadow dark:bg-n0
                text-sm font-medium
                ${selectedLanguage === "Tamil"
                  ? 'text-purple-700'
                  : selectedLanguage === "Telugu"
                    ? 'text-green-700'
                    : selectedLanguage === "Kannada"
                      ? 'text-orange-700'
                      : 'text-gray-700'}
                dark:text-gray-300`}>
              <PiLightbulb className={`${showSuggestions
                ? 'text-primaryColor'
                : selectedLanguage === "Tamil"
                  ? 'text-purple-500'
                  : selectedLanguage === "Telugu"
                    ? 'text-green-500'
                    : selectedLanguage === "Kannada"
                      ? 'text-orange-500'
                      : 'text-amber-500'}`} />
              <span>{
                selectedLanguage === "Tamil"
                  ? 'பரிந்துரைகள்'
                  : selectedLanguage === "Telugu"
                    ? 'సూచనలు'
                    : selectedLanguage === "Kannada"
                      ? 'ಸಲಹೆಗಳು'
                      : 'Suggestions'
              }</span>
              <PiSparkle className={`${showSuggestions
                ? 'text-primaryColor'
                : selectedLanguage === "Tamil"
                  ? 'text-purple-400'
                  : selectedLanguage === "Telugu"
                    ? 'text-green-400'
                    : selectedLanguage === "Kannada"
                      ? 'text-orange-400'
                      : 'text-amber-400'}
                ${!showSuggestions ? 'animate-pulse' : ''}`} />
            </button>

            {/* Suggestions popup with professional styling and animations */}
            {showSuggestions && (
              <div
                className={`fixed inset-0 flex items-center justify-center z-50 animate-fadeIn ${
                  selectedLanguage === "Tamil"
                    ? 'bg-gradient-to-br from-purple-900/30 to-black/40'
                    : selectedLanguage === "Telugu"
                      ? 'bg-gradient-to-br from-green-900/30 to-black/40'
                      : selectedLanguage === "Kannada"
                        ? 'bg-gradient-to-br from-orange-900/30 to-black/40'
                        : 'bg-gradient-to-br from-blue-900/30 to-black/40'
                }`}
                onClick={() => setShowSuggestions(false)}
                style={{ backdropFilter: 'blur(3px)', animationDuration: '0.2s' }}
              >
                <div
                  ref={suggestionsRef}
                  className={`rounded-xl shadow-2xl border max-w-2xl w-full mx-4 max-h-[80vh] overflow-hidden animate-scaleIn ${
                    selectedLanguage === "Tamil"
                      ? 'border-purple-200/50 bg-gradient-to-b from-white to-purple-50/30'
                      : selectedLanguage === "Telugu"
                        ? 'border-green-200/50 bg-gradient-to-b from-white to-green-50/30'
                        : selectedLanguage === "Kannada"
                          ? 'border-orange-200/50 bg-gradient-to-b from-white to-orange-50/30'
                          : 'border-blue-200/50 bg-gradient-to-b from-white to-blue-50/30'
                  }`}
                  onClick={(e) => e.stopPropagation()}
                  style={{
                    animationDuration: '0.3s',
                    boxShadow: '0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.05)'
                  }}
                >
                  <div className={`p-5 border-b flex justify-between items-center rounded-t-xl ${
                    selectedLanguage === "Tamil"
                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'
                      : selectedLanguage === "Telugu"
                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'
                        : selectedLanguage === "Kannada"
                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'
                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'
                  }`}>
                    <h3 className="text-lg font-semibold flex items-center gap-2">
                      <div className={`p-2 rounded-full ${
                        selectedLanguage === "Tamil"
                          ? 'bg-purple-100'
                          : selectedLanguage === "Telugu"
                            ? 'bg-green-100'
                            : selectedLanguage === "Kannada"
                              ? 'bg-orange-100'
                              : 'bg-blue-100'
                      }`}>
                        <PiLightbulb className={`text-xl ${
                          selectedLanguage === "Tamil"
                            ? 'text-purple-500'
                            : selectedLanguage === "Telugu"
                              ? 'text-green-500'
                              : selectedLanguage === "Kannada"
                                ? 'text-orange-500'
                                : 'text-blue-500'
                        }`} />
                      </div>
                      <span className={
                        selectedLanguage === "Tamil"
                          ? 'text-purple-800'
                          : selectedLanguage === "Telugu"
                            ? 'text-green-800'
                            : selectedLanguage === "Kannada"
                              ? 'text-orange-800'
                              : 'text-blue-800'
                      }>
                        {selectedLanguage === "Tamil"
                          ? 'பரிந்துரைக்கப்பட்ட நிதி கேள்விகள்'
                          : selectedLanguage === "Telugu"
                            ? 'సిఫార్సు చేయబడిన ఆర్థిక ప్రశ్నలు'
                            : selectedLanguage === "Kannada"
                              ? 'ಶಿಫಾರಸು ಮಾಡಲಾದ ಹಣಕಾಸು ಪ್ರಶ್ನೆಗಳು'
                              : 'Recommended Financial Questions'}
                      </span>
                    </h3>
                    <button
                      onClick={() => setShowSuggestions(false)}
                      className={`p-2 rounded-full transition-colors ${
                        selectedLanguage === "Tamil"
                          ? 'bg-purple-100/50 text-purple-500 hover:bg-purple-200 hover:text-purple-700'
                          : selectedLanguage === "Telugu"
                            ? 'bg-green-100/50 text-green-500 hover:bg-green-200 hover:text-green-700'
                            : selectedLanguage === "Kannada"
                              ? 'bg-orange-100/50 text-orange-500 hover:bg-orange-200 hover:text-orange-700'
                              : 'bg-blue-100/50 text-blue-500 hover:bg-blue-200 hover:text-blue-700'
                      }`}
                      aria-label="Close"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    </button>
                  </div>

                  <div className="p-5 overflow-y-auto max-h-[60vh]">
                    <div className={`rounded-lg p-3 mb-5 ${
                      selectedLanguage === "Tamil"
                        ? 'bg-gradient-to-r from-purple-50 to-white border border-purple-100'
                        : selectedLanguage === "Telugu"
                          ? 'bg-gradient-to-r from-green-50 to-white border border-green-100'
                          : selectedLanguage === "Kannada"
                            ? 'bg-gradient-to-r from-orange-50 to-white border border-orange-100'
                            : 'bg-gradient-to-r from-blue-50 to-white border border-blue-100'
                    }`}>
                      <p className={`text-sm font-medium ${
                        selectedLanguage === "Tamil"
                          ? 'text-purple-700'
                          : selectedLanguage === "Telugu"
                            ? 'text-green-700'
                            : selectedLanguage === "Kannada"
                              ? 'text-orange-700'
                              : 'text-blue-700'
                      }`}>
                        {selectedLanguage === "Tamil"
                          ? 'உங்கள் நிதி தேவைகளுக்கான பரிந்துரைக்கப்பட்ட கேள்விகளைப் பார்க்கவும்'
                          : selectedLanguage === "Telugu"
                            ? 'మీ ఆర్థిక అవసరాలకు సిఫార్సు చేయబడిన ప్రశ్నలను చూడండి'
                            : selectedLanguage === "Kannada"
                              ? 'ನಿಮ್ಮ ಹಣಕಾಸು ಅಗತ್ಯಗಳಿಗಾಗಿ ಈ ಶಿಫಾರಸು ಮಾಡಲಾದ ಪ್ರಶ್ನೆಗಳನ್ನು ಆಯ್ಕೆಮಾಡಿ'
                              : 'Select from these recommended questions for your financial needs'}
                      </p>
                    </div>

                    <div className="space-y-3">
                      {recommendedSuggestions.map((suggestion, index) => (
                        <button
                          key={index}
                          onClick={() => {
                            handleSelectSuggestion(suggestion);
                            setShowSuggestions(false);
                          }}
                          className={`w-full text-left px-5 py-4 text-sm rounded-lg border shadow-sm hover:shadow-md transition-all
                            ${selectedLanguage === "Tamil"
                              ? 'border-purple-200 bg-white hover:bg-gradient-to-r hover:from-purple-50 hover:to-white hover:border-purple-300'
                              : selectedLanguage === "Telugu"
                                ? 'border-green-200 bg-white hover:bg-gradient-to-r hover:from-green-50 hover:to-white hover:border-green-300'
                                : selectedLanguage === "Kannada"
                                  ? 'border-orange-200 bg-white hover:bg-gradient-to-r hover:from-orange-50 hover:to-white hover:border-orange-300'
                                  : 'border-blue-200 bg-white hover:bg-gradient-to-r hover:from-blue-50 hover:to-white hover:border-blue-300'}
                            text-gray-700 animate-fadeInUp`}
                          style={{ animationDelay: `${index * 0.05}s`, animationDuration: '0.3s' }}
                        >
                          <div className="flex items-start">
                            <span className={`inline-block p-1.5 rounded-full mr-3 mt-0.5 ${
                              selectedLanguage === "Tamil"
                                ? 'bg-gradient-to-br from-purple-100 to-purple-200 text-purple-600'
                                : selectedLanguage === "Telugu"
                                  ? 'bg-gradient-to-br from-green-100 to-green-200 text-green-600'
                                  : selectedLanguage === "Kannada"
                                    ? 'bg-gradient-to-br from-orange-100 to-orange-200 text-orange-600'
                                    : 'bg-gradient-to-br from-blue-100 to-blue-200 text-blue-600'
                            }`}>
                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                              </svg>
                            </span>
                            <span className="font-medium">{suggestion}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>

                  <div className={`p-4 border-t rounded-b-xl text-right ${
                    selectedLanguage === "Tamil"
                      ? 'bg-gradient-to-r from-purple-50 to-purple-100 border-purple-100'
                      : selectedLanguage === "Telugu"
                        ? 'bg-gradient-to-r from-green-50 to-green-100 border-green-100'
                        : selectedLanguage === "Kannada"
                          ? 'bg-gradient-to-r from-orange-50 to-orange-100 border-orange-100'
                          : 'bg-gradient-to-r from-blue-50 to-blue-100 border-blue-100'
                  }`}>
                    <button
                      onClick={() => setShowSuggestions(false)}
                      className={`px-5 py-2 rounded-lg text-white font-medium shadow-sm transition-all hover:shadow-md
                        ${selectedLanguage === "Tamil"
                          ? 'bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700'
                          : selectedLanguage === "Telugu"
                            ? 'bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700'
                            : selectedLanguage === "Kannada"
                              ? 'bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700'
                              : 'bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700'}`}
                    >
                      {selectedLanguage === "Tamil"
                        ? 'மூடு'
                        : selectedLanguage === "Telugu"
                          ? 'మూసివేయండి'
                          : selectedLanguage === "Kannada"
                            ? 'ಮುಚ್ಚಿರಿ'
                            : 'Close'}
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>
          <div className="flex justify-between items-center gap-4">
            {/* Language selection - responsive design */}
            <div className="flex-grow">
              {/* Desktop view - horizontal buttons */}
              <div className="hidden md:flex md:items-center">
                {/* <div className="flex items-center mr-4">
                  <PiGlobe className="text-gray-600 mr-2 text-lg" />
                  <span className="text-sm font-medium text-gray-700">Select Language:</span>
                </div> */}

                <div className="flex space-x-4">
                  {/* Show English, Tamil, and Telugu buttons as prominent language selectors on desktop */}
                  {languages.map((language, index) => {
                    const isSelected = selectedLanguage === language.name;

                    // Get color classes based on language
                    const getColorClass = () => {
                      if (language.name === "Tamil") return "text-purple-700 border-purple-300 bg-purple-50";
                      if (language.name === "Telugu") return "text-green-700 border-green-300 bg-green-50";
                      if (language.name === "Kannada") return "text-orange-700 border-orange-300 bg-orange-50";
                      return "text-blue-700 border-blue-300 bg-blue-50";
                    };

                    const getHoverClass = () => {
                      if (language.name === "Tamil") return "hover:bg-purple-100 hover:border-purple-400";
                      if (language.name === "Telugu") return "hover:bg-green-100 hover:border-green-400";
                      if (language.name === "Kannada") return "hover:bg-orange-100 hover:border-orange-400";
                      return "hover:bg-blue-100 hover:border-blue-400";
                    };

                    const getIconColor = () => {
                      if (language.name === "Tamil") return "text-purple-600";
                      if (language.name === "Telugu") return "text-green-600";
                      if (language.name === "Kannada") return "text-orange-600";
                      return "text-blue-600";
                    };

                    const getBorderColor = () => {
                      if (language.name === "Tamil") return "bg-purple-500";
                      if (language.name === "Telugu") return "bg-green-500";
                      if (language.name === "Kannada") return "bg-orange-500";
                      return "bg-blue-500";
                    };

                    // Using type="button" to prevent form submission when clicking language buttons
                    return (
                      <button
                        key={index}
                        type="button"
                        onClick={(e) => handleSelectLanguage(e, language.name)}
                        disabled={languageButtonsDisabled || isLoading}
                        className={`px-5 py-2 rounded-lg text-sm font-medium transition-all border relative
                          ${isSelected
                            ? getColorClass()
                            : 'bg-white text-gray-600 border-gray-200 ' + getHoverClass()}
                          transform transition-all duration-300 ease-in-out
                          ${isSelected ? 'scale-105 shadow-md' : 'hover:scale-105 hover:shadow-sm'}
                          ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}
                          animate-fadeIn
                        `}
                        style={{
                          animationDelay: `${index * 0.05}s`,
                          animationDuration: '0.3s'
                        }}
                      >
                        <div className="flex items-center gap-2">
                          <PiGlobe className={`${getIconColor()} text-lg ${isSelected ? 'animate-fadeIn' : ''}`}
                            style={{ animationDuration: '0.3s' }}
                          />
                          <span className="font-medium">{language.name}</span>
                        </div>

                        {/* Bottom border animation for selected language */}
                        {isSelected && (
                          <div
                            className={`absolute bottom-0 left-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}
                            style={{
                              width: '100%',
                              transformOrigin: 'left',
                              animationDuration: '0.4s'
                            }}
                          ></div>
                        )}

                        {/* Top border animation for selected language */}
                        {isSelected && (
                          <div
                            className={`absolute top-0 right-0 h-0.5 rounded-full animate-scaleInHorizontal ${getBorderColor()}`}
                            style={{
                              width: '100%',
                              transformOrigin: 'right',
                              animationDuration: '0.4s',
                              animationDelay: '0.1s'
                            }}
                          ></div>
                        )}
                      </button>
                    );
                  })}
                </div>
              </div>

              {/* Mobile view - dropup menu */}
              <div className="md:hidden relative" ref={languageMenuRef}>
                <button
                  type="button"
                  onClick={() => setShowLanguageMenu(!showLanguageMenu)}
                  disabled={languageButtonsDisabled || isLoading}
                  className={`flex items-center gap-2 px-4 py-2 rounded-lg border transition-all
                    ${selectedLanguage === "Tamil"
                      ? "text-purple-700 border-purple-300 bg-purple-50"
                      : selectedLanguage === "Telugu"
                        ? "text-green-700 border-green-300 bg-green-50"
                        : selectedLanguage === "Kannada"
                          ? "text-orange-700 border-orange-300 bg-orange-50"
                          : "text-blue-700 border-blue-300 bg-blue-50"
                    }
                    hover:shadow-sm
                    ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  <PiGlobe className={`text-lg ${
                    selectedLanguage === "Tamil"
                      ? "text-purple-600"
                      : selectedLanguage === "Telugu"
                        ? "text-green-600"
                        : selectedLanguage === "Kannada"
                          ? "text-orange-600"
                          : "text-blue-600"
                  }`} />
                  <span className="font-medium text-sm">{selectedLanguage}</span>
                  <span className={`transition-transform duration-300 ${showLanguageMenu ? 'rotate-180' : ''}`}>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
                    </svg>
                  </span>
                </button>

                {/* Dropup menu */}
                {showLanguageMenu && (
                  <div className="absolute bottom-full left-0 mb-1 w-full bg-white dark:bg-n0 rounded-lg border shadow-lg z-50 overflow-hidden animate-fadeIn"
                    style={{ animationDuration: '0.2s' }}
                  >
                    {languages.map((language, index) => {
                      const isSelected = selectedLanguage === language.name;

                      // Get color classes based on language
                      const getItemColorClass = () => {
                        if (language.name === "Tamil") return isSelected ? "bg-purple-50 text-purple-700" : "hover:bg-purple-50";
                        if (language.name === "Telugu") return isSelected ? "bg-green-50 text-green-700" : "hover:bg-green-50";
                        if (language.name === "Kannada") return isSelected ? "bg-orange-50 text-orange-700" : "hover:bg-orange-50";
                        return isSelected ? "bg-blue-50 text-blue-700" : "hover:bg-blue-50";
                      };

                      const getIconColor = () => {
                        if (language.name === "Tamil") return "text-purple-600";
                        if (language.name === "Telugu") return "text-green-600";
                        if (language.name === "Kannada") return "text-orange-600";
                        return "text-blue-600";
                      };

                      return (
                        <button
                          key={index}
                          type="button"
                          onClick={(e) => {
                            handleSelectLanguage(e, language.name);
                            setShowLanguageMenu(false);
                          }}
                          disabled={languageButtonsDisabled || isLoading}
                          className={`w-full flex items-center gap-2 px-4 py-3 text-left text-sm ${getItemColorClass()} transition-colors
                            ${(languageButtonsDisabled || isLoading) ? 'opacity-50 cursor-not-allowed' : ''}
                          `}
                          style={{
                            animationDelay: `${index * 0.05}s`,
                          }}
                        >
                          <PiGlobe className={`${getIconColor()} text-lg`} />
                          <span className="font-medium">{language.name}</span>
                          {isSelected && (
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          )}
                        </button>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>

            {/* Action buttons container */}
            <div className="flex items-center gap-3">
              {/* Microphone button */}
              <div className="relative z-50"> {/* Added z-50 to ensure it's above other elements */}
                {browserSupportsSpeechRecognition ? (
                  <button
                    type="button"
                    onClick={isListening ? toggleListening : toggleListening}
                    className={`p-2.5 rounded-full flex justify-center items-center border transition-all shadow-sm relative z-50
                      ${isListening
                        ? 'border-red-500 bg-gradient-to-r from-red-50 to-red-100 hover:from-red-100 hover:to-red-200'
                        : selectedLanguage === "Tamil"
                          ? 'border-purple-300 bg-white hover:bg-purple-50'
                          : selectedLanguage === "Telugu"
                            ? 'border-green-300 bg-white hover:bg-green-50'
                            : selectedLanguage === "Kannada"
                              ? 'border-orange-300 bg-white hover:bg-orange-50'
                              : 'border-blue-300 bg-white hover:bg-blue-50'
                      }
                      dark:bg-n0`}
                    title={isListening ? "Stop voice recording" : `Start voice recording in ${selectedLanguage}`}
                  >
                    {isListening ? (
                      <div className="relative">
                        <div
                          className="absolute -inset-1 bg-red-100 rounded-full opacity-50"
                          style={{
                            animationName: 'pulse',
                            animationDuration: '1.5s',
                            animationIterationCount: 'infinite',
                            animationDirection: 'alternate'
                          }}
                        ></div>
                        <PiStop className="text-red-500 relative z-10" />
                        <span
                          className="absolute -top-1 -right-1 h-2 w-2 rounded-full bg-red-500"
                          style={{
                            animationName: 'ping',
                            animationDuration: '1.5s',
                            animationIterationCount: 'infinite'
                          }}
                        ></span>
                      </div>
                    ) : (
                      <PiMicrophone className={
                        selectedLanguage === "Tamil"
                          ? 'text-purple-600'
                          : selectedLanguage === "Telugu"
                            ? 'text-green-600'
                            : selectedLanguage === "Kannada"
                              ? 'text-orange-600'
                              : 'text-blue-600'
                      } />
                    )}
                  </button>
                ) : (
                  <button
                    type="button"
                    className="bg-white p-2.5 rounded-full flex justify-center items-center border border-gray-300 text-gray-400 cursor-not-allowed"
                    title="Speech recognition not supported in this browser"
                    disabled
                  >
                    <PiMicrophone />
                  </button>
                )}
              </div>

              {/* Submit button */}
              <button
                type="submit"
                className={`rounded-full flex justify-center items-center border text-white shadow-sm hover:shadow-md transition-all
                  ${selectedLanguage === "Tamil"
                    ? "px-4 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 border-purple-600"
                    : selectedLanguage === "Telugu"
                      ? "px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 border-green-600"
                      : selectedLanguage === "Kannada"
                        ? "px-4 py-2.5 bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 border-orange-600"
                        : "px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-blue-600"}
                  ${isLoading || isTranslating ? 'opacity-80 cursor-not-allowed' : ''}`}
                disabled={isLoading || isTranslating}
                title={
                  isLoading || isTranslating
                    ? isTranslating
                      ? selectedLanguage === "Tamil"
                        ? "மொழிபெயர்க்கிறது..."
                        : selectedLanguage === "Telugu"
                          ? "అనువదిస్తోంది..."
                          : selectedLanguage === "Kannada"
                            ? "ಅನುವಾದಿಸುತ್ತಿದೆ..."
                            : "Translating..."
                      : selectedLanguage === "Tamil"
                        ? "அனுப்புகிறது..."
                        : selectedLanguage === "Telugu"
                          ? "పంపుతోంది..."
                          : selectedLanguage === "Kannada"
                            ? "ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ..."
                            : "Sending..."
                    : selectedLanguage === "Tamil"
                      ? "கேள்வியை அனுப்பு"
                      : selectedLanguage === "Telugu"
                        ? "ప్రశ్నను పంపండి"
                        : selectedLanguage === "Kannada"
                          ? "ಪ್ರಶ್ನೆಯನ್ನು ಕಳುಹಿಸಿ"
                          : "Send question"
                }
              >
                {isLoading || isTranslating ? (
                  // Loading/Translating state with spinner
                  isTranslating ? (
                    selectedLanguage === "Tamil" ? (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        மொழிபெயர்க்கிறது... <PiSpinner className="animate-spin" />
                      </span>
                    ) : selectedLanguage === "Telugu" ? (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        అనువదిస్తోంది... <PiSpinner className="animate-spin" />
                      </span>
                    ) : selectedLanguage === "Kannada" ? (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        ಅನುವಾದಿಸುತ್ತಿದೆ... <PiSpinner className="animate-spin" />
                      </span>
                    ) : (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        Translating... <PiSpinner className="animate-spin" />
                      </span>
                    )
                  ) : (
                    selectedLanguage === "Tamil" ? (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        அனுப்புகிறது... <PiSpinner className="animate-spin" />
                      </span>
                    ) : selectedLanguage === "Telugu" ? (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        పంపుతోంది... <PiSpinner className="animate-spin" />
                      </span>
                    ) : selectedLanguage === "Kannada" ? (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        ಕಳುಹಿಸಲಾಗುತ್ತಿದೆ... <PiSpinner className="animate-spin" />
                      </span>
                    ) : (
                      <span className="text-sm font-medium flex items-center gap-1.5">
                        Sending... <PiSpinner className="animate-spin" />
                      </span>
                    )
                  )
                ) : (
                  // Normal state
                  selectedLanguage === "Tamil" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      அனுப்பு <PiArrowUp />
                    </span>
                  ) : selectedLanguage === "Telugu" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      పంపండి <PiArrowUp />
                    </span>
                  ) : selectedLanguage === "Kannada" ? (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      ಕಳುಹಿಸಿ <PiArrowUp />
                    </span>
                  ) : (
                    <span className="text-sm font-medium flex items-center gap-1.5">
                      Send <PiArrowUp />
                    </span>
                  )
                )}
              </button>
            </div>

            {/* Debug button - only visible in development */}
            {/* {process.env.NODE_ENV === 'development' && (
              <button
                type="button"
                onClick={logSpeechRecognitionState}
                className="text-xs px-2 py-1 bg-gray-100 border border-gray-300 rounded text-gray-600 hover:bg-gray-200"
                title="Debug speech recognition"
              >
                Debug
              </button>
            )} */}
          </div>
        </div>
      </form>

      {/* Add style for animation and z-index fixes */}
      <style jsx>{`
        @keyframes pulse {
          0% { height: 4px; }
          100% { height: 16px; }
        }

        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        /* Spinner animation */
        .animate-spin {
          animation: spin 1s linear infinite;
        }

        /* Ensure microphone elements are above other UI elements */
        .relative.z-50 {
          position: relative;
          z-index: 50;
        }

        /* Fix for overlapping elements */
        .absolute.z-50 {
          position: absolute;
          z-index: 50;
        }

        /* Ensure input is always clickable */
        input {
          position: relative;
          z-index: 30;
        }

        /* Style for editable transcript textarea */
        textarea {
          font-family: inherit;
          line-height: 1.5;
          transition: all 0.2s ease-in-out;
        }

        /* Animation for edit button */
        button:hover svg {
          transform: scale(1.1);
          transition: transform 0.2s ease-in-out;
        }

        /* Dropup animations */
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
          animation: fadeIn 0.2s ease-out forwards;
        }

        /* Scale in animation for horizontal borders */
        @keyframes scaleInHorizontal {
          from { transform: scaleX(0); }
          to { transform: scaleX(1); }
        }

        .animate-scaleInHorizontal {
          animation: scaleInHorizontal 0.4s ease-out forwards;
        }
      `}</style>
    </div>
  );
}

export default ChatBox;
