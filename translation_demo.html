<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fast Translation Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .input-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        textarea {
            height: 100px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 5px;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            border-left-color: #dc3545;
        }
        .success {
            color: #155724;
            background: #d4edda;
            border-left-color: #28a745;
        }
        .stats {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Fast Translation Demo</h1>
        <p>This demo shows the optimized translation with timeout and caching for faster responses.</p>
        
        <div class="input-group">
            <label for="text">Text to translate:</label>
            <textarea id="text" placeholder="Enter text to translate...">Hello, how are you? I hope you are doing well today.</textarea>
        </div>
        
        <div class="input-group">
            <label for="sourceLang">From:</label>
            <select id="sourceLang">
                <option value="en">English</option>
                <option value="ta">Tamil</option>
                <option value="te">Telugu</option>
                <option value="kn">Kannada</option>
            </select>
        </div>
        
        <div class="input-group">
            <label for="targetLang">To:</label>
            <select id="targetLang">
                <option value="te">Telugu</option>
                <option value="ta">Tamil</option>
                <option value="kn">Kannada</option>
                <option value="en">English</option>
            </select>
        </div>
        
        <button onclick="translateText()" id="translateBtn">🚀 Fast Translate</button>
        <button onclick="clearCache()" id="clearBtn">🗑️ Clear Cache</button>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Simple translation cache
        const translationCache = new Map();
        
        async function translateWithMyMemory(text, sourceLang, targetLang) {
            console.log(`🌐 Attempting direct MyMemory translation: ${sourceLang} -> ${targetLang}`);
            
            const url = "https://api.mymemory.translated.net/get";
            const params = new URLSearchParams({
                q: text,
                langpair: `${sourceLang}|${targetLang}`
            });

            try {
                const response = await fetch(`${url}?${params}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.responseStatus === 200) {
                        const translatedText = data.responseData?.translatedText;
                        if (translatedText && translatedText !== text) {
                            console.log(`✅ MyMemory translation successful`);
                            return translatedText;
                        }
                    }
                }
            } catch (error) {
                console.warn(`⚠️ MyMemory translation failed:`, error);
            }
            
            return null;
        }
        
        async function translateText() {
            const text = document.getElementById('text').value.trim();
            const sourceLang = document.getElementById('sourceLang').value;
            const targetLang = document.getElementById('targetLang').value;
            const resultDiv = document.getElementById('result');
            const translateBtn = document.getElementById('translateBtn');
            
            if (!text) {
                alert('Please enter text to translate');
                return;
            }
            
            if (sourceLang === targetLang) {
                resultDiv.innerHTML = '<div class="error">Source and target languages are the same!</div>';
                resultDiv.style.display = 'block';
                return;
            }
            
            // Check cache first
            const cacheKey = `${sourceLang}-${targetLang}-${text}`;
            const cachedTranslation = translationCache.get(cacheKey);
            if (cachedTranslation) {
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>💾 Cached Result (Instant):</strong><br>
                        ${cachedTranslation}
                        <div class="stats">Retrieved from cache instantly</div>
                    </div>
                `;
                resultDiv.style.display = 'block';
                return;
            }
            
            // Show loading
            translateBtn.disabled = true;
            translateBtn.textContent = '🔄 Translating...';
            resultDiv.innerHTML = '<div class="loading">🚀 Fast translating with timeout...</div>';
            resultDiv.style.display = 'block';
            
            const startTime = Date.now();
            
            try {
                // Create timeout promise
                const timeoutPromise = new Promise((_, reject) => {
                    setTimeout(() => reject(new Error('Translation timeout')), 5000);
                });
                
                // Translation promise
                const translationPromise = translateWithMyMemory(text, sourceLang, targetLang);
                
                // Race between translation and timeout
                const result = await Promise.race([translationPromise, timeoutPromise]);
                const duration = Date.now() - startTime;
                
                if (result && result !== text) {
                    // Cache the result
                    translationCache.set(cacheKey, result);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ Translation Result:</strong><br>
                            ${result}
                            <div class="stats">Completed in ${duration}ms | Cached for future use</div>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ Translation Failed:</strong><br>
                            No translation available or same as original text.
                            <div class="stats">Duration: ${duration}ms</div>
                        </div>
                    `;
                }
            } catch (error) {
                const duration = Date.now() - startTime;
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>⚠️ Translation Error:</strong><br>
                        ${error.message}
                        <div class="stats">Duration: ${duration}ms</div>
                    </div>
                `;
            } finally {
                translateBtn.disabled = false;
                translateBtn.textContent = '🚀 Fast Translate';
            }
        }
        
        function clearCache() {
            translationCache.clear();
            document.getElementById('result').innerHTML = '<div class="success">🗑️ Cache cleared successfully!</div>';
            document.getElementById('result').style.display = 'block';
        }
        
        // Add enter key support
        document.getElementById('text').addEventListener('keydown', function(e) {
            if (e.ctrlKey && e.key === 'Enter') {
                translateText();
            }
        });
    </script>
</body>
</html>
